# 若依单体系统

## 项目介绍

若依单体系统是基于 Spring Boot + MyBatis Plus + Spring Security + Redis 的单体应用架构。

## 技术栈

- **后端框架**: Spring Boot 2.7.2
- **安全框架**: Spring Security
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **ORM框架**: MyBatis Plus 3.5.1
- **数据库连接池**: Druid 1.2.11
- **动态数据源**: dynamic-datasource 3.5.1
- **验证码**: Kaptcha
- **接口文档**: Swagger 3.0
- **分页插件**: PageHelper

## 项目结构

```
ruoyi/
├── ruoyi-admin/          # 单体应用模块
│   ├── src/main/java/
│   │   └── com/ruoyi/
│   │       ├── RuoYiApplication.java    # 启动类
│   │       └── system/                  # 系统模块
│   │           ├── controller/          # 控制器
│   │           ├── service/             # 服务层
│   │           ├── mapper/              # 数据访问层
│   │           └── config/              # 配置类
│   └── src/main/resources/
│       ├── application.yml              # 主配置文件
│       ├── application-druid.yml        # 数据源配置
│       └── mybatis/                     # MyBatis配置
└── ruoyi-common/         # 公共模块
    ├── ruoyi-common-core/       # 核心模块
    ├── ruoyi-common-security/   # 安全模块
    ├── ruoyi-common-redis/      # 缓存模块
    ├── ruoyi-common-datasource/ # 数据源模块
    ├── ruoyi-common-log/        # 日志模块
    ├── ruoyi-common-swagger/    # 接口文档模块
    └── ruoyi-common-entity/     # 实体模块
```

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-repo/ruoyi.git
   cd ruoyi
   ```

2. **配置数据库**
   - 创建数据库 `ry-vue`
   - 执行 `sql/ry_20230706.sql` 脚本

3. **修改配置**
   - 修改 `ruoyi-admin/src/main/resources/application-druid.yml` 中的数据库连接信息
   - 修改 `ruoyi-admin/src/main/resources/application.yml` 中的Redis配置

4. **启动应用**
   ```bash
   mvn clean install
   cd ruoyi-admin
   mvn spring-boot:run
   ```

5. **访问应用**
   - 应用地址: http://localhost:8080
   - 接口文档: http://localhost:8080/swagger-ui.html
   - Druid监控: http://localhost:8080/druid

## 主要特性

- ✅ **基于 DispatcherServlet**: 使用传统的 Spring MVC 架构
- ✅ **统一认证**: 基于 JWT 的 Token 认证
- ✅ **权限控制**: 基于 RBAC 的权限管理
- ✅ **数据权限**: 支持数据范围权限控制
- ✅ **多数据源**: 支持动态数据源切换
- ✅ **代码生成**: 支持自动生成 CRUD 代码
- ✅ **操作日志**: 记录用户操作日志
- ✅ **数据字典**: 支持数据字典管理
- ✅ **参数配置**: 支持系统参数配置
- ✅ **定时任务**: 支持定时任务管理
- ✅ **文件上传**: 支持文件上传下载
- ✅ **Excel导入导出**: 支持 Excel 数据处理

## 架构说明

本项目采用单体架构，基于 Spring Boot + DispatcherServlet 实现：

- **表现层**: Spring MVC + DispatcherServlet
- **业务层**: Spring Boot + Spring Security
- **持久层**: MyBatis Plus + Druid
- **缓存层**: Redis
- **安全层**: Spring Security + JWT

## 开发指南

### 添加新功能

1. 在 `ruoyi-admin/src/main/java/com/ruoyi/system/` 下创建对应的包结构
2. 创建 Controller、Service、Mapper 类
3. 在 `ruoyi-admin/src/main/resources/mapper/` 下创建对应的 XML 文件
4. 配置路由和权限

### 代码生成

使用代码生成器可以快速生成 CRUD 代码：

1. 访问代码生成页面
2. 导入数据表
3. 配置生成参数
4. 生成代码并下载

## 部署说明

### 开发环境

```bash
mvn spring-boot:run
```

### 生产环境

```bash
mvn clean package -Dmaven.test.skip=true
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

## 许可证

本项目基于 MIT 许可证开源。

