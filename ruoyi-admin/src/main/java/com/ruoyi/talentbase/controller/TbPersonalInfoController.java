package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * 人才个人信息管理Controller
 * 
 * <AUTHOR>
 * @date 2023-07-01
 */
@Api(tags = "人才个人信息管理API")
@RestController
@RequestMapping("/talentbase/personal")
public class TbPersonalInfoController extends BaseController
{
    /**
     * 查询人才个人信息列表
     */
    @ApiOperation("查询人才个人信息列表")
    @GetMapping("/list")
    public TableDataInfo list()
    {
        // TODO: 实现人才信息列表查询逻辑
        return getDataTable(null);
    }

    /**
     * 获取人才个人信息详细信息
     */
    @ApiOperation("获取人才个人信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // TODO: 实现人才信息详情查询逻辑
        return success("查询成功");
    }

    /**
     * 新增人才个人信息
     */
    @ApiOperation("新增人才个人信息")
    @PostMapping
    public AjaxResult add(@RequestBody Object tbPersonalInfo)
    {
        // TODO: 实现人才信息新增逻辑
        return success("新增成功");
    }

    /**
     * 修改人才个人信息
     */
    @ApiOperation("修改人才个人信息")
    @PutMapping
    public AjaxResult edit(@RequestBody Object tbPersonalInfo)
    {
        // TODO: 实现人才信息修改逻辑
        return success("修改成功");
    }

    /**
     * 删除人才个人信息
     */
    @ApiOperation("删除人才个人信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // TODO: 实现人才信息删除逻辑
        return success("删除成功");
    }
}
