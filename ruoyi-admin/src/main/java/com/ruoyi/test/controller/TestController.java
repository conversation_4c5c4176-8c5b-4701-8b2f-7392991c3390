package com.ruoyi.test.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试Controller
 * 
 * <AUTHOR>
 * @date 2023-07-01
 */
@Api(tags = "测试API")
@RestController
@RequestMapping("/test")
public class TestController
{
    /**
     * 测试接口
     */
    @ApiOperation("测试接口")
    @GetMapping("/hello")
    public AjaxResult hello()
    {
        return AjaxResult.success("Hello, 模块整合成功!");
    }

    /**
     * 测试知识库模块
     */
    @ApiOperation("测试知识库模块")
    @GetMapping("/knowledgebase")
    public AjaxResult testKnowledgebase()
    {
        return AjaxResult.success("知识库模块整合成功!");
    }

    /**
     * 测试人才库模块
     */
    @ApiOperation("测试人才库模块")
    @GetMapping("/talentbase")
    public AjaxResult testTalentbase()
    {
        return AjaxResult.success("人才库模块整合成功!");
    }

    /**
     * 测试文件模块
     */
    @ApiOperation("测试文件模块")
    @GetMapping("/file")
    public AjaxResult testFile()
    {
        return AjaxResult.success("文件模块整合成功!");
    }

    /**
     * 测试代码生成模块
     */
    @ApiOperation("测试代码生成模块")
    @GetMapping("/gen")
    public AjaxResult testGen()
    {
        return AjaxResult.success("代码生成模块整合成功!");
    }
}
