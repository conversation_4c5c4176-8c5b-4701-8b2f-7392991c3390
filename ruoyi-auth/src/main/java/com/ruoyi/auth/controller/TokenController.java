package com.ruoyi.auth.controller;

import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.entity.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Api(tags = "登录权限认证API")
@Slf4j
@RestController
@RequestMapping("/auth")
public class TokenController {
    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenService tokenService;

    /**
     * PC登录
     */
    @ApiOperation(value = "后台登录接口")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        
        // 返回登录结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", "test_token");
        result.put("user", "test");
        
        return AjaxResult.success("登录成功", result);
    }
    
    /**
     * 验证码校验
     */
    private void validateCaptcha(String code, String uuid) {
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(uuid)) {
            throw new ServiceException("验证码不能为空");
        }
        
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisService.getCacheObject(verifyKey);
        redisService.deleteObject(verifyKey);
        
        if (captcha == null) {
            throw new ServiceException("验证码已过期");
        }
        
        if (!code.equalsIgnoreCase(captcha)) {
            throw new ServiceException("验证码错误");
        }
    }
}
