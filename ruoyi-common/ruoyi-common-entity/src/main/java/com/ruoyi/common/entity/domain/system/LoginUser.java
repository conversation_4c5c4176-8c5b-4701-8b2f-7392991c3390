package com.ruoyi.common.entity.domain.system;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysAppUser;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
public class LoginUser implements UserDetails {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 总的权限列表
     */
    private Set<String> permissions;

    /**
     * 平台级权限列表
     */
    private Map<String, Set<String>> systemPermissions;

    /**
     * 用户信息
     */
    private SysUser user;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 切换的公司id
     */
    private Long toggleCompanyId;

    /**
     * 子系统标识
     */
    private String subSystem;

    /**
     * 所持有的子系统
     */
    private List<String> systemList;

    public LoginUser() {
    }

    public LoginUser(SysUser user, Set<String> permissions) {
        this.user = user;
        if (!StringUtils.isNull(user)) {
            this.userId = user.getUserId();
            this.companyId = user.getCompanyId();
        }
        this.permissions = permissions;
    }

    public LoginUser(SysUser user) {
        this(user, new HashSet<>());
    }

    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getUserName();
    }

    public void setUser(SysUser user) {
        if (user == null) {
            this.companyId = null;
        } else {
            this.companyId = user.getCompanyId();
        }
        this.user = user;
    }

    /**
     * 判断调试人员身份
     *
     * @return
     */
    public boolean isDebugging() {
        return checkAdminRole("debugging");
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isEnabled() {
        return true;
    }

    public boolean isSuperAdmin() {
        return checkAdminRole("admin");
    }

    /**
     * 超级管理员或运维管理员
     * @return
     */
    public boolean isSuperAdminAndOpsAdmin(){
        return checkAdminRole("admin") || checkAdminRole("opsAdmin");
    }

    private boolean checkAdminRole(String roleKey) {
        if (this.getUser() instanceof SysAdminUser && !StringUtils.isNull(roleKey)) {
            SysAdminUser adminUser = (SysAdminUser) this.getUser();
            List<SysRole> roleList = adminUser.getRoles();
            if (roleList != null) {
                for (SysRole sysRole : roleList) {
                    if (roleKey.equals(sysRole.getRoleKey())) {
                        //运维管理员
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public boolean isAdmin() {
        return isSuperAdmin();
    }

    public boolean isAppUser() {
        return this.getUser() instanceof SysAppUser;
    }

    public <T extends SysUser> T getCurrentUser(Class<T> t) {
        if (this.user.getClass().equals(t)) {
            return (T) this.user;
        }
        return null;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    public void setAllPermissions(Map<String, Set<String>> map) {
        for (Set<String> value : map.values()) {
            if (this.permissions != null) {
                Set<String> perm = this.permissions;
                perm.addAll(value);
                this.permissions = perm;
            } else {
                this.permissions = new HashSet<>();
                this.permissions.addAll(value);
            }
        }
    }
}
