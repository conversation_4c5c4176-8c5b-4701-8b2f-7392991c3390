package com.ruoyi.common.log.service;

import com.ruoyi.common.entity.domain.SysOperLog;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 异步调用日志服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncLogService
{

    /**
     * 保存系统日志记录
     */
    @Async
    public void saveSysLog(SysOperLog sysOperLog)
    {
        //TODO: 原feign接口实现，需修改
//        remoteLogService.saveLog(sysOperLog, SecurityConstants.INNER);
    }
}
