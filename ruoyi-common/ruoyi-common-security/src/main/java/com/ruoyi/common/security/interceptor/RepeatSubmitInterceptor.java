package com.ruoyi.common.security.interceptor;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.annotation.RepeatSubmit;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 防止重复提交拦截器
 * 第二步
 *
 * <AUTHOR>
 */
@Component
public abstract class RepeatSubmitInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //instanceof是Java的一个保留关键字，左边是对象，右边是类，返回类型是Boolean类型。
        //它的具体作用是测试左边的对象是否是右边类或者该类的子类创建的实例对象
        if (handler instanceof HandlerMethod) {
            //处理器方法
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            //获取处理方法中的注册方法
            Method method = handlerMethod.getMethod();
            //从注册方法中获取到自定义注解
            RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
            //判断注解是否存在
            if (annotation != null) {    //调用抽象方法，判断此注解是否以存在
                if (this.isRepeatSubmit(request, annotation)) {
                    //重复提交
                    AjaxResult ajaxResult = AjaxResult.error(annotation.message());
                    //将字符串渲染到客户端
                    ServletUtils.renderString(response, JSON.toJSONString(ajaxResult));
                    return false;
                }
            }
            return true;
        } else {
            return true;
        }
    }

    /**
     * 验证是否重复提交由子类实现具体的防重复提交的规则
     *
     * @param request
     * @return
     * @throws Exception
     */
    public abstract boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation);

}