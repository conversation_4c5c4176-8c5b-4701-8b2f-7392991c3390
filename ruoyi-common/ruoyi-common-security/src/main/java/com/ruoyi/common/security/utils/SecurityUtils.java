package com.ruoyi.common.security.utils;


import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.sign.RsaUtils;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SecurityUtils {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 企业及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";
    /**
     * 用户ID
     **/
    public static Long getUserId() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取公司ID
     **/
    public static Long getCompanyId() {
        try {
            return getLoginUser().getCompanyId();
        } catch (Exception e) {
            throw new ServiceException("获取部门ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取公司ID，除管理员、运维管理员
     *
     * @return
     */
    public static Long getCompanyIdExcludeAdmin() {
        Long companyId = null;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        boolean admin = SecurityUtils.isAdministrator();
        if (!admin) {
            companyId = loginUser.getCompanyId();
        }
        return companyId;
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUser().getUserName();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户昵称
     **/
    public static String getNickname() {
        try {
            return getLoginUser().getUser().getNickName();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 生成部门操作人
     * @return
     */
    public static String creDeptRelationBy(){
        return SecurityUtils.getNickname()+"("+SecurityUtils.getUsername()+")";
    }

    /**
     * 获取上下文用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        } catch (Exception e) {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取当前登录用户
     **/
    public static <T extends SysUser> T getUser(Class<T> t) {
        LoginUser loginUser = getLoginUser();
        if (loginUser != null) {
            return loginUser.getCurrentUser(t);
        }
        return null;
    }

    public static SysUser getUser() {
        LoginUser loginUser = getLoginUser();
        if (loginUser != null) {
            return loginUser.getUser();
        }
        return null;
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 是否为超级管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 是否为管理员身份
     *
     * @param
     * @return 结果
     */
    public static boolean isAdministrator() {
        LoginUser loginUser = getLoginUser();
        ArrayList<String> roles = new ArrayList<>(loginUser.getRoles());
        if (roles == null || roles.size() == 0) {
            return false;
        }
        return roles.get(0).equals("admin");
    }

    /**
     * 获取请求token
     */
    public static String getToken() {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request) {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        try {
            return passwordEncoder.matches(RsaUtils.decryptByPrivateKey(rawPassword), encodedPassword);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取切换到的公司id
     */
    public static Long getToggleCompanyId() {
        LoginUser loginUser = getLoginUser();
        if (loginUser.getToggleCompanyId() != null) {
            return loginUser.getToggleCompanyId();
        }
        return null;
    }


    /**
     * 返回当前用户企业ID（含切换逻辑）
     * @return
     */
    public static Long getHandleCompanyId(){
        // 获取当前登录用户
        LoginUser loginUser = getLoginUser();
        // 当前用户所属企业ID
        Long companyId = loginUser.getCompanyId();
        // 管理员或运维管理员切换到的企业ID
        Long toggleCompanyId = loginUser.getToggleCompanyId();
        if(toggleCompanyId == null){
            return companyId;
        } else{
            return toggleCompanyId;
        }
    }

    /**
     * 获取当前用户的企业ID
     * 后台管理平台切换至企业平台后，返回所切换企业ID
     * 管理员和运维管理员为平台级角色，可切换至企业平台
     * 管理员和运维管理员在平台侧可以查看全部企业数据，在企业侧查看目标企业数据(数据权限与企业侧同步）
     * 非管理员、运维管理员角色，根据DataScope权限展示数据
     * @return
     */
    public static Long getCurrentCompanyId() {
        // 获取当前登录用户
        LoginUser loginUser = getLoginUser();
        // 当前用户所属企业ID
        Long companyId = loginUser.getCompanyId();
        // 管理员或运维管理员切换到的企业ID
        Long toggleCompanyId = loginUser.getToggleCompanyId();

        if(Constants.SUB_SYSTEM_WEB.equals(loginUser.getSubSystem())){
            if(toggleCompanyId != null && toggleCompanyId != 100L){
                return toggleCompanyId;
            }else{
                return null;
            }
        }else{
            if(toggleCompanyId != null && toggleCompanyId != 100L){
                return toggleCompanyId;
            }else{
                return companyId;
            }
        }
    }

    /**
     * 视频地址加密
     * 截取规则: 文件名 取后缀名往前30位加密
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static String urlEncryption(String url) throws Exception {
        String isUrl = null;
        // 路径为空直接返回
        if (url == null){
            return null;
        }
        String[] splitUrl = url.split("/");
        //判断路径的合法性  不合法路径直接扔出去不做处理
        try {
            if (StringUtils.isNotEmpty(url) && StringUtils.isNotEmpty(splitUrl) && splitUrl.length > 1) {
                String fileName = url.substring(url.lastIndexOf('/') + 1, url.lastIndexOf('.'));
                isUrl = fileName.length() > 30 ? fileName.substring(fileName.length() - 30) : fileName;
            }
            if (StringUtils.isNotEmpty(isUrl)) {
                String encryptByPublicKey = RsaUtils.encryptByPublicKey(isUrl);
                return url.replace(isUrl, encryptByPublicKey);
            }
        }catch (Exception e){
            // 加密失败直接返回本来的数据
            log.error("视频地址加密失败,请联系管理员");
            return url;
        }

        return null;
    }

    /**
     * 获取角色ids
     * @return
     */
    public static List<Long> getRoleIds() {
        List<Long> ids = getUser().getRoleIds();
        return ids;
    }

    /**
     * 获取企业平台id
     * @return
     */
    public static Long getPlatformId(){
        try {
            RedisService redisService = SpringUtils.getBean(RedisService.class);
            String defIdStr = redisService.getCacheObject(Constants.DEFAULT_PLATFORM_ID).toString();
            // 获取默认平台Id
            Long defId = Long.parseLong(defIdStr);
            // 判断是否登录
            LoginUser loginUser = getLoginUser();
            if (loginUser==null){
                return defId;
            }
            // 获取企业Id
            Long companyId = getCurrentCompanyId();
            if (companyId!=null){
                String key = Constants.COMPANY_PLATFORM_KEY + companyId;
                if (redisService.hasKey(key)){
                    String idStr = redisService.getCacheObject(key).toString();
                    return Long.parseLong(idStr);
                }
            }
            return defId;
        }catch (Exception e){
            throw new ServiceException("获取所属平台信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

}
