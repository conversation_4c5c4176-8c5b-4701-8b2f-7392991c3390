package com.ruoyi.common.security.utils;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.common.security.service.TokenService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class UserRedisUtils {

    public static void updateUserRedisInfo(SysUser sysUser) {
        TokenService tokenService = SpringUtils.getBean(TokenService.class);
        List<LoginUser> redisUsers = getRedisUser(sysUser.getUserId(), sysUser.getClass());
        for (LoginUser loginUser : redisUsers) {
            if (loginUser != null) {
                loginUser.setUser(sysUser);
                tokenService.refreshToken(loginUser);
            }
        }

    }

    /**
     * 更新登录用户token信息
     * @param user
     */
    public static void resUserRedisInfo(LoginUser user) {
        TokenService tokenService = SpringUtils.getBean(TokenService.class);
        List<LoginUser> redisUsers = getRedisUser(user.getUserId(), SysAdminUser.class);
        for (LoginUser loginUser : redisUsers) {
            if (loginUser != null) {
                loginUser.setUser(user.getUser());
                loginUser.setPermissions(user.getPermissions());
                tokenService.refreshToken(loginUser);
            }
        }

    }

    private static List<LoginUser> getRedisUser(Long userId, Class userClass) {
        List<LoginUser> loginUsers = new ArrayList<>();
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        for (String key : keys) {
            LoginUser loginUser = redisCache.getCacheObject(key);
            if (loginUser != null) {
                if (loginUser.getUserId() != null && loginUser.getUserId().equals(userId)) {
                    if (loginUser.getUser() != null && userClass == loginUser.getUser().getClass()) {
                        loginUsers.add(loginUser);
                    }
                }
            }
        }
        return loginUsers;
    }

    public static void removeRedisUser(Long userId, Class userClass) {
        List<LoginUser> redisUsers = getRedisUser(userId, userClass);
        for (LoginUser loginUser : redisUsers) {
            if (loginUser != null) {
                RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                redisCache.deleteObject(Constants.LOGIN_TOKEN_KEY + loginUser.getToken());
            }
        }
    }

    private static Boolean deleteRedisUser(Long userId, Class userClass) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        for (String key : keys) {
            LoginUser loginUser = redisCache.getCacheObject(key);
            if (loginUser != null) {
                if (loginUser.getUserId() != null && loginUser.getUserId().equals(userId)) {
                    if (userClass == loginUser.getUser().getClass()) {
                        return redisCache.deleteObject(key);
                    }
                }
            }
        }
        return false;
    }

//    public static AjaxResult deleteUserRedis(SysUser user) {
//        if (!user.isEnabled()) {
//            if (!UserRedisUtils.deleteRedisUser(user.getUserId(), user.getClass())) {
//                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//                return AjaxResult.error("更新缓存失败");
//            }
//        }
//        return AjaxResult.success();
//    }

    public static void deleteUserRedisWhenDisabled(SysUser user) {
        if (!user.isEnabled()) {
            UserRedisUtils.deleteRedisUser(user.getUserId(), user.getClass());
        }
    }

    /**
     * 删除用户redis token，使其重新登录以更新角色的数据权限
     * @param user
     */
    public static void deleteUserRedis(SysUser user){
        UserRedisUtils.deleteRedisUser(user.getUserId(), user.getClass());
    }
}
