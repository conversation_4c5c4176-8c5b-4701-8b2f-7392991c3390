package com.ruoyi.file.controller;

import com.ruoyi.common.core.utils.file.FileUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysFiles;
import com.ruoyi.file.domain.UploadResult;
import com.ruoyi.file.service.ISysFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
public class SysFileController {
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 文件上传请求
     */
    @PostMapping("/uploadFile")
    public AjaxResult upload(MultipartFile file, @RequestParam(value = "isDownload", required = false) String isDownload) {
        try {
            String fileName = file.getOriginalFilename();
            String fileType = "";
            // Get file extension
            if (fileName != null && fileName.contains(".")) {
                fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                fileName = fileName.substring(0, fileName.lastIndexOf("."));
            }
            SysFiles sysFile = new SysFiles();
            sysFile.setName(fileName);
            sysFile.setFileSize(file.getSize());
            sysFile.setFileType(fileType);
            sysFile.setIsDownload(isDownload != null ? isDownload : "0"); // 设置是否允许下载，默认为允许

            // 保存到本地
            UploadResult uploadResult = sysFileService.uploadFile(file);
            sysFile.setUrl(uploadResult.getUrl());
            return AjaxResult.success(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 上传文件列表
     */
    @PostMapping(value = "/uploadFileList", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult uploadFileList(@RequestPart("files") MultipartFile[] files) {
        try {
            List<SysFiles> sysFiles = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String fileType = "";
                // Get file extension
                if (fileName != null && fileName.contains(".")) {
                    fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                    fileName = fileName.substring(0, fileName.lastIndexOf("."));
                }
                SysFiles sysFile = new SysFiles();
                sysFile.setName(fileName);
                sysFile.setFileSize(file.getSize());
                sysFile.setFileType(fileType);
                sysFile.setIsDownload("0"); // 默认允许下载
                
                // 保存到本地
                UploadResult uploadResult = sysFileService.uploadFile(file);
                sysFile.setUrl(uploadResult.getUrl());
                sysFiles.add(sysFile);
            }
            
            return AjaxResult.success(sysFiles);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 下载
     */
    @PostMapping("downloadFile")
    public void downloadFile(HttpServletResponse response, String fileUrl) throws Exception {
        String name = FileUtils.getName(fileUrl);
        sysFileService.downloadFile(name, response);
    }

    /**
     * 删除文件
     */
    @GetMapping("remove")
    public AjaxResult remove(String fileUrl) {
        return AjaxResult.success(sysFileService.deleteFile(fileUrl));
    }

    /**
     * 内部调用删除文件
     */
    @GetMapping("/deleteFile")
    public int deleteFile(@RequestParam("fileUrl") String fileUrl) {
        return sysFileService.deleteFile(fileUrl);
    }
}