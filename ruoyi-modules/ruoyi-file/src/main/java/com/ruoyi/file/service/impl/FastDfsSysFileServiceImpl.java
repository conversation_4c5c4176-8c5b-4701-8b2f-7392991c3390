package com.ruoyi.file.service.impl;

import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.file.domain.UploadResult;
import com.ruoyi.file.service.ISysFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * FastDFS 文件存储
 * 
 * <AUTHOR>
 */
@Service
public class FastDfsSysFileServiceImpl implements ISysFileService
{
    /**
     * 域名或本机访问地址
     */
    @Value("${fdfs.domain}")
    public String domain;

    @Autowired
    private FastFileStorageClient storageClient;

    /**
     * FastDfs文件上传接口
     * 
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public UploadResult uploadFile(MultipartFile file) throws Exception
    {
        StorePath storePath = storageClient.uploadFile(file.getInputStream(), file.getSize(),
                FileTypeUtils.getExtension(file), null);
        UploadResult uploadResult = new UploadResult();
        uploadResult.setUrl(domain + "/" + storePath.getFullPath());
        return uploadResult;
    }

    @Override
    public void downloadFile(String fileName, HttpServletResponse response) throws Exception {

    }

    @Override
    public int deleteFile(String fileUrl) {
        return 0;
    }
}
