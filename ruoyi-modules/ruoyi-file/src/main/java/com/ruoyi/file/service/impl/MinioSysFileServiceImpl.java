package com.ruoyi.file.service.impl;

import com.ruoyi.common.core.utils.file.FileUtils;
import com.ruoyi.file.config.MinioConfig;
import com.ruoyi.file.domain.UploadResult;
import com.ruoyi.file.service.ISysFileService;
import io.minio.*;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Minio 文件存储
 *
 * <AUTHOR>
 */
@Primary
@Service
public class MinioSysFileServiceImpl implements ISysFileService {
    private static final Logger log = LoggerFactory.getLogger(MinioSysFileServiceImpl.class);

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient client;

    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public UploadResult uploadFile(MultipartFile file) throws Exception {
        return upload(file);
    }

    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    public UploadResult upload(MultipartFile file) throws Exception {
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String uuid = java.util.UUID.randomUUID().toString().replace("-", "");
        String storagePath = uuid + extension;
        String uploadId = generateUploadId(); // 生成上传ID
        
        // 如果文件大小超过5MB，使用分片上传
        if (file.getSize() > 5 * 1024 * 1024) {
            uploadFileInChunks(minioConfig.getBucketName(), storagePath, file, uploadId);
        } else {
            // 小文件直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(storagePath)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            client.putObject(args);
        }
        
        // 构建返回结果
        UploadResult result = new UploadResult();
        result.setUrl(minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + storagePath);
        result.setUploadId(uploadId);
        result.setFileName(originalFilename);
        result.setTotalSize(file.getSize());
        result.setFileType(file.getContentType());
        
        return result;
    }

    /**
     * 获取已上传的分片列表
     * @param uploadId 上传ID
     * @return 已上传的分片列表
     */
    private List<String> getExistingParts(String uploadId) throws Exception {
        List<String> existingParts = new ArrayList<>();
        try {
            // 列出所有以 uploadId 开头的对象
            Iterable<Result<Item>> results = client.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .prefix(uploadId + "_part_")
                    .build()
            );
            
            // 遍历结果，获取分片名称
            for (Result<Item> result : results) {
                Item item = result.get();
                existingParts.add(item.objectName());
            }
        } catch (Exception e) {
            log.error("获取已上传分片列表失败", e);
            throw new RuntimeException("获取已上传分片列表失败", e);
        }
        return existingParts;
    }

    /**
     * 分片上传
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param file 上传的文件
     */
    public void uploadFileInChunks(String bucketName, String objectName, MultipartFile file, String uploadId) throws Exception {
        log.info("开始分片上传，bucket: {}, object: {}, uploadId: {}", bucketName, objectName, uploadId);
        
        // 检查是否存在未完成的上传
        List<String> existingParts = new ArrayList<>();
        if (uploadId != null) {
            existingParts = getExistingParts(uploadId);
            log.info("找到已上传的分片数量: {}", existingParts.size());
        }

        // 创建桶（Bucket），如果桶不存在的话
        BucketExistsArgs build = BucketExistsArgs.builder().bucket(bucketName).build();
        if (!client.bucketExists(build)) {
            client.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            log.info("创建新的存储桶: {}", bucketName);
        }

        // 文件大小和分片大小
        InputStream inputStream = file.getInputStream();
        long fileSize = file.getSize();
        // 设置最小分片大小为5MB
        long partSize = 5 * 1024 * 1024;
        // 如果文件大小小于50MB，直接上传不分片
        if (fileSize <= 50 * 1024 * 1024) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, fileSize, -1)
                    .contentType(file.getContentType())
                    .build();
            client.putObject(args);
            return;
        }
        // 对于大文件，计算合适的分片数量（最多100个分片）
        int maxParts = 100;
        partSize = Math.max(partSize, (fileSize + maxParts - 1) / maxParts);
        int partNumber = 1;

        // 创建临时目录存储分片
        List<String> partNames = new ArrayList<>();
        byte[] buffer = new byte[(int) partSize];
        int bytesRead;

        try {
            // 分片上传
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                String partObjectName = objectName + "_part_" + partNumber;
                
                // 检查分片是否已上传
                if (!existingParts.contains(partObjectName)) {
                    try {
                        client.putObject(
                                PutObjectArgs.builder()
                                        .bucket(bucketName)
                                        .object(partObjectName)
                                        .stream(new ByteArrayInputStream(buffer, 0, bytesRead), bytesRead, -1)
                                        .build()
                        );
                        partNames.add(partObjectName);
                        log.debug("上传分片成功: {}", partObjectName);
                    } catch (Exception e) {
                        log.error("上传分片失败: {}", partObjectName, e);
                        // 清理已上传的分片
                        for (String uploadedPart : partNames) {
                            try {
                                client.removeObject(
                                        RemoveObjectArgs.builder()
                                                .bucket(bucketName)
                                                .object(uploadedPart)
                                                .build()
                                );
                                log.debug("清理分片: {}", uploadedPart);
                            } catch (Exception ex) {
                                log.error("清理分片失败: {}", uploadedPart, ex);
                            }
                        }
                        throw new RuntimeException("分片上传失败", e);
                    }
                } else {
                    log.debug("分片已存在，跳过上传: {}", partObjectName);
                    partNames.add(partObjectName);
                }
                partNumber++;
            }

            // 合并分片
            List<ComposeSource> sources = new ArrayList<>();
            for (String partName : partNames) {
                sources.add(ComposeSource.builder()
                        .bucket(bucketName)
                        .object(partName)
                        .build());
            }

            log.info("开始合并分片，总数: {}", sources.size());
            // 执行合并
            client.composeObject(ComposeObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .sources(sources)
                    .build());
            log.info("分片合并完成");

            // 删除分片
            for (String partName : partNames) {
                try {
                    client.removeObject(RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(partName)
                            .build());
                    log.debug("删除分片: {}", partName);
                } catch (Exception e) {
                    log.error("删除分片失败: {}", partName, e);
                }
            }
        } finally {
            inputStream.close();
        }
    }

    /**
     * 从minio下载文件
     */
    @Override
    public void downloadFile(String fileName, HttpServletResponse response) throws Exception {
        // 创建输入流
        InputStream is = null;
        try {
            StatObjectResponse statObjectResponse = client.statObject(StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(fileName).build());
            // 响应 设置内容类型
            response.setContentType(statObjectResponse.contentType());
            // 响应 设置编码格式
            response.setCharacterEncoding("UTF-8");
            // 响应 设置头文件
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 输入流
            is = client.getObject(GetObjectArgs.builder().bucket(minioConfig.getBucketName()).object(fileName).build());
            // 将字节从输入流复制到输出流
            IOUtils.copy(is, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("下载文件异常", e);
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 删除文件
     *
     * @param fileUrl
     * @return
     */
    @Override
    public int deleteFile(String fileUrl) {
        try {
            String name = FileUtils.getName(fileUrl);
            client.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(name)
                            .build()
            );
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 删除minio中的bucket
     *
     * @throws Exception
     */
    public void removeBucket(String bucketName) throws Exception {
        boolean isExist = client.bucketExists(
                BucketExistsArgs
                        .builder()
                        .bucket(bucketName)
                        .build()
        );
        if (isExist) {
            //桶不空，删不掉，所以清桶的objects
            Iterable<Result<Item>> iterable = client.listObjects(
                    ListObjectsArgs
                            .builder()
                            .bucket(bucketName)
                            .build()
            );
            for (Result<Item> o : iterable) {
                System.out.println("当前objectname---->>>>>>>" + o.get().objectName());
                client.removeObject(
                        RemoveObjectArgs.builder()
                                .bucket(bucketName)
                                .object(o.get().objectName())
                                .versionId(o.get().versionId())
                                .build());
                System.out.println("清理---->>>>>>>" + o.get().objectName());
            }
            System.out.println("清理" + bucketName + "下的object完毕");
            client.removeBucket(
                    RemoveBucketArgs
                            .builder()
                            .bucket(bucketName)
                            .build()
            );
            if (!client.bucketExists(
                    BucketExistsArgs.
                            builder()
                            .bucket(bucketName)
                            .build()
            )) {
                System.out.println("删除" + bucketName + "完毕，刷新minio的web页面");
            }
        } else {
            System.out.println("没有这个bucket，无需操作");
        }
    }

    /**
     * 查询bucket列表
     */
    public void testListBuckets() throws Exception {
        List<Bucket> bucketList = client.listBuckets();
        for (Bucket bucket : bucketList) {
            System.out.println(bucket.name());
        }
    }


    /**
     * 新建桶
     *
     * @throws Exception
     */
    public boolean createBucket(String bucketName) throws Exception {
        boolean isExist = client.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (isExist) {
            System.out.println(bucketName + "已经存在！");
        } else {
            client.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            System.out.println("创建了一个名字是" + bucketName + "的bucket");
        }
        return isExist;
    }

    private String generateUploadId() {
        // Implementation of generateUploadId method
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
}
