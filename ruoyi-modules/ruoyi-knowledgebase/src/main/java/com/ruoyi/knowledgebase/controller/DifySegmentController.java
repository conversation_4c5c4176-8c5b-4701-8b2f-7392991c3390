package com.ruoyi.knowledgebase.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.DifySegment;
import com.ruoyi.common.core.domain.DifySegmentRequest;
import com.ruoyi.common.core.domain.DifySegmentResponse;
import com.ruoyi.common.core.utils.DifyApiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Dify分段管理Controller
 */
@RestController
@RequestMapping("/dify/segment")
public class DifySegmentController {

    @Autowired
    private DifyApiUtil difyApiUtil;

    /**
     * 新增文档分段
     */
    @PostMapping("/{datasetId}/documents/{documentId}/segments")
    public R<DifySegmentResponse> addSegments(@PathVariable("datasetId") String datasetId,
                                @PathVariable("documentId") String documentId,
                                @RequestBody Map<String, List<DifySegmentRequest>> request) {
        List<DifySegmentRequest> segments = request.get("segments");
        String result = difyApiUtil.addSegments(datasetId, documentId, convertToMapList(segments));
        DifySegmentResponse response = com.alibaba.fastjson.JSON.parseObject(result, DifySegmentResponse.class);
        return R.ok(response);
    }

    /**
     * 查询文档分段
     */
    @GetMapping("/{datasetId}/documents/{documentId}/segments")
    public R<DifySegmentResponse> getSegments(@PathVariable("datasetId") String datasetId,
                                @PathVariable("documentId") String documentId,
                                @RequestParam(required = false) String keyword,
                                @RequestParam(required = false) String status,
                                @RequestParam(required = false) Integer page,
                                @RequestParam(required = false) Integer limit) {
        String result = difyApiUtil.getSegments(datasetId, documentId, keyword, status, page, limit);
        DifySegmentResponse response = com.alibaba.fastjson.JSON.parseObject(result, DifySegmentResponse.class);
        return R.ok(response);
    }

    /**
     * 删除文档分段
     */
    @DeleteMapping("/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public R<String> deleteSegment(@PathVariable("datasetId") String datasetId,
                                  @PathVariable("documentId") String documentId,
                                  @PathVariable("segmentId") String segmentId) {
        String result = difyApiUtil.deleteSegment(datasetId, documentId, segmentId);
        return R.ok(result);
    }

    /**
     * 更新文档分段
     */
    @PostMapping("/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public R<DifySegment> updateSegment(@PathVariable("datasetId") String datasetId,
                                  @PathVariable("documentId") String documentId,
                                  @PathVariable("segmentId") String segmentId,
                                  @RequestBody Map<String, DifySegmentRequest> request) {
        DifySegmentRequest segment = request.get("segment");
        String result = difyApiUtil.updateSegment(datasetId, documentId, segmentId, convertToMap(segment));
        DifySegment response = com.alibaba.fastjson.JSON.parseObject(result, DifySegment.class);
        return R.ok(response);
    }

    /**
     * 将DifySegmentRequest列表转换为Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<DifySegmentRequest> segments) {
        return segments.stream()
                .map(this::convertToMap)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将DifySegmentRequest转换为Map
     */
    private Map<String, Object> convertToMap(DifySegmentRequest segment) {
        return com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(segment), Map.class);
    }
} 