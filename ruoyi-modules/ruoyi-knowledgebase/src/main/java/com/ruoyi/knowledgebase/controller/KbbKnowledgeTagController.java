package com.ruoyi.knowledgebase.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeTag;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeTagService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库标签关联Controller
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/tag")
public class KbbKnowledgeTagController extends BaseController
{
    @Autowired
    private IKbbKnowledgeTagService kbbKnowledgeTagService;

    /**
     * 查询知识库标签关联列表
     */
    @RequiresPermissions("knowledgebase:tag:list")
    @GetMapping("/list")
    public TableDataInfo list(KbbKnowledgeTag kbbKnowledgeTag)
    {
        startPage();
        List<KbbKnowledgeTag> list = kbbKnowledgeTagService.selectKbbKnowledgeTagList(kbbKnowledgeTag);
        return getDataTable(list);
    }

    /**
     * 导出知识库标签关联列表
     */
    @RequiresPermissions("knowledgebase:tag:export")
    @Log(title = "知识库标签关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbKnowledgeTag kbbKnowledgeTag)
    {
        List<KbbKnowledgeTag> list = kbbKnowledgeTagService.selectKbbKnowledgeTagList(kbbKnowledgeTag);
        ExcelUtil<KbbKnowledgeTag> util = new ExcelUtil<KbbKnowledgeTag>(KbbKnowledgeTag.class);
        util.exportExcel(response, list, "知识库标签关联数据");
    }

    /**
     * 获取知识库标签关联详细信息
     */
    @RequiresPermissions("knowledgebase:tag:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(kbbKnowledgeTagService.selectKbbKnowledgeTagById(id));
    }

    /**
     * 新增知识库标签关联
     */
    @RequiresPermissions("knowledgebase:tag:add")
    @Log(title = "知识库标签关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbKnowledgeTag kbbKnowledgeTag)
    {
        return toAjax(kbbKnowledgeTagService.insertKbbKnowledgeTag(kbbKnowledgeTag));
    }

    /**
     * 修改知识库标签关联
     */
    @RequiresPermissions("knowledgebase:tag:edit")
    @Log(title = "知识库标签关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbKnowledgeTag kbbKnowledgeTag)
    {
        return toAjax(kbbKnowledgeTagService.updateKbbKnowledgeTag(kbbKnowledgeTag));
    }

    /**
     * 删除知识库标签关联
     */
    @RequiresPermissions("knowledgebase:tag:remove")
    @Log(title = "知识库标签关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kbbKnowledgeTagService.deleteKbbKnowledgeTagByIds(ids));
    }
}
