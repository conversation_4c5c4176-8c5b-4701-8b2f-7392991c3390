package com.ruoyi.knowledgebase.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.knowledgebase.domain.KbbTag;
import com.ruoyi.knowledgebase.service.IKbbTagService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.core.exception.ServiceException;

/**
 * 标签Controller
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/tags")
public class KbbTagController extends BaseController
{
    @Autowired
    private IKbbTagService kbbTagService;

    /**
     * 查询标签列表
     */
    @RequiresPermissions("knowledgebase:tag:list")
    @GetMapping("/list")
    public TableDataInfo list(KbbTag kbbTag)
    {
        startPage();
        List<KbbTag> list = kbbTagService.selectKbbTagList(kbbTag);
        return getDataTable(list);
    }

    /**
     * 导出标签列表
     */
    @RequiresPermissions("knowledgebase:tag:export")
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbTag kbbTag)
    {
        List<KbbTag> list = kbbTagService.selectKbbTagList(kbbTag);
        ExcelUtil<KbbTag> util = new ExcelUtil<KbbTag>(KbbTag.class);
        util.exportExcel(response, list, "标签数据");
    }

    /**
     * 获取标签详细信息
     */
    @RequiresPermissions("knowledgebase:tag:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(kbbTagService.selectKbbTagById(id));
    }

    /**
     * 新增标签
     */
    @RequiresPermissions("knowledgebase:tag:add")
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbTag kbbTag)
    {
        try {
            KbbTag newTag = kbbTagService.insertKbbTag(kbbTag);
            return AjaxResult.success(newTag);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改标签
     */
    @RequiresPermissions("knowledgebase:tag:edit")
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbTag kbbTag)
    {
        try {
            return toAjax(kbbTagService.updateKbbTag(kbbTag));
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除标签
     */
    @RequiresPermissions("knowledgebase:tag:remove")
    @Log(title = "标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kbbTagService.deleteKbbTagByIds(ids));
    }
}
