package com.ruoyi.knowledgebase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 知识库文件对象 kbb_file
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
public class KbbFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文件ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long kbId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件存储地址 */
    @Excel(name = "文件存储地址")
    private String fileUrl;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 文件大小(字节) */
    @Excel(name = "文件大小(字节)")
    private Long fileSize;

    /** dify文档ID */
    @Excel(name = "dify文档ID")
    private String documentId;

    /** 分段数量 */
    @Excel(name = "分段数量")
    private Long segmentCount;

    /** 状态（0-解析中 1-解析完成 2-解析失败） */
    @Excel(name = "状态", readConverterExp = "状态（0-未处理 1-处理中 2-解析失败 3-处理失败）")
    private String status;

    /** 是否可用（0-可用 1-不可用） */
    @Excel(name = "是否可用", readConverterExp = "0=-可用,1=-不可用")
    private String isAvailable;

    /** 是否允许下载（0-允许，1-不允许） */
    @Excel(name = "是否允许下载", readConverterExp = "0=-允许,1=-不允许")
    private String isDownload;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date uploadTime;
}
