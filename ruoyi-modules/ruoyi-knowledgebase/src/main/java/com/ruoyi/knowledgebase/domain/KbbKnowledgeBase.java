package com.ruoyi.knowledgebase.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 知识库对象 kbb_knowledge_base
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */

@Data
public class KbbKnowledgeBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 知识库ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    private String kbName;

    /** 知识库名称拼音 */
    @Excel(name = "知识库名称拼音")
    private String pinyin;

    /** 排序 */
    @Excel(name = "排序")
    private Integer kbSort;

    /** 文件数量 */
    private Integer fileCount;

    /** 指派人数 */
    private Integer assignCount;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("kbName", getKbName())
            .append("pinyin", getPinyin())
            .append("kbSort", getKbSort())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
