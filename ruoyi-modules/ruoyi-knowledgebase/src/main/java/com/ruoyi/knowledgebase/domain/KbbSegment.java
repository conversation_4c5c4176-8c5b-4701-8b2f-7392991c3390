package com.ruoyi.knowledgebase.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 文件分段对象 kbb_segment
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public class KbbSegment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分段ID */
    private Long id;

    /** 文件ID */
    @Excel(name = "文件ID")
    private Long fileId;

    /** 分段索引 */
    @Excel(name = "分段索引")
    private Long segmentIndex;

    /** 分段内容 */
    @Excel(name = "分段内容")
    private String segmentContent;

    /** 分段字数 */
    @Excel(name = "分段字数")
    private Long wordCount;

    /** 关键词，多个关键词用逗号分隔 */
    @Excel(name = "关键词，多个关键词用逗号分隔")
    private String keywords;

    /** 是否可用（0-可用 1-不可用） */
    @Excel(name = "是否可用", readConverterExp = "0=-可用,1=-不可用")
    private String isAvailable;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFileId(Long fileId) 
    {
        this.fileId = fileId;
    }

    public Long getFileId() 
    {
        return fileId;
    }
    public void setSegmentIndex(Long segmentIndex) 
    {
        this.segmentIndex = segmentIndex;
    }

    public Long getSegmentIndex() 
    {
        return segmentIndex;
    }
    public void setSegmentContent(String segmentContent) 
    {
        this.segmentContent = segmentContent;
    }

    public String getSegmentContent() 
    {
        return segmentContent;
    }
    public void setWordCount(Long wordCount) 
    {
        this.wordCount = wordCount;
    }

    public Long getWordCount() 
    {
        return wordCount;
    }
    public void setKeywords(String keywords) 
    {
        this.keywords = keywords;
    }

    public String getKeywords() 
    {
        return keywords;
    }
    public void setIsAvailable(String isAvailable) 
    {
        this.isAvailable = isAvailable;
    }

    public String getIsAvailable() 
    {
        return isAvailable;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fileId", getFileId())
            .append("segmentIndex", getSegmentIndex())
            .append("segmentContent", getSegmentContent())
            .append("wordCount", getWordCount())
            .append("keywords", getKeywords())
            .append("isAvailable", getIsAvailable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
