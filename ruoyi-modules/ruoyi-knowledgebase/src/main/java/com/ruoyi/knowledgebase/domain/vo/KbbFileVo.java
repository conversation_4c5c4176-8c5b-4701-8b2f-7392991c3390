package com.ruoyi.knowledgebase.domain.vo;

import com.ruoyi.knowledgebase.domain.KbbFile;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库文件视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KbbFileVo extends KbbFile {
    /**
     * 文件解析状态描述
     */
    private String fileStateDesc;

    /**
     * 设置文件解析状态
     */
    public void setStatus(String status) {
        super.setStatus(status);
        switch (status) {
            case "0":
                this.fileStateDesc = "解析中";
                break;
            case "1":
                this.fileStateDesc = "解析完成";
                break;
            case "2":
                this.fileStateDesc = "解析失败";
                break;
            default:
                this.fileStateDesc = "未知状态";
                break;
        }
    }
} 