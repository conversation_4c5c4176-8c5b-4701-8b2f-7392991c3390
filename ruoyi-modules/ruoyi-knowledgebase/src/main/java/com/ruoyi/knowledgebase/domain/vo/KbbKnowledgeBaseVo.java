package com.ruoyi.knowledgebase.domain.vo;

import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库视图对象
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KbbKnowledgeBaseVo extends KbbKnowledgeBase
{
    private static final long serialVersionUID = 1L;

    /** 文件数量 */
    private Integer fileCount;

    /** 指派人数 */
    private Integer assignCount;
} 