package com.ruoyi.knowledgebase.mapper;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbFile;

/**
 * 知识库文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface KbbFileMapper 
{
    /**
     * 查询知识库文件
     * 
     * @param id 知识库文件主键
     * @return 知识库文件
     */
    public KbbFile selectKbbFileById(Long id);

    /**
     * 查询知识库文件列表
     * 
     * @param kbbFile 知识库文件
     * @return 知识库文件集合
     */
    public List<KbbFile> selectKbbFileList(KbbFile kbbFile);

    /**
     * 新增知识库文件
     * 
     * @param kbbFile 知识库文件
     * @return 结果
     */
    public int insertKbbFile(KbbFile kbbFile);

    /**
     * 修改知识库文件
     * 
     * @param kbbFile 知识库文件
     * @return 结果
     */
    public int updateKbbFile(KbbFile kbbFile);

    /**
     * 删除知识库文件
     * 
     * @param id 知识库文件主键
     * @return 结果
     */
    public int deleteKbbFileById(Long id);

    /**
     * 批量删除知识库文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKbbFileByIds(Long[] ids);

    /**
     * 根据ID列表查询文件列表
     * 
     * @param ids 文件ID数组
     * @return 文件列表
     */
    public List<KbbFile> selectKbbFileListByIds(Long[] ids);

    /**
     * 查询知识库文件总数
     * 
     * @param kbbFile 知识库文件查询条件
     * @return 知识库文件总数
     */
    public int selectKbbFileCount(KbbFile kbbFile);
}
