package com.ruoyi.knowledgebase.mapper;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import com.ruoyi.knowledgebase.domain.dto.KbbKnowledgeBaseDto;
import com.ruoyi.knowledgebase.domain.vo.KbbKnowledgeBaseVo;
import org.apache.ibatis.annotations.Param;

/**
 * 知识库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface KbbKnowledgeBaseMapper 
{
    /**
     * 查询知识库
     * 
     * @param id 知识库主键
     * @return 知识库
     */
    public KbbKnowledgeBase selectKbbKnowledgeBaseById(Long id);

    /**
     * 查询知识库列表
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库集合
     */
    public List<KbbKnowledgeBase> selectKbbKnowledgeBaseList(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 获取知识库文件数量
     * 
     * @param kbId 知识库ID
     * @return 文件数量
     */
    public Integer selectFileCountByKbId(Long kbId);

    /**
     * 获取知识库指派人数
     * 
     * @param kbId 知识库ID
     * @return 指派人数
     */
    public Integer selectAssignCountByKbId(Long kbId);

    /**
     * 新增知识库
     * 
     * @param kbbKnowledgeBase 知识库
     * @return 结果
     */
    public int insertKbbKnowledgeBase(KbbKnowledgeBase kbbKnowledgeBase);

    /**
     * 修改知识库
     * 
     * @param kbbKnowledgeBase 知识库
     * @return 结果
     */
    public int updateKbbKnowledgeBase(KbbKnowledgeBase kbbKnowledgeBase);

    /**
     * 删除知识库
     * 
     * @param id 知识库主键
     * @return 结果
     */
    public int deleteKbbKnowledgeBaseById(Long id);

    /**
     * 批量删除知识库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKbbKnowledgeBaseByIds(Long[] ids);

    /**
     * 查询知识库总数
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库总数
     */
    public Integer selectKbbKnowledgeBaseCount(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 增加文件数量
     */
    public int incrementFileCount(Long kbId);

    /**
     * 减少文件数量
     */
    public int decrementFileCount(@Param("kbId") Long kbId, @Param("count") int count);

    /**
     * 更新知识库指派人数
     * 
     * @param kbId 知识库ID
     * @param count 新增的用户数量
     * @return 结果
     */
    public int incrementAssignCount(@Param("kbId") Long kbId, @Param("count") int count);

    /**
     * 减少指派人数
     */
    public int decrementAssignCount(Long kbId);
}
