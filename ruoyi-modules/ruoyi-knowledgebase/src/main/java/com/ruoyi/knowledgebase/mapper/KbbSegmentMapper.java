package com.ruoyi.knowledgebase.mapper;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbSegment;

/**
 * 文件分段Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface KbbSegmentMapper 
{
    /**
     * 查询文件分段
     * 
     * @param id 文件分段主键
     * @return 文件分段
     */
    public KbbSegment selectKbbSegmentById(Long id);

    /**
     * 查询文件分段列表
     * 
     * @param kbbSegment 文件分段
     * @return 文件分段集合
     */
    public List<KbbSegment> selectKbbSegmentList(KbbSegment kbbSegment);

    /**
     * 新增文件分段
     * 
     * @param kbbSegment 文件分段
     * @return 结果
     */
    public int insertKbbSegment(KbbSegment kbbSegment);

    /**
     * 修改文件分段
     * 
     * @param kbbSegment 文件分段
     * @return 结果
     */
    public int updateKbbSegment(KbbSegment kbbSegment);

    /**
     * 删除文件分段
     * 
     * @param id 文件分段主键
     * @return 结果
     */
    public int deleteKbbSegmentById(Long id);

    /**
     * 批量删除文件分段
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKbbSegmentByIds(Long[] ids);
}
