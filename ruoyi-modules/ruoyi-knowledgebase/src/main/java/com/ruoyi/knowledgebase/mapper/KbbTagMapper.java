package com.ruoyi.knowledgebase.mapper;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbTag;

/**
 * 标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface KbbTagMapper 
{
    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    public KbbTag selectKbbTagById(Long id);

    /**
     * 查询标签列表
     * 
     * @param kbbTag 标签
     * @return 标签集合
     */
    public List<KbbTag> selectKbbTagList(KbbTag kbbTag);

    /**
     * 新增标签
     * 
     * @param kbbTag 标签
     * @return 结果
     */
    public int insertKbbTag(KbbTag kbbTag);

    /**
     * 修改标签
     * 
     * @param kbbTag 标签
     * @return 结果
     */
    public int updateKbbTag(KbbTag kbbTag);

    /**
     * 删除标签
     * 
     * @param id 标签主键
     * @return 结果
     */
    public int deleteKbbTagById(Long id);

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKbbTagByIds(Long[] ids);
}
