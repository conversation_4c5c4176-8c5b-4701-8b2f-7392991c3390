package com.ruoyi.knowledgebase.service;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import com.ruoyi.knowledgebase.domain.dto.KbbKnowledgeBaseDto;
import com.ruoyi.knowledgebase.domain.vo.KbbKnowledgeBaseVo;

/**
 * 知识库Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IKbbKnowledgeBaseService 
{
    /**
     * 查询知识库
     * 
     * @param id 知识库主键
     * @return 知识库
     */
    public KbbKnowledgeBase selectKbbKnowledgeBaseById(Long id);

    /**
     * 查询知识库列表
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库集合
     */
    public List<KbbKnowledgeBaseVo> selectKbbKnowledgeBaseList(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 查询知识库总数
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库总数
     */
    public int selectKbbKnowledgeBaseCount(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 新增知识库
     * 
     * @param kbbKnowledgeBaseDto 知识库
     * @return 结果
     */
    public int insertKbbKnowledgeBase(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 修改知识库
     * 
     * @param kbbKnowledgeBaseDto 知识库
     * @return 结果
     */
    public int updateKbbKnowledgeBase(KbbKnowledgeBaseDto kbbKnowledgeBaseDto);

    /**
     * 批量删除知识库
     * 
     * @param ids 需要删除的知识库主键集合
     * @return 结果
     */
    public int deleteKbbKnowledgeBaseByIds(Long[] ids);

    /**
     * 增加文件数量
     */
    public int incrementFileCount(Long kbId);

    /**
     * 减少知识库文件数量
     * 
     * @param kbId 知识库ID
     * @param count 减少的数量
     * @return 结果
     */
    public int decrementFileCount(Long kbId, int count);

    /**
     * 更新知识库指派人数
     * 
     * @param kbId 知识库ID
     * @param count 新增的用户数量
     * @return 结果
     */
    public int incrementAssignCount(Long kbId, int count);

    /**
     * 删除知识库
     * 
     * @param kbbKnowledgeBase 知识库信息
     * @return 结果
     */
    public int deleteKbbKnowledgeBase(KbbKnowledgeBase kbbKnowledgeBase);
}
