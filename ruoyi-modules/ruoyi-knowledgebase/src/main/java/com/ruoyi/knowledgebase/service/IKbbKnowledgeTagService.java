package com.ruoyi.knowledgebase.service;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeTag;

/**
 * 知识库标签关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IKbbKnowledgeTagService 
{
    /**
     * 查询知识库标签关联
     * 
     * @param id 知识库标签关联主键
     * @return 知识库标签关联
     */
    public KbbKnowledgeTag selectKbbKnowledgeTagById(Long id);

    /**
     * 查询知识库标签关联列表
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 知识库标签关联集合
     */
    public List<KbbKnowledgeTag> selectKbbKnowledgeTagList(KbbKnowledgeTag kbbKnowledgeTag);

    /**
     * 新增知识库标签关联
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 结果
     */
    public int insertKbbKnowledgeTag(KbbKnowledgeTag kbbKnowledgeTag);

    /**
     * 修改知识库标签关联
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 结果
     */
    public int updateKbbKnowledgeTag(KbbKnowledgeTag kbbKnowledgeTag);

    /**
     * 批量删除知识库标签关联
     * 
     * @param ids 需要删除的知识库标签关联主键集合
     * @return 结果
     */
    public int deleteKbbKnowledgeTagByIds(Long[] ids);

    /**
     * 删除知识库标签关联信息
     * 
     * @param id 知识库标签关联主键
     * @return 结果
     */
    public int deleteKbbKnowledgeTagById(Long id);

    /**
     * 根据知识库ID删除标签关联
     * 
     * @param kbId 知识库ID
     * @return 结果
     */
    public int deleteKbbKnowledgeTagByKbId(Long kbId);
}
