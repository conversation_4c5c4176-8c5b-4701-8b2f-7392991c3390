package com.ruoyi.knowledgebase.service;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeUser;

/**
 * 知识库用户关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IKbbKnowledgeUserService 
{
    /**
     * 查询知识库用户关联
     * 
     * @param id 知识库用户关联主键
     * @return 知识库用户关联
     */
    public KbbKnowledgeUser selectKbbKnowledgeUserById(Long id);

    /**
     * 查询知识库用户关联列表
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 知识库用户关联集合
     */
    public List<KbbKnowledgeUser> selectKbbKnowledgeUserList(KbbKnowledgeUser kbbKnowledgeUser);

    /**
     * 新增知识库用户关联
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 结果
     */
    public int insertKbbKnowledgeUser(KbbKnowledgeUser kbbKnowledgeUser);

    /**
     * 修改知识库用户关联
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 结果
     */
    public int updateKbbKnowledgeUser(KbbKnowledgeUser kbbKnowledgeUser);

    /**
     * 批量删除知识库用户关联
     * 
     * @param ids 需要删除的知识库用户关联主键集合
     * @return 结果
     */
    public int deleteKbbKnowledgeUserByIds(Long[] ids);

    /**
     * 删除知识库用户关联信息
     * 
     * @param id 知识库用户关联主键
     * @return 结果
     */
    public int deleteKbbKnowledgeUserById(Long id);

    /**
     * 根据知识库ID删除用户关联
     * 
     * @param kbId 知识库ID
     * @return 结果
     */
    public int deleteKbbKnowledgeUserByKbId(Long kbId);

    /**
     * 根据用户ID删除知识库用户关联
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteKbbKnowledgeUserByUserId(Long userId);
}
