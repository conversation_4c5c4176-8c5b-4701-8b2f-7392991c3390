package com.ruoyi.knowledgebase.service;

import java.util.List;
import com.ruoyi.knowledgebase.domain.KbbTag;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IKbbTagService 
{
    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    public KbbTag selectKbbTagById(Long id);

    /**
     * 查询标签列表
     * 
     * @param kbbTag 标签
     * @return 标签集合
     */
    public List<KbbTag> selectKbbTagList(KbbTag kbbTag);

    /**
     * 新增标签
     * 
     * @param kbbTag 标签
     * @return 新增的标签对象
     */
    public KbbTag insertKbbTag(KbbTag kbbTag);

    /**
     * 修改标签
     * 
     * @param kbbTag 标签
     * @return 结果
     */
    public int updateKbbTag(KbbTag kbbTag);

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的标签主键集合
     * @return 结果
     */
    public int deleteKbbTagByIds(Long[] ids);

    /**
     * 删除标签信息
     * 
     * @param id 标签主键
     * @return 结果
     */
    public int deleteKbbTagById(Long id);
}
