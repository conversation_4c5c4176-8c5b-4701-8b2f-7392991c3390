package com.ruoyi.knowledgebase.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.agent.AgentApiUtil;
import com.ruoyi.common.core.utils.agent.FileInfoResponse;
import com.ruoyi.knowledgebase.domain.KbbFile;
import com.ruoyi.knowledgebase.mapper.KbbFileMapper;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeBaseMapper;
import com.ruoyi.knowledgebase.service.IKbbFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 知识库文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbFileServiceImpl implements IKbbFileService
{
    private static final Logger log = LoggerFactory.getLogger(KbbFileServiceImpl.class);

    @Autowired
    private KbbFileMapper kbbFileMapper;

    @Autowired
    private KbbKnowledgeBaseMapper kbbKnowledgeBaseMapper;

    /**
     * 查询知识库文件
     * 
     * @param id 知识库文件主键
     * @return 知识库文件
     */
    @Override
    public KbbFile selectKbbFileById(Long id)
    {
        return kbbFileMapper.selectKbbFileById(id);
    }

    /**
     * 查询知识库文件列表
     * 
     * @param kbbFile 知识库文件
     * @return 知识库文件
     */
    @Override
    public List<KbbFile> selectKbbFileList(KbbFile kbbFile)
    {
        return kbbFileMapper.selectKbbFileList(kbbFile);
    }

    /**
     * 新增知识库文件
     * 
     * @param kbbFile 知识库文件
     * @return 结果
     */
    @Override
    public int insertKbbFile(KbbFile kbbFile)
    {
        kbbFile.setCreateTime(DateUtils.getNowDate());
        return kbbFileMapper.insertKbbFile(kbbFile);
    }

    /**
     * 修改知识库文件
     * 
     * @param kbbFile 知识库文件
     * @return 结果
     */
    @Override
    public int updateKbbFile(KbbFile kbbFile)
    {
        kbbFile.setUpdateTime(DateUtils.getNowDate());
        return kbbFileMapper.updateKbbFile(kbbFile);
    }

    /**
     * 查询知识库文件总数
     * 
     * @param kbbFile 知识库文件查询条件
     * @return 知识库文件总数
     */
    @Override
    public int selectKbbFileCount(KbbFile kbbFile)
    {
        return kbbFileMapper.selectKbbFileCount(kbbFile);
    }

    @Override
    public List<KbbFile> selectKbbFileListByIds(Long[] ids) {
        return kbbFileMapper.selectKbbFileListByIds(ids);
    }
}
