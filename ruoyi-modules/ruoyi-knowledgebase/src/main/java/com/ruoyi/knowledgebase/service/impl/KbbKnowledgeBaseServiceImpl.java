package com.ruoyi.knowledgebase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeTag;
import com.ruoyi.knowledgebase.domain.dto.KbbKnowledgeBaseDto;
import com.ruoyi.knowledgebase.domain.vo.KbbKnowledgeBaseVo;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeBaseMapper;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeTagMapper;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 知识库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbKnowledgeBaseServiceImpl implements IKbbKnowledgeBaseService 
{
    @Autowired
    private KbbKnowledgeBaseMapper kbbKnowledgeBaseMapper;

    @Autowired
    private KbbKnowledgeTagMapper kbbKnowledgeTagMapper;

    /**
     * 查询知识库
     * 
     * @param id 知识库主键
     * @return 知识库
     */
    @Override
    public KbbKnowledgeBaseVo selectKbbKnowledgeBaseById(Long id)
    {
        // 获取基本信息
        KbbKnowledgeBase knowledgeBase = kbbKnowledgeBaseMapper.selectKbbKnowledgeBaseById(id);
        if (knowledgeBase == null)
        {
            return null;
        }

        // 组装VO对象
        KbbKnowledgeBaseVo vo = new KbbKnowledgeBaseVo();
        org.springframework.beans.BeanUtils.copyProperties(knowledgeBase, vo);
        
        // 设置指派人数
        vo.setAssignCount(kbbKnowledgeBaseMapper.selectAssignCountByKbId(id));
        
        return vo;
    }

    /**
     * 查询知识库列表
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库集合
     */
    @Override
    public List<KbbKnowledgeBaseVo> selectKbbKnowledgeBaseList(KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        List<KbbKnowledgeBase> list = kbbKnowledgeBaseMapper.selectKbbKnowledgeBaseList(kbbKnowledgeBaseDto);
        return list.stream().map(knowledgeBase -> {
            KbbKnowledgeBaseVo vo = new KbbKnowledgeBaseVo();
            org.springframework.beans.BeanUtils.copyProperties(knowledgeBase, vo);
            return vo;
        }).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 查询知识库总数
     * 
     * @param kbbKnowledgeBaseDto 知识库查询条件
     * @return 知识库总数
     */
    @Override
    public int selectKbbKnowledgeBaseCount(KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        return kbbKnowledgeBaseMapper.selectKbbKnowledgeBaseCount(kbbKnowledgeBaseDto);
    }

    /**
     * 新增知识库
     * 
     * @param kbbKnowledgeBaseDto 知识库DTO
     * @return 结果
     */
    @Override
    @Transactional
    public int insertKbbKnowledgeBase(KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 生成拼音
        String pinyin = "";
        if (kbbKnowledgeBaseDto.getKbName() != null) {
            pinyin = PinyinUtils.getPinyin(kbbKnowledgeBaseDto.getKbName());
            kbbKnowledgeBaseDto.setPinyin(pinyin);
        }
        
        // 获取当前时间
        Date now = DateUtils.getNowDate();
        
        // 2. 创建知识库基本信息
        KbbKnowledgeBase kbbKnowledgeBase = new KbbKnowledgeBase();
        kbbKnowledgeBase.setUserId(SecurityUtils.getUserId());
        kbbKnowledgeBase.setKbName(kbbKnowledgeBaseDto.getKbName());
        kbbKnowledgeBase.setPinyin(pinyin);
        kbbKnowledgeBase.setKbSort(kbbKnowledgeBaseDto.getKbSort());
        kbbKnowledgeBase.setStatus(kbbKnowledgeBaseDto.getStatus());
        kbbKnowledgeBase.setDelFlag("0");
        kbbKnowledgeBase.setCreateTime(now);
        kbbKnowledgeBase.setUpdateTime(now);
        kbbKnowledgeBase.setCreateBy(SecurityUtils.getNickname());
        kbbKnowledgeBase.setUpdateBy(SecurityUtils.getNickname());
        kbbKnowledgeBase.setRemark(kbbKnowledgeBaseDto.getRemark());
        
        int rows = kbbKnowledgeBaseMapper.insertKbbKnowledgeBase(kbbKnowledgeBase);
        
        // 3. 创建知识库标签关联
        if (kbbKnowledgeBaseDto.getTagIds() != null && kbbKnowledgeBaseDto.getTagIds().length > 0)
        {
            for (Long tagId : kbbKnowledgeBaseDto.getTagIds())
            {
                KbbKnowledgeTag kbbKnowledgeTag = new KbbKnowledgeTag();
                kbbKnowledgeTag.setKbId(kbbKnowledgeBase.getId());
                kbbKnowledgeTag.setTagId(tagId);
                kbbKnowledgeTag.setCreateTime(now);
                kbbKnowledgeTagMapper.insertKbbKnowledgeTag(kbbKnowledgeTag);
            }
        }
        
        return rows;
    }

    /**
     * 修改知识库
     * 
     * @param kbbKnowledgeBaseDto 知识库DTO
     * @return 结果
     */
    @Override
    @Transactional
    public int updateKbbKnowledgeBase(KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 如果知识库名称发生变化，重新生成拼音
        String pinyin = null;
        if (kbbKnowledgeBaseDto.getKbName() != null) {
            KbbKnowledgeBase oldKb = kbbKnowledgeBaseMapper.selectKbbKnowledgeBaseById(kbbKnowledgeBaseDto.getId());
            if (oldKb != null && !kbbKnowledgeBaseDto.getKbName().equals(oldKb.getKbName())) {
                pinyin = PinyinUtils.getPinyin(kbbKnowledgeBaseDto.getKbName());
                kbbKnowledgeBaseDto.setPinyin(pinyin);
            }
        }
        
        // 1. 更新知识库基本信息
        KbbKnowledgeBase kbbKnowledgeBase = new KbbKnowledgeBase();
        kbbKnowledgeBase.setId(kbbKnowledgeBaseDto.getId());
        kbbKnowledgeBase.setKbName(kbbKnowledgeBaseDto.getKbName());
        if (pinyin != null) {
            kbbKnowledgeBase.setPinyin(pinyin);
        }
        kbbKnowledgeBase.setKbSort(kbbKnowledgeBaseDto.getKbSort());
        kbbKnowledgeBase.setStatus(kbbKnowledgeBaseDto.getStatus());
        kbbKnowledgeBase.setUpdateTime(DateUtils.getNowDate());
        kbbKnowledgeBase.setUpdateBy(SecurityUtils.getNickname());
        kbbKnowledgeBase.setRemark(kbbKnowledgeBaseDto.getRemark());
        
        int rows = kbbKnowledgeBaseMapper.updateKbbKnowledgeBase(kbbKnowledgeBase);
        
        // 2. 更新知识库标签关联
        // 2.1 删除原有的标签关联
        kbbKnowledgeTagMapper.deleteKbbKnowledgeTagByKbId(kbbKnowledgeBaseDto.getId());
        
        // 2.2 创建新的标签关联
        if (kbbKnowledgeBaseDto.getTagIds() != null && kbbKnowledgeBaseDto.getTagIds().length > 0)
        {
            for (Long tagId : kbbKnowledgeBaseDto.getTagIds())
            {
                KbbKnowledgeTag kbbKnowledgeTag = new KbbKnowledgeTag();
                kbbKnowledgeTag.setKbId(kbbKnowledgeBaseDto.getId());
                kbbKnowledgeTag.setTagId(tagId);
                kbbKnowledgeTag.setCreateTime(DateUtils.getNowDate());
                kbbKnowledgeTagMapper.insertKbbKnowledgeTag(kbbKnowledgeTag);
            }
        }
        
        return rows;
    }

    /**
     * 批量删除知识库
     * 
     * @param ids 需要删除的知识库主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteKbbKnowledgeBaseByIds(Long[] ids)
    {
        return kbbKnowledgeBaseMapper.deleteKbbKnowledgeBaseByIds(ids);
    }

    /**
     * 增加文件数量
     */
    @Override
    public int incrementFileCount(Long kbId) {
        return kbbKnowledgeBaseMapper.incrementFileCount(kbId);
    }

    /**
     * 减少知识库文件数量
     */
    @Override
    public int decrementFileCount(Long kbId, int count) {
        return kbbKnowledgeBaseMapper.decrementFileCount(kbId, count);
    }

    /**
     * 更新知识库指派人数
     */
    @Override
    public int incrementAssignCount(Long kbId, int count) {
        return kbbKnowledgeBaseMapper.incrementAssignCount(kbId, count);
    }

    /**
     * 删除知识库
     * 
     * @param kbbKnowledgeBase 知识库信息
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeBase(KbbKnowledgeBase kbbKnowledgeBase) {
        return kbbKnowledgeBaseMapper.updateKbbKnowledgeBase(kbbKnowledgeBase);
    }
}
