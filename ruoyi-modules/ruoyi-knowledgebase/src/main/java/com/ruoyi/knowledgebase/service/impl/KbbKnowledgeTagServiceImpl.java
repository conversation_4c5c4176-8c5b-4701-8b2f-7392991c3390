package com.ruoyi.knowledgebase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeTagMapper;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeTag;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeTagService;

/**
 * 知识库标签关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbKnowledgeTagServiceImpl implements IKbbKnowledgeTagService 
{
    @Autowired
    private KbbKnowledgeTagMapper kbbKnowledgeTagMapper;

    /**
     * 查询知识库标签关联
     * 
     * @param id 知识库标签关联主键
     * @return 知识库标签关联
     */
    @Override
    public KbbKnowledgeTag selectKbbKnowledgeTagById(Long id)
    {
        return kbbKnowledgeTagMapper.selectKbbKnowledgeTagById(id);
    }

    /**
     * 查询知识库标签关联列表
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 知识库标签关联
     */
    @Override
    public List<KbbKnowledgeTag> selectKbbKnowledgeTagList(KbbKnowledgeTag kbbKnowledgeTag)
    {
        return kbbKnowledgeTagMapper.selectKbbKnowledgeTagList(kbbKnowledgeTag);
    }

    /**
     * 新增知识库标签关联
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 结果
     */
    @Override
    public int insertKbbKnowledgeTag(KbbKnowledgeTag kbbKnowledgeTag)
    {
        kbbKnowledgeTag.setCreateTime(DateUtils.getNowDate());
        return kbbKnowledgeTagMapper.insertKbbKnowledgeTag(kbbKnowledgeTag);
    }

    /**
     * 修改知识库标签关联
     * 
     * @param kbbKnowledgeTag 知识库标签关联
     * @return 结果
     */
    @Override
    public int updateKbbKnowledgeTag(KbbKnowledgeTag kbbKnowledgeTag)
    {
        return kbbKnowledgeTagMapper.updateKbbKnowledgeTag(kbbKnowledgeTag);
    }

    /**
     * 批量删除知识库标签关联
     * 
     * @param ids 需要删除的知识库标签关联主键
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeTagByIds(Long[] ids)
    {
        return kbbKnowledgeTagMapper.deleteKbbKnowledgeTagByIds(ids);
    }

    /**
     * 删除知识库标签关联信息
     * 
     * @param id 知识库标签关联主键
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeTagById(Long id)
    {
        return kbbKnowledgeTagMapper.deleteKbbKnowledgeTagById(id);
    }

    /**
     * 根据知识库ID删除标签关联
     * 
     * @param kbId 知识库ID
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeTagByKbId(Long kbId)
    {
        return kbbKnowledgeTagMapper.deleteKbbKnowledgeTagByKbId(kbId);
    }
}
