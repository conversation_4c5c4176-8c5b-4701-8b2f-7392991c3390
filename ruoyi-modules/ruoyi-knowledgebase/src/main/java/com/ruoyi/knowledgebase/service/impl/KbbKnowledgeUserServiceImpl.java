package com.ruoyi.knowledgebase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeUserMapper;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeUser;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 知识库用户关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbKnowledgeUserServiceImpl implements IKbbKnowledgeUserService 
{
    @Autowired
    private KbbKnowledgeUserMapper kbbKnowledgeUserMapper;

    /**
     * 查询知识库用户关联
     * 
     * @param id 知识库用户关联主键
     * @return 知识库用户关联
     */
    @Override
    public KbbKnowledgeUser selectKbbKnowledgeUserById(Long id)
    {
        return kbbKnowledgeUserMapper.selectKbbKnowledgeUserById(id);
    }

    /**
     * 查询知识库用户关联列表
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 知识库用户关联
     */
    @Override
    public List<KbbKnowledgeUser> selectKbbKnowledgeUserList(KbbKnowledgeUser kbbKnowledgeUser)
    {
        return kbbKnowledgeUserMapper.selectKbbKnowledgeUserList(kbbKnowledgeUser);
    }

    /**
     * 新增知识库用户关联
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 结果
     */
    @Override
    public int insertKbbKnowledgeUser(KbbKnowledgeUser kbbKnowledgeUser)
    {
        kbbKnowledgeUser.setCreateTime(DateUtils.getNowDate());
        return kbbKnowledgeUserMapper.insertKbbKnowledgeUser(kbbKnowledgeUser);
    }

    /**
     * 修改知识库用户关联
     * 
     * @param kbbKnowledgeUser 知识库用户关联
     * @return 结果
     */
    @Override
    public int updateKbbKnowledgeUser(KbbKnowledgeUser kbbKnowledgeUser)
    {
        return kbbKnowledgeUserMapper.updateKbbKnowledgeUser(kbbKnowledgeUser);
    }

    /**
     * 批量删除知识库用户关联
     * 
     * @param ids 需要删除的知识库用户关联主键
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeUserByIds(Long[] ids)
    {
        return kbbKnowledgeUserMapper.deleteKbbKnowledgeUserByIds(ids);
    }

    /**
     * 删除知识库用户关联信息
     * 
     * @param id 知识库用户关联主键
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeUserById(Long id)
    {
        return kbbKnowledgeUserMapper.deleteKbbKnowledgeUserById(id);
    }

    /**
     * 根据知识库ID删除用户关联
     * 
     * @param kbId 知识库ID
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeUserByKbId(Long kbId)
    {
        return kbbKnowledgeUserMapper.deleteKbbKnowledgeUserByKbId(kbId);
    }

    /**
     * 根据用户ID删除知识库用户关联
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteKbbKnowledgeUserByUserId(Long userId) {
        return kbbKnowledgeUserMapper.deleteKbbKnowledgeUserByUserId(userId);
    }
}
