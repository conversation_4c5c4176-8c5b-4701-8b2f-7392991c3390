package com.ruoyi.knowledgebase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.knowledgebase.mapper.KbbSegmentMapper;
import com.ruoyi.knowledgebase.domain.KbbSegment;
import com.ruoyi.knowledgebase.service.IKbbSegmentService;

/**
 * 文件分段Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbSegmentServiceImpl implements IKbbSegmentService 
{
    @Autowired
    private KbbSegmentMapper kbbSegmentMapper;

    /**
     * 查询文件分段
     * 
     * @param id 文件分段主键
     * @return 文件分段
     */
    @Override
    public KbbSegment selectKbbSegmentById(Long id)
    {
        return kbbSegmentMapper.selectKbbSegmentById(id);
    }

    /**
     * 查询文件分段列表
     * 
     * @param kbbSegment 文件分段
     * @return 文件分段
     */
    @Override
    public List<KbbSegment> selectKbbSegmentList(KbbSegment kbbSegment)
    {
        return kbbSegmentMapper.selectKbbSegmentList(kbbSegment);
    }

    /**
     * 新增文件分段
     * 
     * @param kbbSegment 文件分段
     * @return 结果
     */
    @Override
    public int insertKbbSegment(KbbSegment kbbSegment)
    {
        kbbSegment.setCreateTime(DateUtils.getNowDate());
        return kbbSegmentMapper.insertKbbSegment(kbbSegment);
    }

    /**
     * 修改文件分段
     * 
     * @param kbbSegment 文件分段
     * @return 结果
     */
    @Override
    public int updateKbbSegment(KbbSegment kbbSegment)
    {
        kbbSegment.setUpdateTime(DateUtils.getNowDate());
        return kbbSegmentMapper.updateKbbSegment(kbbSegment);
    }

    /**
     * 批量删除文件分段
     * 
     * @param ids 需要删除的文件分段主键
     * @return 结果
     */
    @Override
    public int deleteKbbSegmentByIds(Long[] ids)
    {
        return kbbSegmentMapper.deleteKbbSegmentByIds(ids);
    }

    /**
     * 删除文件分段信息
     * 
     * @param id 文件分段主键
     * @return 结果
     */
    @Override
    public int deleteKbbSegmentById(Long id)
    {
        return kbbSegmentMapper.deleteKbbSegmentById(id);
    }
}
