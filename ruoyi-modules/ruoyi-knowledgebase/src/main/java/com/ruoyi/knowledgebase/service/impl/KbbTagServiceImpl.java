package com.ruoyi.knowledgebase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.knowledgebase.mapper.KbbTagMapper;
import com.ruoyi.knowledgebase.domain.KbbTag;
import com.ruoyi.knowledgebase.service.IKbbTagService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.knowledgebase.mapper.KbbKnowledgeTagMapper;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class KbbTagServiceImpl implements IKbbTagService 
{
    @Autowired
    private KbbTagMapper kbbTagMapper;

    @Autowired
    private KbbKnowledgeTagMapper kbbKnowledgeTagMapper;

    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    @Override
    public KbbTag selectKbbTagById(Long id)
    {
        return kbbTagMapper.selectKbbTagById(id);
    }

    /**
     * 查询标签列表
     * 
     * @param kbbTag 标签
     * @return 标签
     */
    @Override
    public List<KbbTag> selectKbbTagList(KbbTag kbbTag)
    {
        return kbbTagMapper.selectKbbTagList(kbbTag);
    }

    /**
     * 新增标签
     * 
     * @param kbbTag 标签
     * @return 新增的标签对象
     */
    @Override
    public KbbTag insertKbbTag(KbbTag kbbTag)
    {
        // 检查标签名称是否已存在
        KbbTag queryTag = new KbbTag();
        queryTag.setTagName(kbbTag.getTagName());
        List<KbbTag> existingTags = kbbTagMapper.selectKbbTagList(queryTag);
        if (!existingTags.isEmpty()) {
            throw new ServiceException("标签名称已存在");
        }
        
        kbbTag.setCreateTime(DateUtils.getNowDate());
        kbbTagMapper.insertKbbTag(kbbTag);
        return kbbTag;
    }

    /**
     * 修改标签
     * 
     * @param kbbTag 标签
     * @return 结果
     */
    @Override
    public int updateKbbTag(KbbTag kbbTag)
    {
        // 检查标签名称是否已存在（排除自身）
        KbbTag queryTag = new KbbTag();
        queryTag.setTagName(kbbTag.getTagName());
        List<KbbTag> existingTags = kbbTagMapper.selectKbbTagList(queryTag);
        if (!existingTags.isEmpty() && !existingTags.get(0).getId().equals(kbbTag.getId())) {
            throw new ServiceException("标签名称已存在");
        }
        
        kbbTag.setUpdateTime(DateUtils.getNowDate());
        return kbbTagMapper.updateKbbTag(kbbTag);
    }

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的标签主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteKbbTagByIds(Long[] ids)
    {
        // 1. 删除标签与知识库的关联关系
        for (Long id : ids)
        {
            kbbKnowledgeTagMapper.deleteKbbKnowledgeTagByTagId(id);
        }
        
        // 2. 删除标签
        return kbbTagMapper.deleteKbbTagByIds(ids);
    }

    /**
     * 删除标签信息
     * 
     * @param id 标签主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteKbbTagById(Long id)
    {
        // 1. 删除标签与知识库的关联关系
        kbbKnowledgeTagMapper.deleteKbbKnowledgeTagByTagId(id);
        
        // 2. 删除标签
        return kbbTagMapper.deleteKbbTagById(id);
    }
}
