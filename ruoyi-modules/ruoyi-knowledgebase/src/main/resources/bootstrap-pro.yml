# Tomcat
server:
  port: 9218

# Spring
spring:
  main:
    # 允许class之间循环调用
    allow-circular-references: true
  application:
    # 应用名称
    name: ruoyi-knowledgebase
  cloud:
    nacos:
      discovery:
        ip: **************
        # 服务注册地址
        server-addr: **************:8848
        username: nacos
        password: scy_zhbz@2024*%&!
        namespace: 9aecd466-e52c-4a1a-ace3-4ce552e27f7e
      config:
        namespace: 9aecd466-e52c-4a1a-ace3-4ce552e27f7e
        # 配置中心地址
        server-addr: **************:8848
        username: nacos
        password: scy_zhbz@2024*%&!
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
