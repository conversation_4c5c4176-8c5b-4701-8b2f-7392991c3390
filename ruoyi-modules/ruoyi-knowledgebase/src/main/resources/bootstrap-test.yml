# Tomcat
server:
  port: 9218

# Spring
spring:
  main:
    # 允许class之间循环调用
    allow-circular-references: true
  application:
    # 应用名称
    name: ruoyi-knowledgebase
  cloud:
    nacos:
      discovery:
        ip: ${HOST_IP}
        # 服务注册地址
        server-addr: ${HOST_IP}:8848
        username: nacos
        password: nacos
        namespace: 3d1f4237-c4ba-4be9-80f7-ad23d3670a83
      config:
        namespace: 3d1f4237-c4ba-4be9-80f7-ad23d3670a83
        # 配置中心地址
        server-addr: ${HOST_IP}:8848
        username: nacos
        password: nacos
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
