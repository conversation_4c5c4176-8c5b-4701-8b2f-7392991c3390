<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbKnowledgeBaseMapper">
    
    <resultMap type="KbbKnowledgeBase" id="KbbKnowledgeBaseResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="kbName"    column="kb_name"    />
        <result property="pinyin"    column="pinyin"    />
        <result property="kbSort"    column="kb_sort"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKbbKnowledgeBase">
        select id, user_id, kb_name, pinyin, kb_sort, status, file_count, del_flag, create_by, create_time, update_by, update_time, remark
        from kbb_knowledge_base
    </sql>

    <sql id="selectKbbKnowledgeBaseVo">
        select id, user_id, kb_name, pinyin, kb_sort, status, file_count, assign_count, del_flag, create_by, create_time, update_by, update_time, remark
        from kbb_knowledge_base
    </sql>

    <select id="selectKbbKnowledgeBaseList" parameterType="KbbKnowledgeBaseDto" resultMap="KbbKnowledgeBaseResult">
        select kb.id, kb.user_id, kb.kb_name, kb.pinyin, kb.kb_sort, kb.status, kb.file_count, kb.assign_count, kb.del_flag,
               kb.create_by, kb.create_time, kb.update_by, kb.update_time, kb.remark
        from kbb_knowledge_base kb
        <if test="ownershipType != null and (ownershipType == 0 or ownershipType == 2)">
            left join kbb_knowledge_user ku on kb.id = ku.kb_id
        </if>
        where kb.del_flag = '0'
        <if test="userId != null">
            <choose>
                <when test="ownershipType != null and ownershipType == 0">
                    and (kb.user_id = #{userId} or ku.user_id = #{userId})
                </when>
                <when test="ownershipType != null and ownershipType == 1">
                    and kb.user_id = #{userId}
                </when>
                <when test="ownershipType != null and ownershipType == 2">
                    and kb.user_id != #{userId} and ku.user_id = #{userId}
                </when>
                <otherwise>
                    and (kb.user_id = #{userId} or ku.user_id = #{userId})
                </otherwise>
            </choose>
        </if>
        <if test="kbName != null and kbName != ''">
            and kb.kb_name like concat('%', #{kbName}, '%')
        </if>
        <if test="status != null and status != ''">
            and kb.status = #{status}
        </if>
        group by kb.id
        <choose>
            <when test="sortType != null and sortType == 0">
                order by kb.pinyin ASC
            </when>
            <when test="sortType != null and sortType == 1">
                order by kb.create_time desc
            </when>
            <when test="sortType != null and sortType == 2">
                order by kb.update_time desc
            </when>
            <otherwise>
                order by kb.create_time desc, kb.kb_sort
            </otherwise>
        </choose>
    </select>

    <select id="selectKbbKnowledgeBaseCount" parameterType="KbbKnowledgeBaseDto" resultType="Integer">
        select count(distinct kb.id)
        from kbb_knowledge_base kb
        <if test="ownershipType != null and (ownershipType == 0 or ownershipType == 2)">
            left join kbb_knowledge_user ku on kb.id = ku.kb_id
        </if>
        where kb.del_flag = '0'
        <if test="userId != null">
            <choose>
                <when test="ownershipType != null and ownershipType == 0">
                    and (kb.user_id = #{userId} or ku.user_id = #{userId})
                </when>
                <when test="ownershipType != null and ownershipType == 1">
                    and kb.user_id = #{userId}
                </when>
                <when test="ownershipType != null and ownershipType == 2">
                    and kb.user_id != #{userId} and ku.user_id = #{userId}
                </when>
                <otherwise>
                    and (kb.user_id = #{userId} or ku.user_id = #{userId})
                </otherwise>
            </choose>
        </if>
        <if test="kbName != null and kbName != ''">
            and kb.kb_name like concat('%', #{kbName}, '%')
        </if>
        <if test="status != null and status != ''">
            and kb.status = #{status}
        </if>
    </select>
    
    <select id="selectKbbKnowledgeBaseById" parameterType="Long" resultMap="KbbKnowledgeBaseResult">
        <include refid="selectKbbKnowledgeBase"/>
        where id = #{id}
    </select>

    <select id="selectFileCountByKbId" parameterType="Long" resultType="Integer">
        select count(distinct id) 
        from kbb_file 
        where kb_id = #{kbId} and status = '0'
    </select>

    <select id="selectAssignCountByKbId" parameterType="Long" resultType="Integer">
        select count(distinct user_id) 
        from kbb_knowledge_user 
        where kb_id = #{kbId}
    </select>

    <insert id="insertKbbKnowledgeBase" parameterType="KbbKnowledgeBase" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_knowledge_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="kbName != null and kbName != ''">kb_name,</if>
            <if test="pinyin != null and pinyin != ''">pinyin,</if>
            <if test="kbSort != null">kb_sort,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="kbName != null and kbName != ''">#{kbName},</if>
            <if test="pinyin != null and pinyin != ''">#{pinyin},</if>
            <if test="kbSort != null">#{kbSort},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKbbKnowledgeBase" parameterType="KbbKnowledgeBase">
        update kbb_knowledge_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="kbName != null and kbName != ''">kb_name = #{kbName},</if>
            <if test="pinyin != null and pinyin != ''">pinyin = #{pinyin},</if>
            <if test="kbSort != null">kb_sort = #{kbSort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbKnowledgeBaseById" parameterType="Long">
        update kbb_knowledge_base set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteKbbKnowledgeBaseByIds" parameterType="String">
        update kbb_knowledge_base set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 增加文件数量 -->
    <update id="incrementFileCount" parameterType="Long">
        update kbb_knowledge_base
        set file_count = file_count + 1,
            update_time = sysdate()
        where id = #{kbId}
    </update>

    <!-- 减少文件数量 -->
    <update id="decrementFileCount">
        update kbb_knowledge_base
        set file_count = #{count},
            update_time = sysdate()
        where id = #{kbId}
    </update>

    <!-- 更新知识库指派人数 -->
    <update id="incrementAssignCount">
        update kbb_knowledge_base
        set assign_count = #{count},
            update_time = sysdate()
        where id = #{kbId}
    </update>
</mapper>