<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbKnowledgeUserMapper">
    
    <resultMap type="KbbKnowledgeUser" id="KbbKnowledgeUserResult">
        <result property="id"    column="id"    />
        <result property="kbId"    column="kb_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectKbbKnowledgeUserVo">
        select id, kb_id, user_id, create_by, create_time from kbb_knowledge_user
    </sql>

    <select id="selectKbbKnowledgeUserList" parameterType="KbbKnowledgeUser" resultMap="KbbKnowledgeUserResult">
        <include refid="selectKbbKnowledgeUserVo"/>
        <where>  
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectKbbKnowledgeUserById" parameterType="Long" resultMap="KbbKnowledgeUserResult">
        <include refid="selectKbbKnowledgeUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKbbKnowledgeUser" parameterType="KbbKnowledgeUser" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_knowledge_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kbId != null">kb_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kbId != null">#{kbId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateKbbKnowledgeUser" parameterType="KbbKnowledgeUser">
        update kbb_knowledge_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbKnowledgeUserById" parameterType="Long">
        delete from kbb_knowledge_user where id = #{id}
    </delete>

    <delete id="deleteKbbKnowledgeUserByIds" parameterType="String">
        delete from kbb_knowledge_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteKbbKnowledgeUserByKbId" parameterType="Long">
        delete from kbb_knowledge_user where kb_id = #{kbId}
    </delete>

    <delete id="deleteKbbKnowledgeUserByUserId" parameterType="Long">
        delete from kbb_knowledge_user where user_id = #{userId}
    </delete>
</mapper>