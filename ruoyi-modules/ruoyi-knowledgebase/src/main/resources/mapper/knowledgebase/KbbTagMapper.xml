<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbTagMapper">
    
    <resultMap type="KbbTag" id="KbbTagResult">
        <result property="id"    column="id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKbbTagVo">
        select id, tag_name, create_by, create_time, update_by, update_time, remark from kbb_tag
    </sql>

    <select id="selectKbbTagList" parameterType="KbbTag" resultMap="KbbTagResult">
        <include refid="selectKbbTagVo"/>
        <where>  
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
        </where>
    </select>
    
    <select id="selectKbbTagById" parameterType="Long" resultMap="KbbTagResult">
        <include refid="selectKbbTagVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKbbTag" parameterType="KbbTag" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKbbTag" parameterType="KbbTag">
        update kbb_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbTagById" parameterType="Long">
        delete from kbb_tag where id = #{id}
    </delete>

    <delete id="deleteKbbTagByIds" parameterType="String">
        delete from kbb_tag where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>