package com.ruoyi.system.config;

import com.ruoyi.common.redis.service.RedisLocker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    @Autowired
    private RedisLocker redisLocker;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 针对redis数据失效事件，进行数据处理
     *
     * @param message
     * @param pattern
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 用户做自己的业务处理即可,注意message.toString()可以获取失效的key
        String expiredKey = message.toString();
        if (!checkForRedisLock(expiredKey)) {
            return;
        }
        redisLocker.releaseLock(expiredKey);
    }

    private boolean checkForRedisLock(String key) {
        try {
            return redisLocker.tryGetLock(key, "RedisKeyExpirationListener get lock", 10);
        } catch (Exception exception) {
            log.error(exception.toString());
        }

        return false;
    }

}