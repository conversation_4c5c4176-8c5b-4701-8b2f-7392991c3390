package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.domain.SysPrivacyAgreement;
import com.ruoyi.system.service.ISysPrivacyAgreementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "app隐私协议API")
@RestController
@RequestMapping("/appAgreement")
public class AppAgreementController {
    @Autowired
    private ISysPrivacyAgreementService sysPrivacyAgreementService;

    /**
     * 查询隐私协议列表-没有拦截
     */
    //@RequiresPermissions("system:agreement:list")
    @GetMapping("/list")
    @ApiOperation("隐私协议查询")
    public AjaxResult appList(SysPrivacyAgreement sysPrivacyAgreement) {
        List<SysPrivacyAgreement> list = sysPrivacyAgreementService.selectList(sysPrivacyAgreement);
        return AjaxResult.success(list);
    }
}
