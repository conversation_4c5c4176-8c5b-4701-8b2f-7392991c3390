package com.ruoyi.system.controller;


import com.ruoyi.common.core.annotation.RateLimiter;
import com.ruoyi.common.core.utils.ip.IPCheck;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysAppUser;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.AppErrorLog;
import com.ruoyi.system.service.IAppErrorLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * APP崩溃日志Controller
 * 
 * <AUTHOR>
 * @date 2022-09-21
 */
@Api(tags = "崩溃日志API")
@RestController
@RequestMapping("/appErrorLog")
public class AppErrorLogController extends BaseController
{
    // 字典类型：终端类别
    private static final String DICT_TYPE = "sys_terminal_type";
    // 日志级别
    private static final Integer LEVEL_ZERO = 0; // 崩溃日志
    private static final Integer LEVEL_ONE = 1; // 严重日志

    @Autowired
    private IAppErrorLogService appErrorLogService;

    /**
     * 查询APP崩溃日志列表
     */
    @ApiOperation("查询APP崩溃日志列表")
    @RequiresPermissions("system:AppErrorLog:list")
    @GetMapping("/list")
    public TableDataInfo list(AppErrorLog appErrorLog)
    {
        startPage();
        List<AppErrorLog> list = appErrorLogService.selectAppErrorLogList(appErrorLog);
        return getDataTable(list);
    }

    /**
     * 导出APP崩溃日志列表
     */
    @ApiOperation("导出APP崩溃日志列表")
    @RequiresPermissions("system:AppErrorLog:export")
    @Log(title = "APP崩溃日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppErrorLog appErrorLog)
    {
        List<AppErrorLog> list = appErrorLogService.selectAppErrorLogList(appErrorLog);
        ExcelUtil<AppErrorLog> util = new ExcelUtil<AppErrorLog>(AppErrorLog.class);
        util.exportExcel(response, list, "APP崩溃日志数据");
    }

    /**
     * 新增APP崩溃日志
     */
    @ApiOperation("新增APP崩溃日志")
    @Log(title = "APP崩溃日志", businessType = BusinessType.INSERT)
    @PostMapping
    @RateLimiter
    public AjaxResult add(@RequestBody AppErrorLog appErrorLog)
    {
        // ID
        if(appErrorLog.getId() != null){
            return error("id不合法！");
        }
        // 校验终端类别
        String appTypeLabel = DictUtils.getDictLabel(DICT_TYPE, appErrorLog.getAppCode());
        if("".equals(appTypeLabel)){
            return error("终端类别appCode不合法！");
        }
        // 日志级别
        if(!LEVEL_ZERO.equals(appErrorLog.getLevel()) && !LEVEL_ONE.equals(appErrorLog.getLevel())){
            return error("日志级别level不合法！");
        }
        // 日志内容
        if(StringUtils.isBlank(appErrorLog.getLog())){
            return error("日志内容log不合法！");
        }
        // IP
        if(!IPCheck.isValidIPAddress(appErrorLog.getIp())){
            return error("ip不合法！");
        }
        int rows = appErrorLogService.insertRedisAppErrorLog(appErrorLog);
        return toAjax(rows);
    }

    /**
     * 删除APP崩溃日志
     */
    @ApiOperation("删除APP崩溃日志")
    @RequiresPermissions("system:AppErrorLog:remove")
    @Log(title = "APP崩溃日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appErrorLogService.deleteAppErrorLogByIds(ids));
    }
}
