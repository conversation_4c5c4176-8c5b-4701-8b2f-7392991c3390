package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.domain.AppUpdateInfo;
import com.ruoyi.system.service.IAppUpdateInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 2.0.7版大屏应用程序更新信息Controller
 *
 * <AUTHOR>
 * @date 2023-3-1
 */

@Api(tags = "APP更新信息")
@RestController
@RequestMapping("/updateInfo")
public class AppUpdateController extends BaseController {

    @Autowired
    private IAppUpdateInfoService appUpdateInfoService;

    /**
     * 2.0.7版大屏获取应用程序更新信息详细信息
     */
    @ApiOperation("获取应用程序更新信息详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appType", value = "app类型", dataType = "String", dataTypeClass = String.class)
    })
    @GetMapping(value = "")
    public AjaxResult getInfo(@RequestParam("appType") String appType) {
        AppUpdateInfo appUpdateInfo = appUpdateInfoService.queryByType(appType);
        if (appUpdateInfo == null) {
            return AjaxResult.error("未找到对应类型的app更新信息");
        }
        return AjaxResult.success(appUpdateInfo);
    }
}
