package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.AppUpdateInfo;
import com.ruoyi.system.service.IAppUpdateInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.common.security.utils.SecurityUtils.getLoginUser;

/**
 * 应用程序更新信息Controller
 *
 * <AUTHOR>
 * @date 2021-12-06
 */

@Api(tags = "APP更新信息")
@RestController
@RequestMapping("/appUpdateInfo")
public class AppUpdateInfoController extends BaseController {

    @Autowired
    private IAppUpdateInfoService appUpdateInfoService;


    /**
     * 获取应用程序更新信息详细信息
     */
    @ApiOperation(value = "获取应用程序更新信息详细信息")
    @RequiresPermissions("system:updateInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(appUpdateInfoService.selectById(id));
    }

    /**
     * 查询应用程序更新信息列表
     */
    @ApiOperation(value = "查询应用程序更新信息列表")
    @RequiresPermissions("system:updateInfo:query")
    @GetMapping("/list")
    public AjaxResult list(AppUpdateInfo appUpdateInfo) {
        startPage();
        List<AppUpdateInfo> list = appUpdateInfoService.selectList(appUpdateInfo);
        return getNewDataTable(list);
    }


    /**
     * 获取应用程序更新信息详细信息
     */
    @ApiOperation("获取应用程序更新信息详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appType", value = "app类型", dataType = "String", dataTypeClass = String.class)
    })
    @GetMapping(value = "")
    public AjaxResult getInfo(@RequestParam("appType") String appType) {
        AppUpdateInfo appUpdateInfo = appUpdateInfoService.queryByType(appType);
        if (appUpdateInfo == null) {
            return AjaxResult.error("未找到对应类型的app更新信息");
        }
        return AjaxResult.success(appUpdateInfo);
    }

    /**
     * 新增应用程序更新信息
     */
    @ApiOperation(value = "新增应用程序更新信息")
    @RequiresPermissions("system:updateInfo:add")
    @Log(title = "应用程序更新信息", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody AppUpdateInfo appUpdateInfo) {
        // Check for existing record with the same appType and appVersion
        if (appUpdateInfoService.existsByTypeAndVersion(appUpdateInfo.getAppType(), appUpdateInfo.getAppVersion())) {
            return AjaxResult.error("已存在相同类型和版本的应用程序更新信息");
        }
        appUpdateInfo.setCreateBy(getLoginUser().getUsername());
        return toAjax(appUpdateInfoService.insert(appUpdateInfo));
    }

    /**
     * 修改应用程序更新信息
     */
    @ApiOperation(value = "修改应用程序更新信息")
    @RequiresPermissions("system:updateInfo:edit")
    @Log(title = "应用程序更新信息", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody AppUpdateInfo appUpdateInfo) {
        // Check for existing record with the same appType and appVersion
        if (appUpdateInfoService.existsByTypeAndVersion(appUpdateInfo.getAppType(), appUpdateInfo.getAppVersion(), appUpdateInfo.getId())) {
            return AjaxResult.error("已存在相同类型和版本的应用程序更新信息");
        }
        appUpdateInfo.setUpdateBy(getLoginUser().getUsername());
        return toAjax(appUpdateInfoService.update(appUpdateInfo));
    }

    /**
     * 删除应用程序更新信息
     */
    @ApiOperation(value = "删除应用程序更新信息")
    @RequiresPermissions("system:updateInfo:remove")
    @Log(title = "应用程序更新信息", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(appUpdateInfoService.deleteByIds(ids));
    }
}
