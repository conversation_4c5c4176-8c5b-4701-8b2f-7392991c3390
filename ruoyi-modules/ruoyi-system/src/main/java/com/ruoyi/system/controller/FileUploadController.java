package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.serviceUtils.OssUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.util.List;

@Api(tags = "文件上传API")
@RestController
@RequestMapping("/sysFileUpload")
public class FileUploadController {

    @Autowired
    private OssUtil ossUtil;  //1、注入OssUtil

    @ApiOperation(value = "图片上传")
    @PostMapping("/uploadImg")
    public AjaxResult uploadImg(@RequestParam("file") MultipartFile file) throws FileNotFoundException {
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件内容为空，请重新选择");
        }
        String url = ossUtil.uploadImgFile(file); //2、调用
        url = url.replace("http", "https");
        return AjaxResult.success("上传成功", url);
    }

    @ApiOperation(value = "文件上传")
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) throws FileNotFoundException {
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件内容为空，请重新选择");
        }
        String url = ossUtil.uploadFile(file); //2、调用

        return AjaxResult.success("上传成功", url);
    }

    @ApiOperation(value = "多图片上传")
    @PostMapping("/uploadMultiImg")
    public AjaxResult uploadMultiImg(@RequestParam("files") List<MultipartFile> files) throws FileNotFoundException {
        if (files.isEmpty()) {
            return AjaxResult.error("上传文件内容为空，请重新选择");
        }
        String url = ossUtil.uploadFile(files); //2、调用
        return AjaxResult.success("上传成功", url);
    }

}
