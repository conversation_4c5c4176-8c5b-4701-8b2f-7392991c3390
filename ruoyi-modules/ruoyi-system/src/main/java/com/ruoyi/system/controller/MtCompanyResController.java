package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.MtCompanyRes;
import com.ruoyi.system.service.IMtCompanyResService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.ruoyi.common.security.utils.SecurityUtils.getUsername;

/**
 * 公司与会议资源Controller
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Api(tags = "公司及会议资源API")
@RestController
@RequestMapping("/mtCompanyRes")
public class MtCompanyResController extends BaseController {
    @Autowired
    private IMtCompanyResService mtCompanyResService;

    /**
     * 查询公司与会议资源列表
     */
    @ApiOperation(value = "公司及会议资源列表")
    @RequiresPermissions("mt:companyRes:list")
    @GetMapping("/list")
    public AjaxResult list() {
        startPage();
        List<MtCompanyRes> list = mtCompanyResService.selectListAll();
        return getNewDataTable(list);
    }

    /**
     * 查询公司与会议资源列表
     */
    @ApiOperation(value = "公司与会议资源集合")
    @RequiresPermissions("mt:companyRes:list")
    @GetMapping("/listDetail")
    public AjaxResult listDetail() {
        startPage();
        List<MtCompanyRes> list = mtCompanyResService.selectCompanyInfoRes();
        return getNewDataTable(list);
    }

    /**
     * 导出公司与会议资源列表
     */
    @ApiOperation(value = "导出公司与会议资源列表")
    @RequiresPermissions("mt:companyRes:export")
    @Log(title = "公司与会议资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<MtCompanyRes> list = mtCompanyResService.selectListAll();
        ExcelUtil<MtCompanyRes> util = new ExcelUtil<>(MtCompanyRes.class);
        util.exportExcel(response, list, "公司与会议资源数据");
    }

    /**
     * 获取公司与会议资源详细信息
     */
    @ApiOperation(value = "获取公司与会议资源详细信息")
    @RequiresPermissions("mt:companyRes:query")
    @GetMapping()
    public AjaxResult getInfo(@RequestParam("companyId") Long companyId) {
        return AjaxResult.success(mtCompanyResService.selectListByCompanyId(companyId));
    }

    /**
     * 新增公司与会议资源
     */
    @ApiOperation(value = "新增公司与会议资源")
    @RequiresPermissions("mt:companyRes:add")
    @Log(title = "公司与会议资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MtCompanyRes mtCompanyRes) {
        mtCompanyRes.setCreateBy(getUsername());
        mtCompanyRes.setCreateTime(DateUtils.getNowDate());
        return toAjax(mtCompanyResService.insert(mtCompanyRes));
    }

    /**
     * 修改公司与会议资源
     */
    @ApiOperation(value = "修改公司与会议资源")
    @RequiresPermissions("mt:companyRes:edit")
    @Log(title = "公司与会议资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MtCompanyRes mtCompanyRes) {
        mtCompanyRes.setUpdateBy(getUsername());
        mtCompanyRes.setUpdateTime(DateUtils.getNowDate());
        return toAjax(mtCompanyResService.update(mtCompanyRes));
    }

//    /**
//     * 删除公司与会议资源
//     */
//    @RequiresPermissions("system:res:remove")
//    @Log(title = "公司与会议资源", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(mtCompanyResService.delete(ids));
//    }
}
