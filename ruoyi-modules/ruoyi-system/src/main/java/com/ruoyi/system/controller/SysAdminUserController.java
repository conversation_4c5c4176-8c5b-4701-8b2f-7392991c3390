package com.ruoyi.system.controller;


import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.sign.RsaUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.domain.vo.SysUserVo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.utils.UserRedisUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.dto.SysAdminUserDto;
import com.ruoyi.system.service.*;
import com.ruoyi.system.serviceUtils.SysPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "管理员用户信息API")
@RestController
@RequestMapping("/sysAdminUser")
public class SysAdminUserController extends BaseController {

    @Autowired
    private ISysAdminUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:admins:list")
    @ApiOperation("获取后台用户列表")
    @GetMapping("/list")
    public TableDataInfo list(SysAdminUser user) {
        startPage();
        List<SysAdminUser> list = userService.selectUserList(user);
        list.forEach(u -> {
            List<SysRole> sysRoles = roleService.selectUserRolesByUserId(u.getUserId());
            u.setRoles(sysRoles);
            List<Long> ids = new ArrayList<>();
            for (SysRole sysRole : sysRoles) {
                Long roleId = sysRole.getRoleId();
                ids.add(roleId);
                String roleName = u.getRoleName();
                String roleKey = u.getRoleKey();
                // 组装角色名称
                u.setRoleName(StringUtils.isEmpty(roleName) ? sysRole.getRoleName() : sysRole.getRoleName() + "," + roleName);
                // 组装roleIds
                u.setRoleIds(ids);
                // 组装角色keys
                u.setRoleKey(StringUtils.isEmpty(roleKey) ? sysRole.getRoleKey() : sysRole.getRoleKey() + "," + roleKey);
            }
        });
        return getDataTable(list);
    }

    /**
     * 获取除当前用户外的用户列表
     */
    @RequiresPermissions("system:admins:list")
    @ApiOperation("获取除当前用户外的用户列表")
    @GetMapping("/listExcludeCurrent")
    public TableDataInfo listExcludeCurrent(SysAdminUser user) {
        startPage();
        List<SysAdminUser> list = userService.selectUserListExcludeCurrent(user);
        list.forEach(u -> {
            List<SysRole> sysRoles = roleService.selectUserRolesByUserId(u.getUserId());
            u.setRoles(sysRoles);
            List<Long> ids = new ArrayList<>();
            for (SysRole sysRole : sysRoles) {
                Long roleId = sysRole.getRoleId();
                ids.add(roleId);
                String roleName = u.getRoleName();
                String roleKey = u.getRoleKey();
                // 组装角色名称
                u.setRoleName(StringUtils.isEmpty(roleName) ? sysRole.getRoleName() : sysRole.getRoleName() + "," + roleName);
                // 组装roleIds
                u.setRoleIds(ids);
                // 组装角色keys
                u.setRoleKey(StringUtils.isEmpty(roleKey) ? sysRole.getRoleKey() : sysRole.getRoleKey() + "," + roleKey);
            }
        });
        return getDataTable(list);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:admins:query")
    @ApiOperation("根据用户ID获取详细信息")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            throw new ServiceException("用户登录信息无效");
        }
        //userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        List<SysRole> allowedRoles = roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList());
        ;
        ajax.put("roles", allowedRoles);
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            ajax.put(AjaxResult.DATA_TAG, userService.selectUserById(userId));
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
        }
        return ajax;
    }

    /**
     * 获取调试人员列表
     */
    @ApiOperation("获取调试人员列表")
    @GetMapping("/deBuglist")
    public AjaxResult deBuglist() {
        List<SysAdminUser> list = userService.selectByDeBugList();
        return AjaxResult.success(list);
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:admins:query")
    @ApiOperation("根据用户ID获取授权角色")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysAdminUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:admins:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @ApiOperation("用户授权角色")
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("system:admins:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增用户")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysAdminUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        user.setCompanyId(100L);
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:admins:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @ApiOperation("修改用户")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysAdminUser user) {
        user.checkUserAllowed();
        String userName = user.getUserName();
        if (StringUtils.isEmpty(userName)) {
            return AjaxResult.error("修改用户'" + userName + "'失败，用户名为空");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user))) {
            return AjaxResult.error("修改用户'" + userName + "'失败，登录账号已存在");
        }

        // 是否重置token标识
        boolean resetTokenFlag = false;
        // 比较值是否相同前先排序
        List<Long> list = roleService.selectRoleListByUserId(user.getUserId());
        Collections.sort(user.getRoleIds());
        Collections.sort(list);
        if (list != null && list.size() > 0) {
            // 角色发生变更
            String roleIds = StringUtils.join(user.getRoleIds(), ",");
            String roleOldIds = StringUtils.join(list,",");
            if (!roleIds.equals(roleOldIds)) {
                resetTokenFlag = true;
            }
        }
        SysAdminUser sysUsers = userService.selectUserById(user.getUserId());
        if (sysUsers != null) {
            if (!sysUsers.getCompanyId().equals(user.getCompanyId())) {
                resetTokenFlag = true;
            }
        }

        user.setUpdateBy(SecurityUtils.getUsername());
        int i = userService.updateUser(user);
        if (i > 0) {
            updateUserRedis(user.getUserId());
            // 角色变更需重新登录
            if (resetTokenFlag) {
                UserRedisUtils.deleteUserRedis(user);
            }
            return AjaxResult.success();
        }
        return AjaxResult.error("操作失败");
    }


    /**
     * 重置密码
     */
    @RequiresPermissions("system:admins:resetPwd")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @ApiOperation("重置密码")
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysAdminUser user) {
        user.checkUserAllowed();
        String newPassword = "";
        try {
            newPassword = RsaUtils.decryptByPrivateKey(user.getPassword());
        } catch (Exception e) {
            return AjaxResult.error("操作失败");
        }
        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        user.setUpdateBy(SecurityUtils.getUsername());
        int result = userService.updatePassword(user);
        if (result > 0) {
            UserRedisUtils.deleteUserRedisWhenDisabled(user);
            return AjaxResult.success();
        }
        return AjaxResult.error("操作失败");
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:admins:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @ApiOperation("状态修改")
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysAdminUser user) {
        user.checkUserAllowed();
        if (user.getUserId().equals(SecurityUtils.getLoginUser().getUserId())) {
            throw new ServiceException("不允许对自己进行操作");
        }
        user.setUpdateBy(SecurityUtils.getUsername());

        int i = userService.updateUserStatus(user);
        if (i > 0) {
            updateUserRedis(user.getUserId());
            return AjaxResult.success();
        }
        return AjaxResult.error("操作失败");
    }

    @ApiOperation("导出管理员列表")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:admins:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAdminUser user) {
        List<SysAdminUser> list = userService.selectUserList(user);
        ExcelUtil<SysAdminUser> util = new ExcelUtil<>(SysAdminUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @ApiOperation("导入管理员列表")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:admins:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        if (!ExcelUtil.checkSuffix(file)) {
            return AjaxResult.error("文件类型不符合要求");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            throw new ServiceException("用户登录信息失效");
        }

        ExcelUtil<SysAdminUser> util = new ExcelUtil<>(SysAdminUser.class);
        List<SysAdminUser> userList = util.importExcel(file.getInputStream());
        for (SysAdminUser userToImport : userList) {
            String validateMessage = userToImport.validForImport();
            if (validateMessage != null) {
                return AjaxResult.error("用户" + userToImport.getNickName() + "信息校验错误：" + validateMessage);
            }
            if (StringUtils.isNotEmpty(userToImport.getRoleName())) {
                String[] split = userToImport.getRoleName().split(",");
                for (String name : split) {
                    Long roleId = roleService.selectRoleId(name);
                    if (roleId == null) {
                        throw new ServiceException("未能找到" + name + "角色相关信息");
                    }
                }
            }

            userToImport.setCompanyId(100L);
        }

        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @ApiOperation("导入管理员列表模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysAdminUser> util = new ExcelUtil<>(SysAdminUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    private void updateUserRedis(Long userId) {
        // 获取用户当前登录的子系统权限
        SysUser user = SecurityUtils.getUser();
        // 查询最新的用户信息
        SysAdminUser adminUser = userService.selectUserById(userId);
        if (adminUser != null) {

            List<SysRole> sysRoles = roleService.selectUserRolesByUserId(userId);
            adminUser.setRoles(sysRoles);

            List<Long> ids = new ArrayList<>();
            for (SysRole sysRole : sysRoles) {
                Long roleId = sysRole.getRoleId();
                ids.add(roleId);
                // 组装roleIds
                adminUser.setRoleIds(ids);
            }

            if (adminUser.getCompanyId() != null) {
                SysDept dept = deptService.selectDeptById(adminUser.getCompanyId());
                adminUser.setCompanyName(dept.getDeptName());
            }
        }
        UserRedisUtils.updateUserRedisInfo(adminUser);
        UserRedisUtils.deleteUserRedisWhenDisabled(adminUser);
    }

    @ApiOperation("根据企业获取指定企业管理员用户")
    @GetMapping("/getCompanyId/{companyId}")
    public List<SysAdminUser> getCompanyId(@PathVariable("companyId") Long companyId) {
        return userService.getCompanyUser(companyId);
    }

    /**
     * 更新后台用户信息
     *
     * @param sysAdminUser
     * @return
     */
    @InnerAuth
    @PostMapping("/updateAdminUser")
    public int updateAdminUser(@RequestBody SysAdminUser sysAdminUser) {
        return userService.updateUserLoginInfo(sysAdminUser);
    }

    /**
     * 根据id获取用户信息
     * @param userId
     * @return
     */
    @InnerAuth
    @GetMapping("/selectUserById/{userId}")
    public SysAdminUser selectUserById(@PathVariable("userId") Long userId) {
        return userService.selectUserById(userId);
    }

    /**
     * 更新token中用户信息
     */
    @InnerAuth
    @PostMapping("/resUserInfo")
    public int resUserInfo(@RequestBody SysAdminUser users) {
        LoginUser loginUser = new LoginUser();
        // 权限字符集合
        Map<String,Set<String>> map = permissionService.getMenuPermission(users);
        loginUser.setAllPermissions(map);
        loginUser.setSystemPermissions(map);
        // 放置角色标识
        List<SysRole> roleList = roleService.selectUserRolesByUserId(users.getUserId());
        users.setRoles(roleList);
        loginUser.setUserId(users.getUserId());
        loginUser.setUser(users);
        return userService.resUserInfo(loginUser);
    }

    /**
     * 获取物流或实施人员列表
     *
     * @param roleFlag 0物流 1实施
     * @return
     */
    @GetMapping("/userList")
    public AjaxResult userList(@RequestParam("roleFlag") Integer roleFlag) {
        List<SysAdminUser> list;
        if (roleFlag == 0) {
            list = userService.selectUserListByRoleKey("debugSys:order:logistics");
        } else {
            list = userService.selectUserListByRoleKey("debugSys:order:currayOut");
        }
        // 测试添加：没有手机号的先过滤掉
        List<SysAdminUser> collect = list.stream().filter(s -> s.getPhonenumber() != null && !s.getPhonenumber().equals("")).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 查询当前用户所拥有的子系统标识
     * @param userId
     * @return
     */
    @InnerAuth
    @GetMapping("/selectSubSystem")
    public List<String> selectSubSystemFlag(@RequestParam Long userId) {
        return userService.selectSubSystemFlag(userId);
    }

    /**
     * 查询当前用户所拥有的子系统标识
     * @param ids
     * @return
     */
    @InnerAuth
    @PostMapping("/selectUserByIds")
    public List<SysUserVo> selectUserByIds(@RequestBody List<Long> ids) {
        List<SysAdminUser> list = userService.selectUserByIds(ids);
        // 将SysAdminUser 转 SysUserVo
        return list.stream().map(sysAdminUser -> {
            return BeanUtil.copyProperties(sysAdminUser, SysUserVo.class);
        }).collect(Collectors.toList());
    }

    /**
     * 查询企业管理员用户列表
     */
    @ApiOperation(value = "查询企业管理员用户列表")
    @GetMapping("/companyUserList")
    public TableDataInfo selectCompanyUser(SysAdminUserDto adminUserDto){
        if (adminUserDto.getCompanyId()!=null && adminUserDto.getCompanyId()==100){
            throw new ServiceException("操作失败,禁止访问根目录下的管理员");
        }
        startPage();
        List<SysAdminUser> list = userService.selectCompanyUserList(adminUserDto);
        list.forEach(u -> {
            List<SysRole> sysRoles = roleService.selectUserRolesByUserId(u.getUserId());
            u.setRoles(sysRoles);
            List<Long> ids = new ArrayList<>();
            for (SysRole sysRole : sysRoles) {
                Long roleId = sysRole.getRoleId();
                ids.add(roleId);
                String roleName = u.getRoleName();
                String roleKey = u.getRoleKey();
                // 组装角色名称
                u.setRoleName(StringUtils.isEmpty(roleName) ? sysRole.getRoleName() : sysRole.getRoleName() + "," + roleName);
                // 组装roleIds
                u.setRoleIds(ids);
                // 组装角色keys
                u.setRoleKey(StringUtils.isEmpty(roleKey) ? sysRole.getRoleKey() : sysRole.getRoleKey() + "," + roleKey);
            }
        });
        return getDataTable(list);
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("system:admins:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除用户")
    @DeleteMapping("/{userId}")
    public AjaxResult remove(@PathVariable Long userId) {
        if (userId.equals(SecurityUtils.getLoginUser().getUserId())) {
            throw new ServiceException("不允许删除当前登录用户");
        }
        int row = userService.deleteUserById(userId);
        if(row > 0){
            SysAdminUser sysAdminUser = new SysAdminUser();
            sysAdminUser.setUserId(userId);
            UserRedisUtils.deleteUserRedis(sysAdminUser);
        }
        return toAjax(row);
    }
}
