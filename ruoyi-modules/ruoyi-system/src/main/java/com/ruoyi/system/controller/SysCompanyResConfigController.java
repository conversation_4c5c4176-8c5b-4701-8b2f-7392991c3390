package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.entity.domain.system.SysCompanyResConfig;
import com.ruoyi.system.service.ISysCompanyResConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 企业资源配置Controller
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@RestController
@RequestMapping("/compResConfig")
public class SysCompanyResConfigController extends BaseController {
    @Autowired
    private ISysCompanyResConfigService sysCompanyResConfigService;


    /**
     * 获取企业资源配置详细信息
     */
//    @RequiresPermissions("system:compResConfig:query")
    @GetMapping(value = "/{companyId}")
    public AjaxResult getInfo(@PathVariable("companyId") Long companyId) {
        SysCompanyResConfig companyResConfig = new SysCompanyResConfig();
        companyResConfig.setCompanyId(companyId);
        List<SysCompanyResConfig> resConfigs = sysCompanyResConfigService.selectSysCompanyResConfigList(companyResConfig);

        return AjaxResult.success(resConfigs);
    }

    /**
     * 新增企业资源配置
     */
//    @RequiresPermissions("system:compResConfig:edit")
    @Log(title = "新增企业资源配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody List<SysCompanyResConfig> list) {
        try {
            if (list == null || list.size() == 0){
                return AjaxResult.error("资源配置为空");
            }
            SysCompanyResConfig sysCompanyResConfig = list.get(0);
            Long companyId = sysCompanyResConfig.getCompanyId();
            if (companyId == null){
                return AjaxResult.error("请为企业资源配置绑定企业信息");
            }
            // 先删后增
            sysCompanyResConfigService.deleteSysCompanyResConfigByCompanyId(companyId);
            for (SysCompanyResConfig companyResConfig : list) {
                companyResConfig.setCompanyId(companyId);
                sysCompanyResConfigService.insertSysCompanyResConfig(companyResConfig);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("操作失败，请检查资源类型是否正确");
        }
    }

    /**
     * 获取企业参数信息
     */
    @ApiOperation("获取企业参数信息")
    @GetMapping("/getCompResConfig/{type}")
    public AjaxResult getCompResConfig(@PathVariable("type") String type) {
        Long companyId = SecurityUtils.getCurrentCompanyId();
        SysCompanyResConfig resConfig = sysCompanyResConfigService.selectCompResConfigDataByType(companyId, type);
        if (resConfig == null){
            String size = DictUtils.getDictValue(SysDictDataEnum.COMPANY_RES_CONFIG.getName(), type);
            if (size != null){
                return AjaxResult.success(Integer.parseInt(size));
            }else {
                throw new RuntimeException("操作失败，请检查资源类型是否正确");
            }
        }
        return AjaxResult.success(resConfig.getResourceLimit().intValue());
    }

    /**
     * 查询公司资源配置数据配置
     * @param companyId
     * @param type
     * @return
     */
    @InnerAuth
    @GetMapping("/selectCompResConfigDataByType")
    public Integer selectCompResConfigDataByType(@RequestParam("companyId") Long companyId, @RequestParam("type") String type){
        SysCompanyResConfig resConfig = sysCompanyResConfigService.selectCompResConfigDataByType(companyId, type);
        if (resConfig == null){
            Map<String, String> dictData = DictUtils.getDictCacheMap(SysDictDataEnum.COMPANY_RES_CONFIG.getName());
            String value = dictData.get(type);
            if (value != null){
                return Integer.parseInt(value);
            }else {
                throw new RuntimeException("操作失败，请检查资源类型是否正确");
            }
        }
        return resConfig.getResourceLimit().intValue();
    }

}
