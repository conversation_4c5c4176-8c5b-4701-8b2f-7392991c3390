package com.ruoyi.system.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.entity.vo.TreeSelect;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.knowledgebase.api.RemoteTbUserInfoService;
import com.ruoyi.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sysDept")
@Api(tags = "部门管理API")
public class SysDeptController extends BaseController {
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private RemoteTbUserInfoService tbUserInfoService;
    /**
     * 新增部门
     */

    /**
     * 获取部门列表
     */
    @ApiOperation("获取部门列表")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptByList(dept);
        return AjaxResult.success(depts);
    }


    /**
     * 查询部门列表（排除节点）
     */
    @ApiOperation("查询部门列表（排除节点）")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        SysDept sysDept = new SysDept();
        Long companyId = SecurityUtils.getCurrentCompanyId();
        if (companyId != null && !companyId.equals(100)) {
            sysDept.setDeptId(companyId);
        }
        List<SysDept> depts = deptService.selectDeptByList(sysDept);
        Iterator<SysDept> it = depts.iterator();
        while (it.hasNext()) {
            SysDept d = (SysDept) it.next();
            if (d.getDeptId().intValue() == deptId
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + "")) {
                it.remove();
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @ApiOperation("根据部门编号获取详细信息")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表（查询公司的话只能查询到公司下的部门）
     */
    @ApiOperation("获取部门下拉树列表")
    @GetMapping(value = {"/treeselect/{parentDeptId}", "/treeselect"})
    public AjaxResult treeSelectByParent(@PathVariable(value = "parentDeptId", required = false) Long parentDeptId, SysDept dept) {
        List<SysDept> depts = deptService.selectDeptListSort(dept);
        List<TreeSelect> treeSelectList = deptService.buildDeptTreeSelect(depts);
        if (parentDeptId != null && treeSelectList != null) {
            TreeSelect targetSelect = deptService.findTreeSelectByID(treeSelectList, parentDeptId);
            if (targetSelect != null) {
                return AjaxResult.success(targetSelect.getChildren());
            } else {
                return AjaxResult.success(null);
            }
        }
        return AjaxResult.success(treeSelectList);
    }

    /**
     * 部门树，展示公司和部门
     */
    @ApiOperation("获取部门下拉树列表")
    @GetMapping(value = {"/deptTree/{parentDeptId}", "/deptTree"})
    public AjaxResult deptTree(@PathVariable(value = "parentDeptId", required = false) Long parentDeptId, SysDept dept) {
        List<SysDept> depts = null;

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        if (currentCompanyId == null) {
            depts = deptService.selectDeptListSort(dept);
        } else {
            depts = deptService.selectSumDept(currentCompanyId);
        }

        List<TreeSelect> treeSelectList = deptService.buildDeptTreeSelect(depts);
        if (parentDeptId != null && treeSelectList != null) {
            TreeSelect targetSelect = deptService.findTreeSelectByID(treeSelectList, parentDeptId);
            if (targetSelect != null) {
                List<TreeSelect> treeSelects = new ArrayList<>();
                treeSelects.add(targetSelect);
                return AjaxResult.success(treeSelects);
            } else {
                return AjaxResult.success(null);
            }
        }
        return AjaxResult.success(treeSelectList);
    }

    /**
     * 根据父级Id获取公司列表
     *
     * @param parentId
     * @return
     */
    @ApiOperation("根据父级Id获取公司列表")
    @GetMapping("/selectOneLevelDepts/{parentDeptId}")
    public AjaxResult selectOneLevelDepts(@PathVariable(value = "parentDeptId") Long parentId) {
        List<SysDept> list = deptService.selectOneLevelDepts(parentId);
        return AjaxResult.success(list);
    }

    /**
     * 加载对应角色部门列表树
     */
    @ApiOperation("加载对应角色部门列表树")
    @GetMapping(value = "/roleDeptTreeselect/{roleId}")
    public AjaxResult roleDeptTreeselect(@PathVariable("roleId") Long roleId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.buildDeptTreeSelect(depts));
        return ajax;
    }

    /**
     * 获取公司列表(带有后台管理系统)
     * 标识是否需要展示后台管理系统 flag  0 :需要后台管理系统
     */
    @ApiOperation("获取公司列表")
    @GetMapping("/companies")
    public AjaxResult listAllCompany(SysDept dept) {
        dept.setDeptId(SysDept.rootDeptID);
        dept.setDelFlag("0");
        List<SysDept> depts = deptService.selectDeptListByNotId(dept);
        return AjaxResult.success(depts);
    }

    /**
     * 按树形结构获取公司列表
     */
    @ApiOperation("按树形结构获取公司列表")
    //@PreAuthorize("@ss.hasAnyRoles('admin,opsAdmin,comAdmin")
    @GetMapping("/companyTree")
    public AjaxResult treeSelectCompany(@RequestParam(required = false) boolean includeDisabled) {
        SysDept company = deptService.selectDeptById(SysDept.rootDeptID);
        List<SysDept> allCompanyList = deptService.selectAllCompanyList(includeDisabled);
        if (allCompanyList != null) {
            company.setChildren(allCompanyList);
        }
        return AjaxResult.success(company);
    }


    /**
     * 新增部门
     */
    @ApiOperation("新增部门")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(SecurityUtils.getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @ApiOperation("修改部门")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        Long deptId = dept.getDeptId();
        // 校验是否可以正常变更部门
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        // 定义editFlag
        boolean editFlag = false;
        // 查询部门历史信息
        SysDept sysDept = deptService.selectDeptById(deptId);
        if (sysDept.getParentId() != null && dept.getParentId() != null && !sysDept.getParentId().equals(dept.getParentId())) {
            // 查询当前部门的下级
            List<SysDept> deptList = deptService.selectOneLevelDepts(deptId);
            if (deptList != null && deptList.size() > 0) {
                return AjaxResult.error("操作失败,当前部门下存在下级部门,不允许变更上级");
            }
            editFlag = true;
        }
        dept.setUpdateBy(SecurityUtils.getUsername());
        int row = deptService.updateDept(dept);
        if (row > 0 && editFlag) {
            // 修改用户部门信息
            String ancestors = dept.getAncestors();
//            sysAppUserService.updateUserDeptIdsByDeptId(ancestors, deptId);
        }
        return toAjax(row);
    }

    /**
     * 删除部门
     */
    @ApiOperation("删除部门")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return AjaxResult.error("存在下级部门,不允许删除");
        }
        if (tbUserInfoService.getUserCount(deptId, SecurityConstants.INNER) > 0) {
            return AjaxResult.error("存在员工绑定该部门,不允许删除");
        }
        //删除
        if (deptService.deleteDeptById(deptId) > 0) {
            this.recursionDelete(deptId);
        }
        return AjaxResult.success();
    }

    /**
     * @param deptId
     * @return
     */
    public AjaxResult recursion(Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            SysDept sysDept = new SysDept();
            sysDept.setParentId(deptId);
            List<SysDept> depts = deptService.selectDeptList(sysDept);
            if (depts.size() == 0) {
                return AjaxResult.success();
            }
            for (SysDept sysDept1 : depts) {
                if (deptService.checkDeptExistUser(sysDept1.getDeptId())) {
                    return AjaxResult.error("部门存在用户,不允许删除");
                }
                this.recursion(sysDept1.getDeptId());
            }


        }
        return AjaxResult.success();
    }

    /**
     * @param deptId
     * @return
     */
    public void recursionDelete(Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            SysDept sysDept = new SysDept();
            sysDept.setParentId(deptId);
            List<SysDept> depts = deptService.selectDeptList(sysDept);
            if (depts.size() > 0) {
                for (SysDept sysDept1 : depts) {
                    deptService.deleteDeptById(sysDept1.getDeptId());
                    this.recursion(sysDept1.getDeptId());
                }
            }


        }
    }

    /**
     * 导入企业列表模板
     *
     * @param response
     */
    @ApiOperation("导入企业列表模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysDept> util = new ExcelUtil<SysDept>(SysDept.class);
        util.importTemplateExcel(response, "企业数据");
    }

    @ApiOperation("导入企业列表")
    @Log(title = "部门管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        if (ExcelUtil.checkSuffix(file)) {
            ExcelUtil<SysDept> util = new ExcelUtil<SysDept>(SysDept.class);
            List<SysDept> deptList = util.importExcel(file.getInputStream());
            for (SysDept sysDept : deptList) {
                if (sysDept == null) {
                    return AjaxResult.error("导入失败");
                }
                if (!StringUtils.isNotEmpty(sysDept.getDeptName())) {
                    return AjaxResult.error("部门名称不能为空");
                }
            }
            String operName = SecurityUtils.getUsername();
            String message = deptService.importData(deptList, updateSupport, operName);
            return AjaxResult.success(message);
        }
        return AjaxResult.error("文件类型不符合要求");
    }

    @ApiOperation("导出公司列表")
    @Log(title = "部门管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDept dept) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        dept.setDeptId(currentCompanyId);
        List<SysDept> list = deptService.selectDeptList(dept);
        ExcelUtil<SysDept> util = new ExcelUtil<SysDept>(SysDept.class);
        util.exportExcel(response, list, "企业（部门）数据");
    }

    /**
     * 内部调用：根据部门id获取部门信息
     *
     * @param deptId
     * @return
     */
    @InnerAuth
    @GetMapping("/selectDeptById/{deptId}")
    public SysDept selectDeptById(@PathVariable("deptId") Long deptId) {
        return deptService.selectDeptById(deptId);
    }

    /**
     * 内部调用：根据手机号获取部门信息
     *
     * @param phone
     * @return
     */
    @InnerAuth
    @GetMapping("/selectDeptByPhone/{phone}")
    public List<SysDept> selectDeptByPhone(@PathVariable("phone") String phone) {
        return deptService.selectDeptByPhone(phone);
    }

    /**
     * 内部调用：根据部门ID字符串获取相应的部门名字
     *
     * @param code
     * @return
     */
    @InnerAuth
    @GetMapping(value = "/deptNames/{code}", produces = "application/json")
    public String deptNames(@PathVariable("code") String code) {
//        SysAppUser appUser = sysAppUserService.selectUserByUserName(code);
//        if (appUser != null) {
//            String depts = deptService.selectDeptNamesByIds(appUser.getDepts(), false, "/");
//            return depts;
//        }
        return null;
    }
}
