package com.ruoyi.system.controller;


import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysDivision;
import com.ruoyi.system.service.ISysDivisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 区域信息Controller
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Api(tags = "区域信息API")
@RestController
@RequestMapping("/sysDivision")
public class SysDivisionController extends BaseController {
    @Autowired
    private ISysDivisionService sysDivisionService;

    /**
     * 查询区域信息列表
     */
    @ApiOperation(value = "查询区域信息列表")
    @RequiresPermissions("system:division:list")
    @GetMapping("/list")
    public AjaxResult list(SysDivision sysDivision) {
        List<SysDivision> list = sysDivisionService.selectList(sysDivision);
        return getNewDataTable(list);
    }

    /**
     * 导出区域信息列表
     */
    @ApiOperation(value = "导出区域信息列表")
    @RequiresPermissions("system:division:export")
    @Log(title = "区域信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDivision sysDivision) {
        List<SysDivision> list = sysDivisionService.selectList(sysDivision);
        for (SysDivision division : list) {
            if (division.getParentId()!=0){
                SysDivision sysDivisionInfo = sysDivisionService.selectById(division.getParentId());
                division.setParentName(sysDivisionInfo.getName());
            }else{
                division.setParentName("无上级区域");
            }
        }
        ExcelUtil<SysDivision> util = new ExcelUtil<SysDivision>(SysDivision.class);
        util.exportExcel(response, list, "区域信息数据");
    }

    /**
     * 导入区域列表模板
     * @param response
     */
    @ApiOperation(value = "导入课程章节列表模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysDivision> util = new ExcelUtil<SysDivision>(SysDivision.class);
        util.importTemplateExcel(response, "区域信息数据");
    }

    /**
     * 导出区域信息列表
     */
    @ApiOperation(value = "导出区域信息列表")
    @RequiresPermissions("system:division:import")
    @Log(title = "区域信息", businessType = BusinessType.EXPORT)
    @Transactional
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        if (!ExcelUtil.checkSuffix(file)) {
            throw new ServiceException("导入失败,文件类型不符合要求");
        }
        ExcelUtil<SysDivision> util = new ExcelUtil<SysDivision>(SysDivision.class);
        List<SysDivision> divisionList = util.importExcel(file.getInputStream());
        if (StringUtils.isEmpty(divisionList) || divisionList.size() < 1) {
            throw new ServiceException("导入失败,当前文件没有数据");
        }
        //基础数据校验
        for (int i = 0; i < divisionList.size(); i++) {
            SysDivision sysDivision = divisionList.get(i);
            String message = sysDivision.validForImport();
            String parentName = sysDivision.getParentName();
            String provinceName = sysDivision.getProvinceName();
            if( message!=null){
                throw new ServiceException("第" + (i + 2) + "行，" +message);
            }
            String divisionName = sysDivision.getName().substring(sysDivision.getName().length()-1);
            if (divisionName.equals("省")) {
                sysDivision.setParentId(0l);
                SysDivision division = sysDivisionService.selectDivisionName(sysDivision.getName(), sysDivision.getParentId());
                if (division!=null){
                    throw new ServiceException("第" + (i + 2) + "行，" +"区域信息已存在");
                }
                sysDivisionService.insert(sysDivision);
                continue;
            }
            SysDivision division =null;
            if (StringUtils.isNotEmpty(provinceName)) {
                 division = sysDivisionService.selectDivisionName(provinceName,null);
                if (division==null){
                    throw new ServiceException("第" + (i + 2) + "行，" +"系统中不存在:"+provinceName);
                }
            }
            SysDivision divisions = null;
            if (division == null){
                 divisions = sysDivisionService.selectDivisionName(parentName,null);
            }else{
                 divisions = sysDivisionService.selectDivisionName(parentName,division.getId());
            }
            //根据市级来定位

            if (divisions==null){
                throw new ServiceException("第" + (i + 2) + "行，" +"系统中"+provinceName+"不存在区县级:"+parentName);
            }
            sysDivision.setParentId(divisions.getId());
            SysDivision sysDivisionOne = sysDivisionService.selectDivisionName(sysDivision.getName(),divisions.getId());
            if (sysDivisionOne!=null){
                throw new ServiceException("第" + (i + 2) + "行，" +"系统中的"+provinceName+"/"+parentName+"下已经存在"+sysDivision.getName());
            }
            sysDivisionService.insert(sysDivision);
        }
        return AjaxResult.success();
    }

    /**
     * 获取区域信息详细信息
     */
    @ApiOperation(value = "获取区域信息详细信息")
    @RequiresPermissions("system:division:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysDivisionService.selectById(id));
    }

    /**
     * 新增区域信息
     */
    @ApiOperation(value = "新增区域信息")
    @RequiresPermissions("system:division:add")
    @Log(title = "区域信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDivision sysDivision) {
        SysDivision division = sysDivisionService.selectDivisionName(sysDivision.getName(), sysDivision.getParentId());
        if (division!=null){
            return AjaxResult.error("区域信息已存在");
        }
        return toAjax(sysDivisionService.insert(sysDivision));
    }

    /**
     * 修改区域信息
     */
    @ApiOperation(value = "修改区域信息")
    @RequiresPermissions("system:division:edit")
    @Log(title = "区域信息", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysDivision sysDivision) {
        SysDivision division = sysDivisionService.selectDivisionName(sysDivision.getName(), sysDivision.getParentId());
        if (division!=null && !sysDivision.getId().equals(division.getId())){
            return AjaxResult.error("区域信息已存在");
        }
        return toAjax(sysDivisionService.update(sysDivision));
    }

    /**
     * 删除区域信息
     */
    @ApiOperation(value = "删除区域信息")
    @RequiresPermissions("system:division:remove")
    @Log(title = "区域信息", businessType = BusinessType.DELETE)
    @PostMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(deleteByParentId(id));
    }

    private int deleteByParentId(Long parentId) {
        List<SysDivision> childList = sysDivisionService.selectByParent(parentId);
        if (childList != null) {
            for (SysDivision division : childList) {
                deleteByParentId(division.getId());
            }
        }
        return sysDivisionService.deleteById(parentId);
    }

    @ApiOperation(value = "H5:查询区域信息")
    @GetMapping("/byParent")
    public AjaxResult listByParent(Long id) {
        List<SysDivision> list = sysDivisionService.selectByParent(id);
        if (StringUtils.isNotEmpty(list)) {
            list.forEach(division -> {
                List<SysDivision> childrenList = sysDivisionService.selectByParent(division.getId());
                if (StringUtils.isNotEmpty(childrenList)) {
                    division.setChildren(childrenList.stream().map(s -> s.getId()).collect(Collectors.toList()));
                }
            });
        }
        return AjaxResult.success(list);
    }


    /**
     * 区域下拉选框
     * @return
     */
    @GetMapping("/divisionTree")
    public AjaxResult divisionTree(){
        List<SysDivision> list = sysDivisionService.selectList(new SysDivision());
        List<SysDivision> divisions = buildDeptTree(list);
        return AjaxResult.success(divisions);
    }

    public List<SysDivision> buildDeptTree(List<SysDivision> depts) {
        List<SysDivision> returnList = new ArrayList<SysDivision>();
        List<Long> tempList = new ArrayList<Long>();
        for (SysDivision dept : depts) {
            tempList.add(dept.getId());
        }
        for (Iterator<SysDivision> iterator = depts.iterator(); iterator.hasNext(); ) {
            SysDivision dept = (SysDivision) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDivision> list, SysDivision t) {
        // 得到子节点列表
        List<SysDivision> childList = getChildList(list, t);
        t.setChildrenList(childList);
        for (SysDivision tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDivision> getChildList(List<SysDivision> list, SysDivision t) {
        List<SysDivision> tlist = new ArrayList<SysDivision>();
        Iterator<SysDivision> it = list.iterator();
        while (it.hasNext()) {
            SysDivision n = (SysDivision) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDivision> list, SysDivision t) {
        return getChildList(list, t).size() > 0;
    }
}
