package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysDownload;
import com.ruoyi.system.service.ISysDownloadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 安装包下载Controller
 *
 * <AUTHOR>
 * @date 2022-05-26
 */
@Api(tags = "安装包下载管理API")
@RestController
@RequestMapping("/sysDownload")
public class SysDownloadController extends BaseController {
    @Autowired
    private ISysDownloadService sysDownloadService;

    /**
     * 查询安装包下载列表
     */
    @ApiOperation("查询安装包下载列表")
    @RequiresPermissions("system:download:list")
    @GetMapping("/list")
    public AjaxResult list(SysDownload sysDownload) {
        startPage();
        List<SysDownload> list = sysDownloadService.selectList(sysDownload);
        return getNewDataTable(list);
    }

    /**
     * 获取安装包下载详细信息
     */
    @ApiOperation("获取安装包下载详细信息")
    @RequiresPermissions("system:download:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysDownloadService.selectById(id));
    }

    /**
     * 新增安装包下载
     */
    @ApiOperation("新增安装包下载")
    @RequiresPermissions("system:download:add")
    @Log(title = "安装包下载", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDownload sysDownload) {
        if (StringUtils.isEmpty(sysDownload.getAppType()) || StringUtils.isEmpty(sysDownload.getDownloadUrl())) {
            return AjaxResult.error("应用类型或下载地址为空");
        }
        sysDownload.setUploadTime(DateUtils.getNowDate());

        SysDownload existedDownload = sysDownloadService.selectByAppType(sysDownload.getAppType());
        if (existedDownload != null) {
            sysDownload.setId(existedDownload.getId());

            return toAjax(sysDownloadService.update(sysDownload));
        }

        return toAjax(sysDownloadService.insert(sysDownload));
    }

    /**
     * 修改安装包下载
     */
    @ApiOperation("修改安装包下载")
    @RequiresPermissions("system:download:edit")
    @Log(title = "安装包下载", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysDownload sysDownload) {
        sysDownload.setUploadTime(DateUtils.getNowDate());
        return toAjax(sysDownloadService.update(sysDownload));
    }

    /**
     * 删除安装包下载
     */
    @ApiOperation("删除安装包下载")
    @RequiresPermissions("system:download:remove")
    @Log(title = "安装包下载", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysDownloadService.deleteByIds(ids));
    }

    @ApiOperation("根据应用程序类别获取安装包信息")
    @GetMapping("/info/{appType}")
    public AjaxResult getDownload(@PathVariable String appType) {
        SysDownload download = sysDownloadService.selectByAppType(appType);
        if (download != null) {
            return AjaxResult.success(download);
        }

        return AjaxResult.error("下载链接不存在");
    }
}
