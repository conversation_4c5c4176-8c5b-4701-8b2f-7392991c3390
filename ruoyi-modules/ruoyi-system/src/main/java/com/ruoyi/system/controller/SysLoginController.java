package com.ruoyi.system.controller;


import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.*;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RunningTime;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.serviceUtils.SysPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(value = "登录", tags = "登录注册组API")
@RestController
@Slf4j
public class SysLoginController {
    @Autowired
    private ISysAdminUserService sysAdminUserService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取当前用户信息
     */
    @ApiOperation(value = "获取当前用户信息（内部接口）")
    @InnerAuth
    @GetMapping("/user/info/{username}")
    public SysAdminUser info(@PathVariable("username") String username) {
        return sysAdminUserService.selectUserByUserName(username);
    }

    /**
     * PC后台用户查询角色集合
     *
     * @param sysAdminUser 用户名
     * @return 结果
     */
    @ApiOperation(value = "PC后台用户查询角色集合（内部接口）")
    @InnerAuth
    @PostMapping("/user/getRolePermission")
    public Set<String> getRolePermission(@RequestBody SysAdminUser sysAdminUser) {
        return permissionService.getRolePermission(sysAdminUser);
    }

    /**
     * PC后台用户查询权限集合
     *
     * @param sysAdminUser 用户名
     * @return 结果
     */
    @ApiOperation(value = "PC后台用户查询权限集合（内部接口）")
    @InnerAuth
    @PostMapping("/user/getMenuPermission")
    public Map<String, Set<String>> getMenuPermission(@RequestBody SysAdminUser sysAdminUser) {
        return permissionService.getMenuPermission(sysAdminUser);
    }


    /**
     * 更新暂存信息到缓存
     *
     * @return 用户信息
     */
    @ApiOperation(value = "更新用户信息")
    @GetMapping("/refreshInfo")
    public AjaxResult refreshInfo(Long companyId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 恢复初始状态
        if (companyId == 100) {
            loginUser.setSubSystem(Constants.SUB_SYSTEM_WEB);
            loginUser.setToggleCompanyId(null);
        } else {
            loginUser.setSubSystem(Constants.SUB_SYSTEM_COM);
            // 切换企业后更新暂存信息
            loginUser.setToggleCompanyId(companyId);
        }
        tokenService.refreshToken(loginUser);
        return AjaxResult.success();
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation(value = "获取用户信息")
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long companyId) {
        // 最新的数据
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        String switchFlag = null;
        if (!SecurityUtils.isAdministrator()) {
            // 获取当前用户角色
            List<SysRole> roleList = user.getRoles();
            List<Long> ids = roleList.stream()
                    .map(SysRole::getRoleId)
                    .collect(Collectors.toList());
            String strIds = StringUtils.join(ids, ",");
            List<String> menuList = menuService.selectMenuByRoleIds(strIds);
            if (menuList != null && menuList.size() > 0) {
                switchFlag = StringUtils.join(menuList, ",");
            }
        } else {
            switchFlag = "";
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", loginUser.getUser());
        ajax.put("roles", loginUser.getRoles());
        ajax.put("permissions", loginUser.getPermissions());
        ajax.put("systemPermissions", loginUser.getSystemPermissions());
        ajax.put("switchFlag", switchFlag);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation(value = "获取路由信息")
    @GetMapping("/getRouters/{subSystem}")
    public AjaxResult getRouters(@PathVariable("subSystem") String subSystem) {

        if (StringUtils.isEmpty(subSystem)) {
            return AjaxResult.error("子系统标识不允许为空");
        }
        // 根据前端权限字符查询菜单对象
        SysMenu sysMenu = menuService.selectByPerms(subSystem);
        if (sysMenu == null || sysMenu.getMenuId() == 0) {
            return AjaxResult.error("未查到此子系统，或子系统不可用");
        }
        // 查询菜单树
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId, sysMenu.getMenuId(), subSystem);
        if (menus.size() == 0) {
            return AjaxResult.error("请先分配权限");
        }
        // 给超管过滤首页
        List<SysMenu> menuList = new ArrayList<>();
        if (SecurityUtils.getLoginUser().isSuperAdmin()) {
            menuList = SuperHomeFilter(menus);
        } else {
            menuList.addAll(menus);
        }
        return AjaxResult.success(menuService.buildMenus(menuList, sysMenu.getMenuId()));
    }

    /**
     * 获取三级路由信息
     *
     * @return 路由信息
     */
    @ApiOperation(value = "获取三级路由信息")
    @GetMapping("/subMenu/{subSystem}")
    @RunningTime
    public AjaxResult subMenu(@PathVariable("subSystem") String subSystem) {
        if (StringUtils.isEmpty(subSystem)) {
            return AjaxResult.error("子系统标识不允许为空");
        }
        // 根据前端权限字符查询菜单对象
        SysMenu sysMenu = menuService.selectByPerms(subSystem);
        if (sysMenu == null || sysMenu.getMenuId() == 0) {
            return AjaxResult.error("未查到此子系统，或子系统不可用");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<SysMenu> menus = null;

        if (loginUser.isSuperAdmin()) {
            menus = menuService.selectMenuTreeAll(sysMenu.getMenuId());
        } else {
            List<Long> ids = SecurityUtils.getRoleIds();
            String roleIds = StringUtils.join(ids, ",");
            menus = menuService.selectGetUserMenu(roleIds, sysMenu.getMenuId(), subSystem);
        }
        if (menus == null || menus.size() == 0) {
            return AjaxResult.error("请先分配权限");
        }
        // 给超管过滤首页
        List<SysMenu> menuList = new ArrayList<>();
        if (SecurityUtils.getLoginUser().isSuperAdmin()) {
            menuList = SuperHomeFilter(menus);
        } else {
            menuList.addAll(menus);
        }
        // 更新缓存
        reToken(subSystem);
        return AjaxResult.success(menuService.buildMenus(menuList, sysMenu.getMenuId()));
    }


    /**
     * 超管首页过滤：超管指定返回运维首页
     * Homepage filtering
     *
     * @param menus
     * @return
     */
    public List<SysMenu> SuperHomeFilter(List<SysMenu> menus) {
        // 初始化顶级 菜单/目录 集合
        List<SysMenu> menuList = new ArrayList<>();
        // 超管拿到所有菜单进入for过滤
        for (SysMenu menu : menus) {
            // 首页没有子级并且是非平台级的跳过组装
            if (menu.getChildren().size() == 0 && menu.getMenuLevel().equals("0")) {
                continue;
            }
            menuList.add(menu);
        }
        return menuList;
    }

    /**
     * 更新用户缓存
     * @param subSystem
     */
    public void reToken(String subSystem){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        loginUser.setSubSystem(subSystem);
        tokenService.refreshToken(loginUser);
    }

}
