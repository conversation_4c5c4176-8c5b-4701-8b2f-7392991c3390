package com.ruoyi.system.controller;


import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysMenu;
import com.ruoyi.common.entity.vo.TreeSelect;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sysMenu")
@Api(tags = "菜单信息API")
public class SysMenuController extends BaseController {
    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取菜单列表
     */
    @ApiOperation("获取菜单列表")
    @RequiresPermissions("system:menu:list")
    @GetMapping("/list")
    public AjaxResult list(SysMenu menu) {
        List<SysMenu> menus = menuService.selectMenuList(menu, SecurityUtils.getUserId());
        return AjaxResult.success(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @ApiOperation("根据菜单编号获取详细信息")
    @RequiresPermissions("system:menu:query")
    @GetMapping(value = "/{menuId}")
    public AjaxResult getInfo(@PathVariable Long menuId) {
        SysMenu sysMenu = menuService.selectMenuById(menuId);
        return AjaxResult.success(sysMenu);
    }

    /**
     * 获取菜单下拉树列表
     */
    @ApiOperation("获取菜单下拉树列表")
    @GetMapping("/treeselect")
    public AjaxResult treeselect(SysMenu menu) {
        // 查询出所有非平台级的数据
        List<SysMenu> menus = menuService.selectNonPlatformMenuList(menu);
        return AjaxResult.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @ApiOperation("获取菜单下拉树列表")
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        // 查询出所有非平台级的数据
        List<SysMenu> menus = menuService.selectNonPlatformMenuList(new SysMenu());
        // 组装前端所需要的格式
        AjaxResult ajax = AjaxResult.success();
        //根据角色权限获取菜单id
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        //获取全部的组装后的数据
        List<TreeSelect> treeSelects = menuService.buildMenuTreeSelect(menus);
        ajax.put("menus", treeSelects);
        return ajax;
    }

    /**
     * 新增菜单
     */
    @ApiOperation("新增菜单")
    @RequiresPermissions("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysMenu menu) {
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        if (menu.getParentId() != 0) {
            //只有三级添加的时候jump有值
            if (StringUtils.isNotEmpty(menu.getIsJump()) && menu.getIsJump().equals("0")) {
                //初始化二级菜单全部都不许跳转，所以不担心父ID为0的情况
                List<SysMenu> menus = menuService.selectParenId(menu.getParentId());
                if (menus.size() != 0) {
                    return AjaxResult.error("该同级目录下已经存有默认跳转的子目录:" + menus.get(0).getMenuName());
                }
            }
            SysMenu sysMenu = menuService.selectMenuById(menu.getParentId());
            if (sysMenu.getParentId() == 0) {
                menu.setSubSystem(sysMenu.getPerms());
            }else{
                menu.setSubSystem(sysMenu.getSubSystem());
            }

        }

        menu.setCreateBy(SecurityUtils.getUsername());
        int rows = menuService.insertMenu(menu);
        menuService.resetConfigCache();
        return toAjax(rows);
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @RequiresPermissions("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysMenu menu) {
        SysMenu sysMenu = menuService.selectMenuById(menu.getParentId());
        if (sysMenu != null) {
            //判断是否为3级菜单
            if (sysMenu.getParentId() != 0 && sysMenu.getMenuType().equals("C")) {
                //数据应为原二级目录的path拼接上默认跳转的三级菜单的path
                menu.setRedirect(sysMenu.getPath() + "/" + menu.getPath());
            }
        }
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (menu.getMenuId().equals(menu.getParentId())) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        if (menu.getParentId() != 0L) {
            // 查询修改前的数据
            SysMenu menuById = menuService.selectMenuById(menu.getMenuId());
            // 查询下面存在子级
            List<SysMenu> menuList = menuService.selectSubMenu(menu.getMenuId());
            // 确认权限发生变动
            if (StringUtils.isNotEmpty(menuList) && !menu.getMenuLevel().equals(menuById.getMenuLevel())) {
                // 更新所有子级
                menuService.updateMenuLevel(menu.getMenuId(), menu.getMenuLevel(), menuList);
            }
            if (StringUtils.isNotEmpty(menu.getIsJump()) && menu.getIsJump().equals(UserConstants.ALLOW)) {
                //初始化二级菜单全部都不许跳转，所以不担心父ID为0的情况
                List<SysMenu> menus = menuService.selectParenId(menu.getParentId());
                if (menus.size() != 0 && !menu.getMenuId().equals(menus.get(0).getMenuId())) {
                    menus.get(0).setIsJump("1");
                    menuService.updateMenu(menus.get(0));
                }
            }
        }
        menu.setUpdateBy(SecurityUtils.getUsername());
        int rows = menuService.updateMenu(menu);
        menuService.resetConfigCache();
        return toAjax(rows);
    }

    /**
     * 删除菜单
     */
    @ApiOperation("删除菜单")
    @RequiresPermissions("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public AjaxResult remove(@PathVariable("menuId") Long menuId) {
        // 递归删除菜单及其子菜单
        deleteMenuRecursively(menuId);
        menuService.resetConfigCache();
        return AjaxResult.success();
    }

    /**
     * 递归删除菜单及其子菜单
     * @param menuId 菜单ID
     */
    private void deleteMenuRecursively(Long menuId) {
        // 查询所有子菜单
        List<SysMenu> childMenus = menuService.selectChilds(menuId);
        
        // 递归删除所有子菜单
        for (SysMenu childMenu : childMenus) {
            deleteMenuRecursively(childMenu.getMenuId());
        }
        
        // 获取当前菜单信息
        SysMenu currentMenu = menuService.selectMenuById(menuId);
        if (currentMenu != null) {
            // 删除当前菜单的角色分配
            menuService.deleteMenuRole(menuId, currentMenu.getMenuLevel());
            // 删除当前菜单
            menuService.deleteMenuById(menuId);
        }
    }
}