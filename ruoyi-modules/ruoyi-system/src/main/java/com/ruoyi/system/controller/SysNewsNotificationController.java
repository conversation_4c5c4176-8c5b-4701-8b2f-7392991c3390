package com.ruoyi.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteDictDataService;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysNewsNotification;
import com.ruoyi.system.service.ISysNewsNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 消息管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Api(tags = "消息管理API")
@RestController
@RequestMapping("/sysNewsNotification")
public class SysNewsNotificationController extends BaseController {
    @Autowired
    private ISysNewsNotificationService sysNewsNotificationService;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    /**
     * 查询消息管理列表
     */
    @ApiOperation(value = "查询消息管理列表")
    @RequiresPermissions("system:notification:list")
    @GetMapping("/list")
    public AjaxResult list(SysNewsNotification sysNewsNotification) {
        startPage();
        List<SysNewsNotification> list = sysNewsNotificationService.selectList(sysNewsNotification);
        return getNewDataTable(list);
    }

    /**
     * 导出消息管理列表
     */
    @ApiOperation(value = "导出消息管理列表")
    @RequiresPermissions("system:notification:export")
    @Log(title = "消息管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysNewsNotification sysNewsNotification) {
        List<SysNewsNotification> list = sysNewsNotificationService.selectList(sysNewsNotification);
        list.forEach(K -> {
            K.setTypeName(remoteDictDataService.selectDictLabel("sys_notification_type", K.getType().toString(), SecurityConstants.INNER));
        });
        ExcelUtil<SysNewsNotification> util = new ExcelUtil<SysNewsNotification>(SysNewsNotification.class);
        util.exportExcel(response, list, "消息管理数据");
    }

    @ApiOperation(value = "导入消息管理列表")
    @Log(title = "消息管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:notification:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            throw new ServiceException("用户登录信息失效");
        }
        ExcelUtil<SysNewsNotification> util = new ExcelUtil<>(SysNewsNotification.class);
        List<SysNewsNotification> sysNewsNotifications = util.importExcel(file.getInputStream());
        for (SysNewsNotification sysNewsNotification : sysNewsNotifications) {
            //查询通知类型是否存在
            Integer type = sysNewsNotification.getType();
            if (type == null) {
                throw new ServiceException("通知类型不存在");
            }
            SysNewsNotification newsNotification = sysNewsNotificationService.selectByType(type);
            if (newsNotification != null) {
                newsNotification.setContent(sysNewsNotification.getContent());
                newsNotification.setTitle(sysNewsNotification.getTitle());
                // BeanUtils.copyProperties(sysNewsNotification, newsNotification);
                sysNewsNotificationService.update(newsNotification);
            } else {
                sysNewsNotificationService.insert(sysNewsNotification);
            }


        }
        return AjaxResult.success();


    }


    /**
     * 获取消息管理详细信息
     */
    @ApiOperation(value = "获取消息管理详细信息")
    @RequiresPermissions("system:notification:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysNewsNotificationService.selectById(id));
    }

    /**
     * 新增消息管理
     */
    @ApiOperation(value = "新增消息管理")
    @RequiresPermissions("system:notification:add")
    @Log(title = "消息管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysNewsNotification sysNewsNotification) {
        List<SysNewsNotification> sysNewsNotificationList = sysNewsNotificationService.getBaseMapper().selectList(new QueryWrapper<SysNewsNotification>().lambda().eq(SysNewsNotification::getType, sysNewsNotification.getType()));
        if (sysNewsNotificationList.size() > 0) {
            return AjaxResult.error("该类型已存在");
        }


        return toAjax(sysNewsNotificationService.insert(sysNewsNotification));
    }

    /**
     * 修改消息管理
     */
    @ApiOperation(value = "修改消息管理")
    @RequiresPermissions("system:notification:edit")
    @Log(title = "消息管理", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysNewsNotification sysNewsNotification) {
        return toAjax(sysNewsNotificationService.update(sysNewsNotification));
    }

    /**
     * 删除消息管理
     */
    @ApiOperation(value = "删除消息管理")
    @RequiresPermissions("system:notification:remove")
    @Log(title = "消息管理", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysNewsNotificationService.deleteByIds(ids));
    }

    /**
     * 导入消息列表模板
     */
    @ApiOperation(value = "导入消息列表模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysNewsNotification> util = new ExcelUtil<SysNewsNotification>(SysNewsNotification.class);
        util.importTemplateExcel(response, "企业数据");
    }
}

