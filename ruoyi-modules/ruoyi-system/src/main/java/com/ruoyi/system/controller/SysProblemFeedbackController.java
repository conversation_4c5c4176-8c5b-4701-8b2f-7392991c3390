package com.ruoyi.system.controller;

import com.ruoyi.common.core.annotation.RepeatSubmit;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysProblemFeedback;
import com.ruoyi.system.service.ISysProblemFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.ruoyi.common.security.utils.SecurityUtils.getUserId;

/**
 * 问题反馈Controller
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@RestController
@RequestMapping("/sysProblemFeedback")
@Api(tags = "问题反馈API")

public class SysProblemFeedbackController extends BaseController {
    @Autowired
    private ISysProblemFeedbackService sysProblemFeedbackService;

    /**
     * 查询问题反馈列表
     */
    @ApiOperation(value = "查询问题反馈列表")
    @RequiresPermissions("system:feedback:list")
    @GetMapping("/list")
    public AjaxResult list(SysProblemFeedback sysProblemFeedback) {
        startPage();
        List<SysProblemFeedback> list = sysProblemFeedbackService.selectList(sysProblemFeedback);

        return getNewDataTable(list);
    }

    /**
     * 导出问题反馈列表
     */
    @ApiOperation(value = "导出问题反馈列表")
    @RequiresPermissions("system:feedback:export")
    @Log(title = "问题反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysProblemFeedback sysProblemFeedback) {
        List<SysProblemFeedback> list = sysProblemFeedbackService.selectList(sysProblemFeedback);
        ExcelUtil<SysProblemFeedback> util = new ExcelUtil<SysProblemFeedback>(SysProblemFeedback.class);
        util.exportExcel(response, list, "问题反馈数据");
    }

    /**
     * 获取问题反馈详细信息
     */
    @ApiOperation(value = "获取问题反馈详细信息")
    @RequiresPermissions("system:feedback:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysProblemFeedbackService.selectById(id));
    }

    /**
     * 新增问题反馈
     */
    @ApiOperation(value = "新增问题反馈")
    @RequiresPermissions("system:feedback:add")
    @Log(title = "问题反馈", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public AjaxResult add(@RequestBody SysProblemFeedback sysProblemFeedback) {
        return toAjax(sysProblemFeedbackService.insert(sysProblemFeedback));
    }

    /**
     * 修改问题反馈
     */
    @ApiOperation(value = "修改问题反馈")
    @RequiresPermissions("system:feedback:edit")
    @Log(title = "问题反馈", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysProblemFeedback sysProblemFeedback) {
        return toAjax(sysProblemFeedbackService.update(sysProblemFeedback));
    }

    /**
     * 删除问题反馈
     */
    @ApiOperation(value = "删除问题反馈")
    @RequiresPermissions("system:feedback:remove")
    @Log(title = "问题反馈", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysProblemFeedbackService.deleteByIds(ids));
    }


    /**
     * App:新增问题反馈
     */
    //  @RequiresPermissions("system:feedback:add")
    @Log(title = "问题反馈", businessType = BusinessType.INSERT)
    @ApiOperation("App:问题反馈")
    @PostMapping("/appAdd")
    public AjaxResult appAdd(@RequestBody SysProblemFeedback sysProblemFeedback) {
        Long userId = getUserId();
        if (userId != null) {
            sysProblemFeedback.setUserId(userId);
            return toAjax(sysProblemFeedbackService.insert(sysProblemFeedback));
        }
        return AjaxResult.error("用户信息失效");
    }
}
