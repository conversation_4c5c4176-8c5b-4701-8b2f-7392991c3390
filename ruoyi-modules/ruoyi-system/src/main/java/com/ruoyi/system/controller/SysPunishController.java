package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysComplaint;
import com.ruoyi.common.entity.domain.SysPunish;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysComplaintService;
import com.ruoyi.system.service.ISysPunishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 处罚信息Controller
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@RestController
@RequestMapping("/punish")
public class SysPunishController extends BaseController
{
    @Autowired
    private ISysPunishService sysPunishService;

    @Autowired
    private ISysComplaintService sysComplaintService;

    /**
     * 查询处罚信息列表
     */
    @RequiresPermissions("system:punish:list")
    @GetMapping("/list")
    public TableDataInfo list(SysPunish sysPunish)
    {
        startPage();
        List<SysPunish> list = sysPunishService.selectSysPunishList(sysPunish);
        return getDataTable(list);
    }

    /**
     * 导出处罚信息列表
     */
    @RequiresPermissions("system:punish:export")
    @Log(title = "处罚信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPunish sysPunish)
    {
        List<SysPunish> list = sysPunishService.selectSysPunishList(sysPunish);
        ExcelUtil<SysPunish> util = new ExcelUtil<SysPunish>(SysPunish.class);
        util.exportExcel(response, list, "处罚信息数据");
    }

    /**
     * 获取处罚信息详细信息
     */
    @RequiresPermissions("system:punish:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sysPunishService.selectSysPunishById(id));
    }

    /**
     * 新增处罚信息
     */
    @RequiresPermissions("system:punish:add")
    @Log(title = "处罚信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPunish sysPunish)
    {
        return toAjax(sysPunishService.insertSysPunish(sysPunish));
    }

    /**
     * 修改处罚信息
     */
    @RequiresPermissions("system:punish:edit")
    @Log(title = "处罚信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPunish sysPunish)
    {
        return toAjax(sysPunishService.updateSysPunish(sysPunish));
    }

    /**
     * 删除处罚信息
     */
    @RequiresPermissions("system:punish:remove")
    @Log(title = "处罚信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysPunishService.deleteSysPunishByIds(ids));
    }

    /**
     * 处罚列表
     * @param targetObjId
     * @return
     */
    @RequiresPermissions("system:user:punishInfo")
    @GetMapping("/query/{targetObjId}")
    public AjaxResult selectUserPunish(@PathVariable("targetObjId") Long targetObjId){
        SysPunish sysPunish = new SysPunish();
        sysPunish.setTargetObjId(targetObjId);
        startPage();
        List<SysPunish> list = sysPunishService.selectSysPunishList(sysPunish);
        return getNewDataTable(list);
    }

    /**
     * 解除处罚
     */
    @PostMapping("/relieve")
    public AjaxResult relieve(@PathVariable("complaintNo") String complaintNo){
        SysPunish sysPunish=sysPunishService.selectSysPunish(complaintNo);
        if (sysPunish==null){
            return AjaxResult.error("未能查询到订单记录");
        }
        sysPunish.setPunishRelieveTime(DateUtils.getNowDate());
        sysPunish.setPunishRelieveName(SecurityUtils.getUsername());
        int i = sysPunishService.updateSysPunish(sysPunish);
        if (i>0){
            SysComplaint sysComplaint = sysComplaintService.selectByComplaintNo(complaintNo);
            sysComplaint.setComplaintStatus(2);
            sysComplaintService.updateSysComplaint(sysComplaint);
        }
        return AjaxResult.success(i);
    }

    /**
     * 处罚信息
     *
     * @param sysPunish 处罚信息
     * @return 结果
     */
    @InnerAuth
    @PostMapping("/insert")
    public int insert(@RequestBody SysPunish sysPunish){
        return sysPunishService.insertSysPunish(sysPunish);
    }
}
