package com.ruoyi.system.controller;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.utils.UserRedisUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.serviceUtils.SysPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@Api(tags = "角色信息API")
@RestController
@RequestMapping("/sysRole")
public class SysRoleController extends BaseController {
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysAdminUserService userService;

    @Autowired
    private ISysMenuService menuService;

    @ApiOperation("获取角色列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/list")
    public TableDataInfo list(SysRole role) {
        startPage();
        List<SysRole> list = roleService.selectRoleList(role);
        return getDataTable(list);
    }

    @ApiOperation("角色导出")
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:role:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRole role) {
        List<SysRole> list = roleService.selectRoleList(role);
        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @ApiOperation("角色详细信息")
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId) {
        roleService.checkRoleDataScope(roleId);
        return AjaxResult.success(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @ApiOperation("新增角色")
    @RequiresPermissions("system:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRole role) {
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return AjaxResult.error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
            return AjaxResult.error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        // 查询所有首页菜单id
        List<Long> menuIds = menuService.getByMenuId();
        // 要修改的菜单id
        List<Long> list = Arrays.asList(role.getMenuIds());
        // 过滤出选择的首页
        List<Long> collect = list.stream().filter(menuIds::contains).collect(Collectors.toList());
        // 首页的数量不能超过1个
        if (collect.size() > 1) {
            return AjaxResult.error("角色配置进入的首页只能选择一个");
        }
        role.setCreateBy(SecurityUtils.getUsername());
        return toAjax(roleService.insertRole(role));

    }

    /**
     * 修改保存角色
     */
    @ApiOperation("修改角色")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return AjaxResult.error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
            return AjaxResult.error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        // 查询所有首页菜单id
        List<Long> menuIds = menuService.getByMenuId();
        // 要修改的菜单id
        List<Long> list = Arrays.asList(role.getMenuIds());
        // 过滤出选择的首页
        List<Long> collect = list.stream().filter(menuIds::contains).collect(Collectors.toList());
        // 首页的数量不能超过1个
        if (collect.size() > 1) {
            return AjaxResult.error("角色配置进入的首页只能选择一个");
        }

        role.setUpdateBy(SecurityUtils.getUsername());

        if (roleService.updateRole(role) > 0) {
            // 更新缓存用户权限
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (StringUtils.isNotNull(loginUser.getUser()) && !SecurityUtils.isAdmin(loginUser.getUserId())) {
                SysUser user = loginUser.getUser();
                Map<String, Set<String>> map = permissionService.getMenuPermission(user);
                loginUser.setAllPermissions(map);
                loginUser.setSystemPermissions(map);
                SysAdminUser adminUser = userService.selectUserByUserName(loginUser.getUser().getUserName());
                List<SysRole> roles = roleService.selectUserRolesByUserId(adminUser.getUserId());
                adminUser.setRoles(roles);
                List<Long> ids = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
                adminUser.setRoleIds(ids);
                loginUser.setUser(adminUser);
                tokenService.setLoginUser(loginUser);
            }
            return AjaxResult.success();
        }
        return AjaxResult.error("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
    }

    /**
     * 修改保存数据权限
     */
    @ApiOperation("修改保存数据权限")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public AjaxResult dataScope(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        int rows = roleService.authDataScope(role);
        deleteUserRedis(role.getRoleId(), null);
        return toAjax(rows);
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        role.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @ApiOperation("批量删除角色信息")
    @RequiresPermissions("system:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds) {
        return toAjax(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    @ApiOperation("获取角色选择框列表")
    @RequiresPermissions("system:role:query")
    @GetMapping("/optionselect")
    public AjaxResult optionselect() {
        return AjaxResult.success(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    @ApiOperation("查询已分配用户角色列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/authUser/allocatedList/{roleId}")
    public TableDataInfo allocatedList(SysAdminUser user, @PathVariable(name = "roleId") Long roleId) {
        startPage();
        List<SysAdminUser> list = userService.selectAllocatedList(user, roleId);
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    @ApiOperation("查询未分配用户角色列表")
    @RequiresPermissions("system:role:list")
    @GetMapping("/authUser/unallocatedList/{roleId}")
    public TableDataInfo unallocatedList(SysAdminUser user, @PathVariable(name = "roleId") Long roleId) {
        startPage();
        List<SysAdminUser> list = userService.selectUnallocatedList(user, roleId);
        return getDataTable(list);
    }

    /**
     * 取消授权用户
     */
    @ApiOperation("取消授权用户")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole) {
        int rows = roleService.deleteAuthUser(userRole);
        deleteUserRedis(userRole.getRoleId(), userRole.getUserId());
        return toAjax(rows);
    }

    /**
     * 批量取消授权用户
     */
    @ApiOperation("批量取消授权用户")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds) {
        int rows = roleService.deleteAuthUsers(roleId, userIds);
        deleteBatchUserRedis(roleId, userIds);
        return toAjax(rows);
    }

    /**
     * 批量选择用户授权
     */
    @ApiOperation("批量选择用户授权")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public AjaxResult selectAuthUserAll(Long roleId, Long[] userIds) {
        int rows = roleService.insertAuthUsers(roleId, userIds);
        deleteBatchUserRedis(roleId, userIds);
        return toAjax(rows);
    }

    /**
     * 删除用户redis
     *
     * @param roleId
     * @param userId
     */
    private void deleteUserRedis(Long roleId, Long userId) {
        SysAdminUser sysAdminUser = new SysAdminUser();
        sysAdminUser.setUserId(userId);
        // 查询操作用户，删除用户redis
        List<SysAdminUser> adminUserList = userService.selectAllocatedList(sysAdminUser, roleId);
        for (SysAdminUser adminUser : adminUserList) {
            UserRedisUtils.deleteUserRedis(adminUser);
        }
    }

    /**
     * 批量删除用户redis
     *
     * @param roleId
     * @param userIds
     */
    private void deleteBatchUserRedis(Long roleId, Long[] userIds) {
        for (Long userId : userIds) {
            SysAdminUser sysAdminUser = new SysAdminUser();
            sysAdminUser.setUserId(userId);
            // 查询操作用户，删除用户redis
            List<SysAdminUser> adminUserList = userService.selectAllocatedList(sysAdminUser, roleId);
            for (SysAdminUser adminUser : adminUserList) {
                UserRedisUtils.deleteUserRedis(adminUser);
            }
        }
    }

    /**
     * 根据用户Id查询角色列表（Feign）
     * @param userId
     * @param source
     * @return
     */
    @InnerAuth
    @GetMapping("/selectUserRolesByUserId/{userId}")
    public List<SysRole> selectUserRolesByUserId(@PathVariable("userId") Long userId,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        return roleService.selectUserRolesByUserId(userId);
    }
}
