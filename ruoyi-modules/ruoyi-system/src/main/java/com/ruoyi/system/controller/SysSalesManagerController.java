package com.ruoyi.system.controller;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysSalesManager;
import com.ruoyi.system.service.ISysSalesManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 销售经理信息Controller
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Api(tags = "销售经理信息API")
@RestController
@RequestMapping("/sysSalesManager")
public class SysSalesManagerController extends BaseController {
    @Autowired
    private ISysSalesManagerService sysSalesManagerService;

    /**
     * 查询销售经理信息列表
     */
    @ApiOperation(value = "查询销售经理信息列表")
    @RequiresPermissions("system:salesManager:list")
    @GetMapping("/list")
    public AjaxResult list(SysSalesManager sysSalesManager) {
        startPage();
        List<SysSalesManager> list = sysSalesManagerService.selectList(sysSalesManager);
        return getNewDataTable(list);
    }

    /**
     * 导出销售经理信息列表
     */
    @ApiOperation(value = "导出销售经理信息列表")
    @RequiresPermissions("system:salesManager:export")
    @Log(title = "销售经理信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSalesManager sysSalesManager) {
        List<SysSalesManager> list = sysSalesManagerService.selectList(sysSalesManager);
        ExcelUtil<SysSalesManager> util = new ExcelUtil<SysSalesManager>(SysSalesManager.class);
        util.exportExcel(response, list, "销售经理信息数据");
    }

    /**
     * 获取销售经理信息详细信息
     */
    @ApiOperation(value = "获取销售经理信息详细信息")
    @RequiresPermissions("system:salesManager:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysSalesManagerService.selectById(id));
    }

    /**
     * 新增销售经理信息
     */
    @ApiOperation(value = "新增销售经理信息")
    @RequiresPermissions("system:salesManager:add")
    @Log(title = "销售经理信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSalesManager sysSalesManager) {
        SysSalesManager salesManager = sysSalesManagerService.selectSalesManager(sysSalesManager.getPhone(),sysSalesManager.getName());
        if (salesManager!=null){
            return AjaxResult.error("添加失败,手机号已被使用");
        }
        return toAjax(sysSalesManagerService.insert(sysSalesManager));
    }

    /**
     * 修改销售经理信息
     */
    @ApiOperation(value = "修改销售经理信息")
    @RequiresPermissions("system:salesManager:edit")
    @Log(title = "销售经理信息", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysSalesManager sysSalesManager) {
        SysSalesManager salesManager = sysSalesManagerService.selectSalesManager(sysSalesManager.getPhone(),sysSalesManager.getName());
        if (salesManager!=null && !salesManager.getId().equals(sysSalesManager.getId()) ){
            return AjaxResult.error("添加失败,手机号已被使用");
        }
        return toAjax(sysSalesManagerService.update(sysSalesManager));
    }

    /**
     * 删除销售经理信息
     */
    @ApiOperation(value = "删除销售经理信息")
    @RequiresPermissions("system:salesManager:remove")
    @Log(title = "销售经理信息", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysSalesManagerService.deleteByIds(ids));
    }


    @ApiOperation(value = "H5:销售经理列表")
    @GetMapping("/all")
    public AjaxResult listAll() {
        SysSalesManager salesManager = new SysSalesManager();
        salesManager.setEnable(0);
        List<SysSalesManager> list = sysSalesManagerService.selectList(salesManager);
        return AjaxResult.success(list);
    }

    /**
     * 导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysSalesManager> util = new ExcelUtil<SysSalesManager>(SysSalesManager.class);
        util.importTemplateExcel(response, "客服经理");
    }

    /**
     * 导入
     * @param file
     * @return
     * @throws Exception
     */
    @RequiresPermissions("system:salesManager:import")
    @Log(title = "销售经理导入", businessType = BusinessType.IMPORT)
    @Transactional
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        if (!ExcelUtil.checkSuffix(file)) {
            throw new ServiceException("文件类型不符合要求");
        }
        ExcelUtil<SysSalesManager> util = new ExcelUtil<>(SysSalesManager.class);
        List<SysSalesManager> salesManagers = util.importExcel(file.getInputStream());
        if (StringUtils.isEmpty(salesManagers) || salesManagers.size() < 1) {
            return AjaxResult.error("导入失败,当前文件没有数据");
        }
        for (int i = 0; i < salesManagers.size(); i++) {
            String message = salesManagers.get(i).validForImport();
            if (message!=null){
                throw new ServiceException("第" + (i + 2) + "行，" + message);
            }
            SysSalesManager sysSalesManager = sysSalesManagerService.selectSalesManager(salesManagers.get(i).getPhone(),salesManagers.get(i).getName());
            if (sysSalesManager!=null) {
                throw new ServiceException("第" + (i + 2) + "行，" + "手机号已被使用");
            }
            sysSalesManagerService.insert(salesManagers.get(i));
        }
        return AjaxResult.success();
    }

    /**
     * 查询销售经理信息列表
     */
    @ApiOperation(value = "查询客户经理下拉框")
    @GetMapping("/comboBox")
    public AjaxResult comboBox(SysSalesManager sysSalesManager) {
        List<SysSalesManager> list = sysSalesManagerService.selectList(sysSalesManager);
        return getNewDataTable(list);
    }
}
