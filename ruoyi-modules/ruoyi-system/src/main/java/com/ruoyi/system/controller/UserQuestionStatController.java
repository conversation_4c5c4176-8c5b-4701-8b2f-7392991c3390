package com.ruoyi.system.controller;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.UserQuestionStat;
import com.ruoyi.system.service.IUserQuestionStatService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 用户问答统计Controller
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
@Slf4j
@RestController
@RequestMapping("/questionStat")
public class UserQuestionStatController extends BaseController
{
    @Autowired
    private IUserQuestionStatService userQuestionStatService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询用户问答统计列表
     */
    @RequiresPermissions("system:stat:list")
    @GetMapping("/list")
    public TableDataInfo list(UserQuestionStat userQuestionStat)
    {
        startPage();
        List<UserQuestionStat> list = userQuestionStatService.selectUserQuestionStatList(userQuestionStat);
        return getDataTable(list);
    }

    /**
     * 导出用户问答统计列表
     */
    @RequiresPermissions("system:stat:export")
    @Log(title = "用户问答统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserQuestionStat userQuestionStat)
    {
        List<UserQuestionStat> list = userQuestionStatService.selectUserQuestionStatList(userQuestionStat);
        ExcelUtil<UserQuestionStat> util = new ExcelUtil<UserQuestionStat>(UserQuestionStat.class);
        util.exportExcel(response, list, "用户问答统计数据");
    }

    /**
     * 获取用户问答统计详细信息
     */
    @RequiresPermissions("system:stat:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(userQuestionStatService.selectUserQuestionStatById(id));
    }

    /**
     * 新增用户问答统计
     */
    @RequiresPermissions("system:stat:add")
    @Log(title = "用户问答统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserQuestionStat userQuestionStat)
    {
        return toAjax(userQuestionStatService.insertUserQuestionStat(userQuestionStat));
    }

    /**
     * 修改用户问答统计
     */
    @RequiresPermissions("system:stat:edit")
    @Log(title = "用户问答统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserQuestionStat userQuestionStat)
    {
        return toAjax(userQuestionStatService.updateUserQuestionStat(userQuestionStat));
    }

    /**
     * 删除用户问答统计
     */
    @RequiresPermissions("system:stat:remove")
    @Log(title = "用户问答统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userQuestionStatService.deleteUserQuestionStatByIds(ids));
    }

    /**
     * 定时任务 汇总统计
     */
//    @GetMapping("/addStat")
    @Scheduled(cron = "0 5 0 * * ?")
    public void addStat(){
        log.info("synchronize data ...");
        // 初始化数据存储集合
        List<UserQuestionStat> list = new ArrayList<>();
        // 用户Key
        String date = DateUtils.parseDateToStr("yyyy-MM-dd",DateUtils.advanceDate(-1));
        Collection<String> keys = redisCache.keys(Constants.USER_QUESTION_COUNT + date + ":*");
        // 获取对应数据
        for (String key : keys) {
            UserQuestionStat stat = redisCache.getCacheObject(key);
            list.add(stat);
        }
        if (list.size()==0){
            log.error("today not synchronize data...");
            return;
        }
        // 同步到库中
        int row = userQuestionStatService.batchInsert(list);
        if (row>0){
            // 删除缓存数据
            redisCache.deleteObject(keys);
        }
    }
}
