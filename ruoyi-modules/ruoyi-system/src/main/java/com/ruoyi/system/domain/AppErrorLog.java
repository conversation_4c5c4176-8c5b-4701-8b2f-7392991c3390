package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * APP崩溃日志对象 app_error_log
 * 
 * <AUTHOR>
 * @date 2022-09-21
 */
@JsonInclude(NON_EMPTY)
@Data
@TableName(value = "app_error_log")
public class AppErrorLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** APP日志ID */
    private Long id;

    /** 应用程序类型 */
    @Excel(name = "应用程序类型")
    private String appCode;

    /** 日志级别（0：崩溃日志，1：严重日志） */
    @Excel(name = "日志级别", readConverterExp = "0=：崩溃日志，1：严重日志")
    private Integer level;

    /** 日志内容 */
    @Excel(name = "日志内容")
    private String log;

    /** 终端IP */
    @Excel(name = "终端IP")
    private String ip;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 日志时间 */
    @Excel(name = "日志时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8", shape = JsonFormat.Shape.STRING)
    private Date logTime;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private Date beginTime;

    /** 截止时间 */
    @Excel(name = "截止时间")
    private Date endTime;
}
