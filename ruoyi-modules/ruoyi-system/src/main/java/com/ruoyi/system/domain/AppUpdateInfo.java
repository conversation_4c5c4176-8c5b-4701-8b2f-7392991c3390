package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 应用程序更新信息对象 app_update_info
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
public class AppUpdateInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用程序类型，android,ios,pc
     */
    @Excel(name = "应用程序类型，android,ios,pc")
    private String appType;

    /**
     * 应用程序版本，1
     */
    @Excel(name = "应用程序版本，1")
    private Integer appVersion;

    /**
     * 是否强制更新
     */
    @Excel(name = "是否强制更新")
    private Boolean forcedUpdate;

    /**
     * 禁用状态
     */
    @Excel(name = "是否禁用状态")
    private Boolean status;

    /**
     * 应用程序下载地址
     */
    @Excel(name = "应用程序下载地址")
    private String appUrl;

    /**
     * 应用程序最低版本，低于此版本的app强制升级
     */
    @Excel(name = "应用程序最低版本，低于此版本的app强制升级")
    private Integer appMinVersion;

    /**
     * 应用程序更新说明
     */
    @Excel(name = "应用程序更新说明")
    private String updateInfo;

    /**
     *
     */
    @TableField(exist = false)
    private String fileType;

}
