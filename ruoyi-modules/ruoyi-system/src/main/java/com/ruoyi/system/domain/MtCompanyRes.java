package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 公司与会议资源对象 mt_company_res
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
public class MtCompanyRes extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公司id
     */
    @Excel(name = "企业id")
    private Long companyId;

    /**
     * 公司名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 会议室名称
     */
    @Excel(name = "会议室名称")
    private String meetingName;

    /**
     * 会议室状态
     */
    @Excel(name = "会议室状态")
    private Integer meetingState;

    /**
     * 会议室容量
     */
    @Excel(name = "会议室容量")
    private Integer meetingVolume;

    /**
     * 会议室开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "会议室开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date meetingStartTime;

    /**
     * 会议室到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "会议室到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date meetingEndTime;

    /**
     * $column.columnComment
     */
    @Excel(name = "会议室到期时间")
    private Integer meetingDuration;

}
