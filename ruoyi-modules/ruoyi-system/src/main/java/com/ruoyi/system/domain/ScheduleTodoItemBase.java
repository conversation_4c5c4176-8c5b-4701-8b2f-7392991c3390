package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

@Data
public abstract class ScheduleTodoItemBase {
    /**
     * 标题
     */
    private String title;

    /**
     * 状态：0:待完成，1：已完成，2：已过期
     */
    private int todoState;

    /**
     * 类别：0：会议，1：考试
     */
    private int todoType;

    @JsonIgnore
    /**
     * 排序日期
     */
    private Date orderDate;
}
