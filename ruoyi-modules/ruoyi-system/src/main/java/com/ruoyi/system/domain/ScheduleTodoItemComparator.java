package com.ruoyi.system.domain;

import java.util.Comparator;

public class ScheduleTodoItemComparator implements Comparator<ScheduleTodoItemBase> {
    private final boolean descending;

    public ScheduleTodoItemComparator(boolean descending) {
        this.descending = descending;
    }

    @Override
    public int compare(ScheduleTodoItemBase o1, ScheduleTodoItemBase o2) {
        int compareResult;
        if (o1 == null || o1.getOrderDate() == null) {
            compareResult = -1;
        } else if (o2 == null || o2.getOrderDate() == null) {
            compareResult = 1;
        } else {
            compareResult = o1.getOrderDate().compareTo(o2.getOrderDate());
        }
        return descending ? 0 - compareResult : compareResult;
    }
}
