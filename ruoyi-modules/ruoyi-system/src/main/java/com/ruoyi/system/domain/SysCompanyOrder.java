package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.annotation.BindField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.RandomStringUtils;

import javax.validation.constraints.Pattern;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 企业订单信息对象 sys_company_order
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_company_order")
public class SysCompanyOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** 主键 */
    private Long id;

    /**
     * 订单单号
     */
    @Excel(name = "订单单号", type = Excel.Type.EXPORT)
    private String serialNo;

    /**
     * 公司名称
     */
    @Excel(name = "*企业名称")
    private String companyName;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderDate;

    /**
     * 公司联系人
     */
    @Excel(name = "*企业联系人")
    private String contactName;

    /**
     * 联系电话
     */
    @Excel(name = "*联系电话")
    @Pattern(regexp = "^$|^1\\d{10}$", message = "企业电话格式不正确")
    private String phone;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 所属行业
     */
    @Excel(name = "所属行业", dictType = "industry_type", width = 30)
    private String industry;

    /**
     * 企业规模
     */
    @Excel(name = "企业规模", dictType = "scale_type")
    private String scale;

    /**
     * 所属行业字段
     */
    @TableField(exist = false)
    private String industryReason;

    /**
     * 企业规模字段
     */
    @TableField(exist = false)
    private String scaleReason;

    /**
     * 套餐时长字段
     */
    @TableField(exist = false)
    private String packageName;

    /**
     * 付费类型字段
     */
    @TableField(exist = false)
    private String payTypeName;

    /**
     * 区域ID
     */
    private String divisionIds;

    /**
     * 所在地区
     */
    @Excel(name = "*所在地区(省/市/县(区)/街道)", width = 30)
    private String divisionNames;

    /**
     * 详细地址
     */
    @Excel(name = "*详细地址")
    private String address;

    /**
     * 快递单号
     */
    @Excel(name = "快递单号", type = Excel.Type.EXPORT)
    private String expressNo;

    /**
     * 安装区域ID
     */
    private String installeDivisionIds;

    /**
     * 安装区域名称
     */
    private String installeDivisionNames;

    /**
     * 安装详细地址
     */
    @Excel(name = "安装详细地址", type = Excel.Type.EXPORT)
    private String installeAddress;

    /**
     * 安装人员姓名
     */
    @Excel(name = "安装人员姓名", type = Excel.Type.EXPORT)
    private String installerName;

    /**
     * 安装人员手机号
     */
    @Excel(name = "安装人员手机号", type = Excel.Type.EXPORT)
    private String installerPhone;

    /**
     * 预计安装时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预计安装时间", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date installTime;

    /**
     * 设备调试人员ID
     */
    private Long debugUserId;

    /**
     * 0：未读、1：已读
     */
    private Integer readFlag;

    /**
     * 签收单照片
     */
    private String receiptImg;

    /**
     * 设备数量
     */
    @Excel(name = "*设备数量")
    private Integer deviceCount;

    /**
     * 销售经理
     */
    @Excel(name = "销售经理ID", type = Excel.Type.EXPORT)
    private Long salesManagerId;

    @TableField(exist = false)
    @BindField(entity = SysSalesManager.class, field = "name", condition = "this.sales_manager_id = id")
    @Excel(name = "*客户经理")
    private String salesManagerName;

    @TableField(exist = false)
    @BindField(entity = SysSalesManager.class, field = "phone", condition = "this.sales_manager_id = id")
    @Excel(name = "*客户经理手机号")
    private String salesManagerPhone;

    @TableField(exist = false)
    @BindField(entity = SysSalesManager.class, field = "company_name", condition = "this.sales_manager_id = id")
    @Excel(name = "客户经理所属公司")
    private String salesManagerCompanyName;


//    /**
//     * 客户经理（导入）
//     */
//    @TableField(exist = false)
//    @Excel(name = "*客户经理")
//    private String salesManagerInfo;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态", type = Excel.Type.EXPORT, readConverterExp = "0=待确认,1=已关闭,2=待备货,3=待指派,4=待完成,5=待审核,6=已完成,7=审核未通过")
    private Integer orderState;


    /**
     * 订单来源
     */
    private Integer orderSource;

    /**
     * 公司ID
     */
    private Long companyId;


    /**
     * 受理时间
     */
    @Excel(name = "受理时间*", prompt = "只写日期,格式示例'2099-01-01'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date processTime;

    /**
     * 套餐时长
     */
    @Excel(name = "套餐时长*", readConverterExp = "12=12个月,24=24个月,36=36个月", combo = {"12个月", "24个月", "36个月"})
    private String packageDuration;

    /**
     * 付款类型
     */
    @Excel(name = "付款类型*", readConverterExp = "0=月付,1=季付,2=年付", combo = {"月付", "季付", "年付"})
    private String payType;

    @JsonIgnore
    public void setOrderStateEnum(SysCompanyOrderState state) {
        if (state != null) {
            this.orderState = state.getValue();
        } else {
            this.orderState = null;
        }
    }

    @JsonIgnore
    public SysCompanyOrderState getOrderStateEnum() {
        if (this.orderState == null) {
            return null;
        }

        return SysCompanyOrderState.getFromStateValue(this.orderState);
    }

    /**
     * 生成订单号
     *
     * @return
     */
    public static String getNewSerialNo() {
        String time = DateUtils.dateTimeFormat(new Date(), DateUtils.YYYYMMDDHHMMSS);
        String randomString = RandomStringUtils.randomNumeric(6);
        return time + randomString;
    }

    /**
     * 订单创建开始时间
     */
    @TableField(exist = false)
    private String startCreatTime;
    /**
     * 订单创建结束时间
     */
    @TableField(exist = false)
    private String endCreatTime;

    /**
     * 备注
     */
    @Excel(name = "备注信息")
    private String remark;

    /**
     * Excel导入验证
     *
     * @return
     */
    public String validForImport() {
        if (StringUtils.isEmpty(this.getCompanyName())) {
            return "企业名称不可为空";
        }
        if (StringUtils.isEmpty(this.getContactName())) {
            return "企业联系人不可为空";
        }
        if (!StringUtils.isEmpty(this.getPhone())) {
            //联系电话
            java.util.regex.Pattern userNamePattern = java.util.regex.Pattern.compile("^1[3|4|5|6|7|8|9][0-9]{9}$");
            Matcher phoneMatcher = userNamePattern.matcher(this.getPhone());
            if (!phoneMatcher.find()) {
                return "联系电话无效";
            }
        } else {
            return "联系电话不可为空";
        }
        if (StringUtils.isNotEmpty(this.getEmail())) {
            java.util.regex.Pattern emailPattern = java.util.regex.Pattern.compile("^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$");
            Matcher matcher = emailPattern.matcher(this.getEmail());
            if (!matcher.find()) {
                return "邮箱格式不正确";
            }

        }
        if (StringUtils.isEmpty(this.getDivisionNames())) {
            return "所在地区不可为空";
        }
        if (this.getDeviceCount() == null
                || StringUtils.isEmpty(this.getDeviceCount().toString())
                || this.getDeviceCount().intValue() <= 0) {
            return "设备数量无效";
        }
        if (StringUtils.isEmpty(this.getSalesManagerName())) {
            return "客户经理名称不可为空";
        }
        if (StringUtils.isEmpty(this.getSalesManagerPhone())) {
            return "客户经理手机号不可为空";
        }
        if (StringUtils.isEmpty(this.getPackageDuration())) {
            return "套餐时长不可为空";
        }
        if (this.getProcessTime()!=null){
            String year=String.format("%tY", this.getProcessTime());
            if (year.length()!=4){
                return "日期格式不对";
            }
            if (DateUtils.resetting(new Date()).after(DateUtils.resetting(this.getProcessTime()))){
                return "受理时间信息无效";
            }
        }else{
            return "受理时间不可为空";
        }
        if (StringUtils.isEmpty(this.getPayType())) {
            return "付费类型不可为空";
        }
//        if (StringUtils.isEmpty(this.getSalesManagerCompanyName())) {
//            return "客户经理所属公司不可为空";
//        }
        return null;
    }


}
