package com.ruoyi.system.domain;

import lombok.ToString;

/**
 * 企业订单事件
 */
@ToString
public enum SysCompanyOrderEvent {

    CreatOrder("1", "订单创建"),
    Prepared("2","填写备货信息，完成备货"),
    Assigned("3","指派调试人员"),
    Debugged("4","完成调试"),
    Reviewed("5","审核通过"),
    NoPassReviewed("6","审核不通过"),
    Closed("7","订单关闭"),
    ConfirmOrder("8","确认订单"),
    PrepareAssigned("9","完成备货并指派人员"),
    Read("10","调试人员已读");

    public final String eventCode;
    public final String eventLabel;

    SysCompanyOrderEvent(String eventCode, String eventLabel){
        this.eventCode = eventCode;
        this.eventLabel = eventLabel;
    }
}
