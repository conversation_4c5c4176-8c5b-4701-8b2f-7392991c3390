package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 企业订单操作记录对象 sys_company_order_log
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_company_order_log")
public class SysCompanyOrderLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** 主键 */
    private Long id;

    /** 企业订单ID */
    @Excel(name = "企业订单ID")
    private Long orderId;

    /** 事件码 */
    @Excel(name = "事件码")
    private String eventNo;

    /** 事件原因 */
    @Excel(name = "事件原因")
    private String eventReason;

    /** 签收单照片 */
    @Excel(name = "签收单照片")
    private String receiptImg;

    /** 创建者用户ID */
    @Excel(name = "创建者用户ID")
    private Long createUserId;

    /**
     * 更新者
     */
    @TableField(exist = false)
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(exist = false)
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



}
