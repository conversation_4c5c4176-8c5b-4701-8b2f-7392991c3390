package com.ruoyi.system.domain;

public enum SysCompanyOrderState {
    ToConfirm(0),// 待确认
    Closed(1),// 已关闭
    ToPrepare(2),// 待备货
    ToAssign(3),// 待指派
    ToComplete(4),// 待完成
    ToReview(5),// 待审核
    Completed(6),// 已完成
    NoPassReview(7);// 审核未通过

    private int stateValue;

    SysCompanyOrderState(int stateValue) {
        this.stateValue = stateValue;
    }

    public int getValue() {
        return this.stateValue;
    }

    public static SysCompanyOrderState getFromStateValue(int stateValue) {
        for (SysCompanyOrderState orderState : SysCompanyOrderState.values()) {
            if (orderState.getValue() == stateValue) {
                return orderState;
            }
        }
        return null;
    }
}
