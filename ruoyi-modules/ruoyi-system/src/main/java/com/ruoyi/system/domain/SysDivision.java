package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.Data;
import lombok.ToString;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 区域信息对象 sys_division
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_division")
public class SysDivision {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** ID */
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "区域名称*",prompt = "区域名称需要填写全称")
    @BindQuery(comparison = Comparison.LIKE)
    private String name;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 上级名称
     */
    @TableField(exist = false)
    @Excel(name = "上级区域名称",width = 30,prompt = "区域名称是省级可不填(全称)")
    private String parentName;

    /**
     * 所属市
     */
    @TableField(exist = false)
    @Excel(name = "所属市名称", width = 50,prompt = "上级名称为区县级须填写城市名称（全称）")
    private String provinceName;

    /**
     * 是否可用
     */
    @Excel(name = "是否可用*",readConverterExp = "0=正常,1=停用",combo = "正常,停用")
    private Integer enable;

    @TableField(exist = false)
    private List<Long> children;

    @TableField(exist = false)
    private List<SysDivision> childrenList;

    public String validForImport() {
        if (StringUtils.isEmpty(this.getName())) {
            return "区域名称不可为空";
        }
        String divisionName = this.getName().substring(this.getName().length()-1);
        if (!divisionName.equals("省")){
            if (StringUtils.isEmpty(this.getParentName())){
                return "上级名称不可为空";
            }else{
                //所属市名称
                String substring = this.getParentName().substring(this.getParentName().length()-1);
                if ((substring.equals("县") || substring.equals("区"))
                        && StringUtils.isEmpty(this.getProvinceName())) {
                    return "上级名称为(县/区)级别，必须填写上级市";
                }
            }
        }
        return null;
    }
}
