package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 安装包下载对象 sys_download
 *
 * <AUTHOR>
 * @date 2022-05-26
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_download")
public class SysDownload implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** 主键ID */
    private Long id;

    /**
     * 应用程序类型
     */
    @Excel(name = "应用程序类型")
    private String appType;

    /**
     * 当前版本号
     */
    @Excel(name = "当前版本号")
    @BindQuery(comparison = Comparison.LIKE)
    private String version;

    /**
     * 下载地址
     */
    @Excel(name = "下载地址")
    private String downloadUrl;

    /**
     * 上传时间
     */
    @Excel(name = "上传时间")
    private Date uploadTime;


}
