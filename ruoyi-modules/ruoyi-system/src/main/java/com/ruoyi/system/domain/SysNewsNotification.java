package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 消息管理对象 sys_news_notification
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_news_notification")
public class SysNewsNotification extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /**
     * 通知标题
     */
    @Excel(name = "通知标题*")
    @BindQuery(comparison = Comparison.LIKE)
    private String title;

    /**
     * 通知类型
     */
    @Excel(name = "通知类型*")
    private Integer type;

    /**
     * 通知内容
     */
    @Excel(name = "通知内容*")
    private String content;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    // @BindField(entity = SysDictData.class,field = "dict_label",condition = "this.type=dict_value")
    private String typeName;


}
