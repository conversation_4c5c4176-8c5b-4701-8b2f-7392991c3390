package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 隐私协议对象 sys_privacy_agreement
 *
 * <AUTHOR>
 * @date 2022-03-15
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_privacy_agreement")
public class SysPrivacyAgreement extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /**
     * 隐私协议内容
     */
    @Excel(name = "隐私协议内容")
    private String content;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private String name;

    private String type;


}
