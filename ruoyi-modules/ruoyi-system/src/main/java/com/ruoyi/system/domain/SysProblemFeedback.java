package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.annotation.BindField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.domain.SysAppUser;
import lombok.Data;
import lombok.ToString;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 问题反馈对象 sys_problem_feedback
 * 
 * <AUTHOR>
 * @date 2022-03-14
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_problem_feedback")
public class SysProblemFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;
@TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /** 问题类型 */
    //@Excel(name = "问题类型")
    private String type;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 图片url */
    @Excel(name = "图片url")
    private String imgUrl;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 用户ID */
   // @Excel(name = "用户ID")
    private Long userId;

    /** 终端类型 */
    @Excel(name = "终端类型")
    private String terminalType;

    @TableField(exist = false)
    private List<String> imgUrls;

    @TableField(exist = false)
    @BindField(entity = SysAppUser.class, field = "user_name", condition = "this.user_id = user_id")
    @Excel(name = "用户")
    private String userName;
}
