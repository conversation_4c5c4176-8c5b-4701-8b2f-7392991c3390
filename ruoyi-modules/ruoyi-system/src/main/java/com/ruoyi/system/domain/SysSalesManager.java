package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.regex.Matcher;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 销售经理信息对象 sys_sales_manager
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_sales_manager")
public class SysSalesManager {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** 编号 */
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名*")
    @NotBlank(message = "姓名不能为空")
    @BindQuery(comparison = Comparison.LIKE)
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Excel(name = "手机号*")
    @Pattern(regexp = "^$|^1\\d{10}$", message = "手机号格式不正确")
    @BindQuery(comparison = Comparison.LIKE)
    private String phone;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 是否可用
     */
    @Excel(name = "是否可用*",combo = "正常,停用",readConverterExp = "0=正常,1=停用")
    private Integer enable;

    /**
     * 获取“用户名/手机号”
     * 用于批量备货模板
     * @return
     */
    public String getUsernameAndPhone(){
        return this.getName()+"/"+this.getPhone();
    }

    /**
     * Excel导入验证
     * @return
     */
    public String validForImport() {
        if (StringUtils.isEmpty(this.getName())) {
            return "名称不可为空";
        }
        if (!StringUtils.isEmpty(this.getPhone())) {
            //联系电话
            java.util.regex.Pattern userNamePattern = java.util.regex.Pattern.compile("^1[3|4|5|6|7|8|9][0-9]{9}$");
            Matcher phoneMatcher = userNamePattern.matcher(this.getPhone());
            if (!phoneMatcher.find()) {
                return "联系电话无效";
            }
        } else {
            return "联系电话不可为空";
        }
        if (this.getEnable()==null) {
            return "可用状态不能为空";
        }
        return null;
    }
}
