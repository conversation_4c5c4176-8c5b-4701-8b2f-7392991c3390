package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.diboot.core.binding.annotation.BindField;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.domain.SysAppUser;
import com.ruoyi.common.entity.domain.SysDept;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 用户申请企业审核对象 sys_user_examine
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@TableName(value = "sys_user_examine")
public class SysUserExamine extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 公司ID
     */
    /**
     * 公司ID
     */
    @Excel(name = "企业ID")
    private Long companyId;

    @TableField(exist = false)
    @BindField(entity = SysAppUser.class, field = "nick_name", condition = "this.user_id = user_id ")
    @Excel(name = "用户")
    @BindQuery(comparison = Comparison.LIKE)
    private String nickName;


    @BindField(entity = SysDept.class, field = "dept_name", condition = "this.company_id = dept_id ")
    @TableField(exist = false)
    @Excel(name = "公司名称")
    private String deptName;

    /**
     * 0未审核1通过2未通过
     */
    @Excel(name = "审核状态", readConverterExp = "0=未审核,1=通过,2=拒绝,3=撤销")
    private Integer status;


    @TableField(exist = false)
    @BindField(entity = SysAppUser.class, field = "user_name", condition = "this.user_id = user_id ")
    // @BindQuery(comparison = Comparison.LIKE)
    private String userName;


    /**
     * 头像
     */
    @TableField(exist = false)
    @BindField(entity = SysAppUser.class, field = "avatar", condition = "this.user_id = user_id ")
    private String avatar;


    private String delFlag;

    /**
     * 查询状态-前端查询时候用
     * 0未审核 1已审核（审核通过，审核拒绝，撤销）
     */
    @TableField(exist = false)
    private Integer queryStatus;

    @TableField(exist = false)
    private Long deptId;

    @TableField(exist = false)
    private String depts;

    @TableField(exist = false)
    private String position;


}
