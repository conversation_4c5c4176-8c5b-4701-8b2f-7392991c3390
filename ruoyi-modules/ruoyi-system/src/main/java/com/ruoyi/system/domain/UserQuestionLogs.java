package com.ruoyi.system.domain;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用户问答记录对象 user_question_logs
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
@ToString
@Data
public class UserQuestionLogs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 用户Id */
    @Excel(name = "用户Id")
    private Long userId;

    /** 用户问题文本 */
    @Excel(name = "用户问题文本")
    private String question;

}
