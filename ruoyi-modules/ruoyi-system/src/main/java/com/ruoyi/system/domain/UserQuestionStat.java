package com.ruoyi.system.domain;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用户问答统计对象 user_question_stat
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */

@ToString
@Data
public class UserQuestionStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 企业ID */
    @Excel(name = "企业ID")
    private Long companyId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 当日使用总量 */
    @Excel(name = "当日使用总量")
    private Long count;

    /** 当日消耗 */
    @Excel(name = "当日消耗")
    private Long consume;
}
