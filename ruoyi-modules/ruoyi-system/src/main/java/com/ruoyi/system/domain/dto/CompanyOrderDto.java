package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.MtScreenInfo;
import com.ruoyi.system.domain.SysCompanyOrder;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;

/**
 * 企业订单备货DTO
 * <AUTHOR>
 * @date 2022-12-27
 */
@Data
public class CompanyOrderDto {
    private SysCompanyOrder companyOrder;
    private List<MtScreenInfo> screenInfos;

    /**
     * 订单单号
     */
    @Excel(name = "*订单单号")
    private String serialNo;
    /**
     * 快递单号
     */
    @Excel(name = "快递单号")
    private String expressNo;

    /**
     * 预计安装时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "*预计安装时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date installTime;


    /**
     * 导入专用预计安装时间
     */
    @Excel(name = "*预计安装时间", width = 30,prompt ="输入格式为 \n 例:2022-02-02 09:00:00")
    private String installTimes;
    /**
     * 安装详细地址
     */
    @Excel(name = "*安装详细地址", width = 30)
    private String installeAddress;

    /**
     * 安装人员姓名
     */
    @Excel(name = "*安装人员姓名")
    private String installerName;

    /**
     * 安装人员手机号
     */
    @Excel(name = "*安装人员手机号")
    private String installerPhone;

    /**
     * 设备码
     */
    @Excel(name = "设备码")
    private String deviceCode;

    /** 设备SN码 */
    @Excel(name = "设备SN码")
    private String deviceSn;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号", dictType = "equip")
    private String deviceType;

    /**
     * 设备型号
     */
    @Excel(name = "设备调试人员用户名/姓名",width = 30)
    private String debugerInfo;


    public Date getInstallDate(){
        if (this.getInstallTimes()!=null){
            this.setInstallTime(DateUtils.parseDate(this.getInstallTimes()));
        }
        return this.getInstallTime();
    }
    /**
     * Excel导入验证
     * @return
     */
    public String validForImport() {
        if (StringUtils.isEmpty(this.getSerialNo())) {
            return "订单单号不可为空";
        }
        if (this.getInstallDate() != null) {
            Date date = new Date();
            if (!getInstallDate().after(date)){
                return "安装时间不能小于当前时间";
            }
        }else{
            return "预计安装时间不可为空";
        }
        if (StringUtils.isEmpty(this.getInstalleAddress())) {
            return "安装详细地址不可为空";
        }
        if (StringUtils.isEmpty(this.getInstallerName())) {
            return "安装人员姓名不可为空";
        }
        if (!StringUtils.isEmpty(this.getInstallerPhone())) {
            //联系电话
            java.util.regex.Pattern userNamePattern = java.util.regex.Pattern.compile("^1[3|4|5|6|7|8|9][0-9]{9}$");
            Matcher phoneMatcher = userNamePattern.matcher(this.getInstallerPhone());
            if (!phoneMatcher.find()) {
                return "安装人员联系电话无效";
            }
        } else {
            return "安装人员联系电话不可为空";
        }
        return null;
    }
}
