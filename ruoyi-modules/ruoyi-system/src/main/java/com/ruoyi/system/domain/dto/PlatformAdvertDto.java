package com.ruoyi.system.domain.dto;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 业务平台关联企业信息
 */
@Data
public class PlatformAdvertDto extends BaseEntity {
    private Long platformId;

    private Long advertId;

    private Long[] advertIds;

    /**
     * 时长
     */
    private Integer duration;

    /** 内容类型0是图片1是视频 */
    private Integer contentType;

    private String contentTypeDisplay;

    public String getContentTypeDisplay(){
        if(contentType == null){
            return "";
        }else if(contentType == 0){
            return "图片";
        }else if(contentType == 1){
            return "视频";
        }
        return "";
    }

    /** 终端类型 */
    private String terminalType;

    private String termialTypeDisplay;

    /** 图片或者视频url */
    private String url;

    /**
     * 关联标识，0：未关联，1：已关联
     */
    private Integer relationFlag;

    /**
     * 关联标识展示
     */
    private String relationFlagDisplay;

    public String getRelationFlagDisplay() {
        if(relationFlag == null || relationFlag == 0){
            return "关联";
        }else{
            return "取消关联";
        }
    }
}
