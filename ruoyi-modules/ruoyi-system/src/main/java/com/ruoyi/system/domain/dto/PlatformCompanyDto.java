package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 业务平台关联企业信息
 */
@Data
public class PlatformCompanyDto extends BaseEntity {
    private Long platformId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 公司名
     */
    private String companyName;

    /**
     * 关联平台ID
     */
    private Long relationPlatformId;

    /**
     * 关联平台名
     */
    private String relationPlatformName;

    /**
     * 关联企业ids
     */
    private List<Long> companyIds;

    /**
     * 关联标识，0：未关联，1：已关联
     */
    private Integer relationFlag;

    /**
     * 关联标识展示
     */
    private String relationFlagDisplay;

    public String getRelationFlagDisplay() {
        if(relationFlag == null || relationFlag == 0){
            return "关联";
        }else{
            return "取消关联";
        }
    }

    /**
     * 关联时间
     */
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date relationTime;

    /**
     * 操作人
     */
    private String relationBy;
}
