package com.ruoyi.system.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 业务平台-课程组关联信息对象 sys_platform_course_group
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Data
public class PlatformCourseGroupDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 业务平台ID */
    private Long platformId;

    /** 课程组分类ID */
    private Long courseGroupClassifyId;

    /** 课程组分类ID */
    private Long[] courseGroupClassifyIds;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 关联标识，0：未关联，1：已关联
     */
    private Integer relationFlag;

    /**
     * 关联标识展示
     */
    private String relationFlagDisplay;

    public String getRelationFlagDisplay() {
        if(relationFlag == null || relationFlag == 0){
            return "关联";
        }else{
            return "取消关联";
        }
    }
}
