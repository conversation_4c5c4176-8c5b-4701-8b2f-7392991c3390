package com.ruoyi.system.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 业务平台信息对象 sys_platform
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@Data
public class PlatformDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 业务平台ID */
    private Long platformId;

    /** 业务平台名称 */
    @Excel(name = "业务平台名称")
    private String platformName;

    /** 业务平台LOGO */
    @Excel(name = "业务平台LOGO")
    private String platformLogo;

    /** 业务平台标题 */
    @Excel(name = "业务平台标题")
    private String platformTitle;

    /** 默认标识，0：非默认，1：默认 */
    @Excel(name = "默认标识，0：非默认，1：默认")
    private Integer platformDefaultFlag;

    /**
     * 归属业务平台企业数量
     */
    private Integer companyCount = 0;

    /**
     * 归属业务平台企业数量显示定义
     */
    private String companyCountDisplay;

    /**
     * 归属业务平台课程组分类数量
     */
    private Integer courseGroupCount = 0;

    /**
     * 归属业务平台课程组分类数量显示定义
     */
    private String courseGroupCountDisplay;

    /**
     * 归属业务平台首页推荐数量
     */
    private Integer recommendCount = 0;

    /**
     * 归属业务平台首页推荐数量展示定义
     */
    private String recommendCountDisplay;

    /**
     * 归属业务平台开机广告数量
     */
    private Integer adBootCount = 0;

    /**
     * 归属业务平台开机广告数量展示定义
     */
    private String adBootCountDisplay;


    /**
     * 归属业务平台轮播图数量
     */
    private Integer slideshowCount = 0;


    /**
     * 归属业务平台轮播图数量展示定义
     */
    private String slideshowCountDisplay;

    public String getCompanyCountDisplay() {
        return this.companyCount+"个企业";
    }

    public String getCourseGroupCountDisplay() {
        return courseGroupCount+"个课程组分类";
    }

    public String getRecommendCountDisplay() {
        return recommendCount+"个推荐设置";
    }

    public String getAdBootCountDisplay() {
        return adBootCount+"个开机广告";
    }

    public String getSlideshowCountDisplay() {
        return slideshowCount+"个轮播图";
    }

}
