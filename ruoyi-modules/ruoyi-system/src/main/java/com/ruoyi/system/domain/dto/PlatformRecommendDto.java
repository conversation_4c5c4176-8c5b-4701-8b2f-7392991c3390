package com.ruoyi.system.domain.dto;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 业务平台关联企业信息
 */
@Data
public class PlatformRecommendDto extends BaseEntity {
    private Long platformId;

    private Long[] platformIds;

    private Long recommendId;

    private Long[] recommendIds;

    /**
     * 推荐名称
     */
    private String recommendName;

    /**
     * 推荐图片
     */
    private String recommendImg;

    private Long classifyId;

    private String recommendKey;

    /**
     * 推荐位置
     */
    private String recommendPostion;

    /**
     * 课程组
     */
    private String recommendContent;

    /**
     * 关联标识，0：未关联，1：已关联
     */
    private Integer relationFlag;

    /**
     * 关联标识展示
     */
    private String relationFlagDisplay;

    public String getRelationFlagDisplay() {
        if(relationFlag == null || relationFlag == 0){
            return "关联";
        }else{
            return "取消关联";
        }
    }
}
