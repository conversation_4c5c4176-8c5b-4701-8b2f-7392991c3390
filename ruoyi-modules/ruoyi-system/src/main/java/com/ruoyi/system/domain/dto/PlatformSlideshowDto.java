package com.ruoyi.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 业务平台关联轮播图信息
 */
@Data
public class PlatformSlideshowDto extends BaseEntity {
    private Long platformId;

    private Long slideshowId;

    private Long[] slideshowIds;

    private String slideshowName;

    private String imgUrl;

    /**
     * 类型 -1:无、0:链接、1:内部课程、2:内容公告、3:套餐
     */
    private String type;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 关联模块
     */
    private Long modular;

    private Integer sort;

    private String modularName = "";

    /**
     * 图片地址
     */
    private String minImgUrl;

    /**
     * 轮播图状态：0是正常1禁用
     */
    @Excel(name = "0是正常1禁用")
    private Integer statusFlag;

    private Integer statusFlagDisplay;

    public String getStatusFlagDisplay() {
        if(statusFlag != null && statusFlag == 1){
            return "禁用";
        }else{
            return "正常";
        }
    }

    /**
     * 关联标识，0：未关联，1：已关联
     */
    private Integer relationFlag;

    /**
     * 关联标识展示
     */
    private String relationFlagDisplay;

    public String getRelationFlagDisplay() {
        if(relationFlag == null || relationFlag == 0){
            return "关联";
        }else{
            return "取消关联";
        }
    }
}
