package com.ruoyi.system.domain.dto;

import lombok.Data;

/**
 * 快捷入口数据传输对象
 */

@Data
public class QuickEntryDataDto {
    /**
     * 快捷入口类型
     */
    private Integer quickEntryType;

    private String quickEntryName;

    /**
     * 快捷入口名称
     * @return
     */
    public String getQuickEntryName() {
        return QuickEntryTypeEnum.getDescriptionByCode(this.quickEntryType);
    }

    private Long quickEntryCount;

    public QuickEntryDataDto(Integer quickEntryType, Long appUserCount) {
        this.quickEntryType = quickEntryType;
        this.quickEntryCount = appUserCount;
    }
}
