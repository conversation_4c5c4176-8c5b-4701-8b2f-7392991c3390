package com.ruoyi.system.domain.dto;

public enum QuickEntryTypeEnum {
    PERSONNEL_MANAGEMENT(1, "人员管理"),
    PERSONNEL_AUDIT(2, "人员审核"),
    MEETING_ARRANGEMENT(3, "会议安排"),
    COURSE_MALL(4, "课程商城"),
    ONLINE_COURSE_ALLOCATION(5, "分配在线课"),
    KNOWLEDGE_SHARING_ASSIGNMENT(6, "知享指派"),
    ORGANIZATION_ASSESSMENT(7, "组织测评");

    private final Integer code;
    private final String description;

    QuickEntryTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    public static String getDescriptionByCode(Integer code) {
        for (QuickEntryTypeEnum entry : QuickEntryTypeEnum.values()) {
            if (entry.getCode() == code) {
                return entry.getDescription();
            }
        }
        return null;
    }
}