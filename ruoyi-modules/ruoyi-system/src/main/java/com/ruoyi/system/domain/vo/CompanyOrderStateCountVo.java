package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.domain.SysCompanyOrderState;
import lombok.Data;

@Data
public class CompanyOrderStateCountVo {
    private Integer orderState;
    private int orderCount;

    @JsonIgnore
    public void setOrderStateEnum(SysCompanyOrderState state) {
        if (state != null) {
            this.orderState = state.getValue();
        } else {
            this.orderState = null;
        }
    }

    @JsonIgnore
    public SysCompanyOrderState getOrderStateEnum() {
        if (this.orderState == null) {
            return null;
        }

        return SysCompanyOrderState.getFromStateValue(this.orderState);
    }
}
