package com.ruoyi.system.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 考试信息对象 tr_exam
 *
 * <AUTHOR>
 * @date 2021-12-25
 */
@Data
public class ExamVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 试卷ID
     */
    private Long paperId;

    @TableField(exist = false)
    private String paperName;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 考试类型，0：普通考试，1：学习计划中的考试
     */
    @JsonIgnore
    private Integer examType;

    /**
     * 考试主题
     */
    @Excel(name = "考试主题")
    private String topic;

    /**
     * 总分
     */
    @Excel(name = "总分")
    private Float totalScore;

    /**
     * 及格分
     */
    @Excel(name = "及格分")
    private Float passScore;

    /**
     * 考试时长
     */
    @Excel(name = "考试时长")
    private Integer duration;

    /**
     * 考试开始时间
     */
    @Excel(name = "考试开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 过期时间
     */
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;



    /**
     * 排序
     */
    @TableField(exist = false)
    private Integer sort;
}
