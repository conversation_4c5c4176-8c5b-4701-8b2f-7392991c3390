package com.ruoyi.system.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.diboot.core.binding.annotation.BindEntity;
import com.diboot.core.binding.query.BindQuery;
import com.diboot.core.binding.query.Comparison;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.domain.SysAppUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

/**
 * 订单对象 tr_order
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Data
@ToString
@JsonInclude(NON_EMPTY)
@ApiModel("订单")
public class OrderVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    @ApiModelProperty("订单号")
    @BindQuery(comparison = Comparison.LIKE)
    private String orderNo;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    @ApiModelProperty("支付金额")
    private BigDecimal payPrice;

    /**
     * 课程ID
     */
    @ApiModelProperty("课程ID")
    @NotBlank(message = "课程ID不能为空")
    private Long courseId;

    /**
     * 课程ID
     */
    // @Excel(name = "课程名称")
    @TableField(exist = false)
    private String courseName;



    /**
     * 课程数量
     */
    @Excel(name = "课程数量")
    @ApiModelProperty("课程数量")
    @NotBlank(message = "请填写课程数量")
    private Long courseNumber;

    /**
     * 课程单价
     */
    @Excel(name = "课程单价")
    @ApiModelProperty("课程单价")
    private BigDecimal coursePrice;

    /**
     * 购买方式企业、个人
     */
    @Excel(name = "购买方式", readConverterExp = "0=企业,1=个人")
    @ApiModelProperty("购买方式 0企业、1个人")
    @NotBlank(message = "请填写购买方式")
    private String buyWay;

    /**
     * 购买途径-支付、兑换券
     */
    @Excel(name = "购买途径", readConverterExp = "0=支付,1=兑换券")
    @ApiModelProperty("购买途径-0支付、1兑换券")
    private String buyChannel;

    /**
     * 购买人ID
     */
    @ApiModelProperty("购买人ID")
    private Long buyPerson;


    /**
     * 兑换券ID
     */
    @ApiModelProperty("兑换券ID")
    private Long voucherId;




    @Excel(name = "兑换券名称")
    @TableField(exist = false)
    private String voucherName;

    /**
     * 支付方式-微信和支付宝
     */
    @Excel(name = "支付方式", readConverterExp = "0=支付宝,1=微信")
    @ApiModelProperty("支付方式-1微信和0支付宝")
    private Integer payWay;

    /**
     * 支付订单号
     */
    @Excel(name = "支付订单号")
    @ApiModelProperty("支付订单号")
    private String payNo;

    /**
     * 支付人账户
     */
    @Excel(name = "支付人账户")
    @ApiModelProperty("支付人账户")
    private String account;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("下单时间")

    private Date orderTime;

    /**
     * 支付状态0未支付1支付成功2支付失败
     */
    @Excel(name = "支付状态", readConverterExp = "0=未支付,1=支付成功,2=支付失败")
    @ApiModelProperty("支付状态")
    private Integer payStatus;

    /**
     * 订单状态0未分配1分配成功2分配失败
     */
    //@Excel(name = "订单状态0未分配1分配成功2分配失败")
    @ApiModelProperty("订单状态0未分配1分配成功2分配失败")
    private Integer orderStaus;

    /**
     * 购买时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "购买时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date buyTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //  @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @ApiModelProperty("系统类型")
    private String terminalType;

    @BindEntity(entity = SysAppUser.class, condition = "this.buy_person=user_id")
    @TableField(exist = false)
    private SysAppUser sysUser;

    /**
     * 下单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("下单时间开始时间")
    @TableField(exist = false)
    private Date orderStartTime;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("下单结束时间")
    @TableField(exist = false)
    private Date OrderEndTime;



    @ApiModelProperty("商品类型")
    private Integer productType;

    @ApiModelProperty("企业ID")
    private Long companyId;

    /**
     * 购买人
     */
    @Excel(name = "购买人")
    @TableField(exist = false)
    private String nikeName;



    @ApiModelProperty("兑换券数量")
    private Integer voucherNumber;

    @Excel(name = "套餐名称")
    @TableField(exist = false)
    private String cvPackageName;
}
