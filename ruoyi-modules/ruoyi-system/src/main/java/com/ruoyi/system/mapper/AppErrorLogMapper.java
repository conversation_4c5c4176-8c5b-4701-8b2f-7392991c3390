package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.AppErrorLog;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * APP崩溃日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-21
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface AppErrorLogMapper  extends BaseMapper<AppErrorLog>
{
    /**
     * 查询APP崩溃日志
     * 
     * @param id APP崩溃日志主键
     * @return APP崩溃日志
     */
    public AppErrorLog selectAppErrorLogById(Long id);

    /**
     * 查询APP崩溃日志列表
     * 
     * @param appErrorLog APP崩溃日志
     * @return APP崩溃日志集合
     */
    public List<AppErrorLog> selectAppErrorLogList(AppErrorLog appErrorLog);
    /**
     * 批量新增APP崩溃日志
     *
     * @param list APP崩溃日志列表
     * @return 结果
     */
    public int insertBatch(List<AppErrorLog> list);

    /**
     * 批量删除APP崩溃日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppErrorLogByIds(Long[] ids);
}
