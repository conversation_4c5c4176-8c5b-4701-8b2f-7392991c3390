package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.AppUpdateInfo;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 应用程序更新信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-06
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface AppUpdateInfoMapper extends BaseMapper<AppUpdateInfo>
{

}
