package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.MtCompanyRes;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 公司与会议资源Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
public interface MtCompanyResMapper extends BaseMapper<MtCompanyRes> {
    @Select("SELECT sd.dept_name,mc.meeting_name,mc.meeting_volume,mc.meeting_state,mc.meeting_start_time,mc.meeting_end_time,mc.meeting_duration FROM mt_company_res mc,sys_dept sd WHERE mc.company_id=sd.dept_id")
    List<MtCompanyRes> selectCompanyInfoRes();
}
