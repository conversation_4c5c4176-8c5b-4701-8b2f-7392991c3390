package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.domain.MtMeetingRoom;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 会议室Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-07
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface MtMeetingRoomMapper extends BaseMapper<MtMeetingRoom>
{
    /**
     * 查询会议室
     * 
     * @param id 会议室主键
     * @return 会议室
     */
    public MtMeetingRoom selectMtMeetingRoomById(Long id);

    /**
     * 查询会议室列表
     * 
     * @param mtMeetingRoom 会议室
     * @return 会议室集合
     */
    public List<MtMeetingRoom> selectMtMeetingRoomList(MtMeetingRoom mtMeetingRoom);

    /**
     * 新增会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    public int insertMtMeetingRoom(MtMeetingRoom mtMeetingRoom);

    /**
     * 修改会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    public int updateMtMeetingRoom(MtMeetingRoom mtMeetingRoom);

    /**
     * 删除会议室
     * 
     * @param id 会议室主键
     * @return 结果
     */
    public int deleteMtMeetingRoomById(Long id);

    /**
     * 批量删除会议室
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMtMeetingRoomByIds(Long[] ids);
}
