package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.dto.DateAmountInfo;
import com.ruoyi.common.entity.dto.TopicAmountInfo;
import com.ruoyi.common.entity.domain.MtScreenInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 会议大屏信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-08
 */
public interface MtScreenInfoMapper extends BaseMapper<MtScreenInfo> {
    String querySql = "SELECT si.*, sd.dept_name AS company_name, sdd.dept_name AS dept_name FROM mt_screen_info si LEFT JOIN sys_dept sd ON si.company_id = sd.dept_id LEFT JOIN sys_dept sdd ON si.dept_id = sdd.dept_id order by create_time DESC, id DESC";
    String wrapperSql = "SELECT * from (" + querySql + ") AS q ${ew.customSqlSegment}";

    String screenActiveByDateSql = "select DATE_FORMAT(activate_time,'${filter}') date, count(id) amount from mt_screen_info " +
            " where order_id is not null and activate_time >= #{startDate} and activate_time < #{endDate}" +
            " group by DATE_FORMAT(activate_time,'${filter}')";

    String screenActiveCountTodaySql = "select 'activeToday' topic,count(id) amount from mt_screen_info where activate_time >= DATE_FORMAT(sysdate(),'%Y-%m-%d')";

    @Select(wrapperSql)
    List<MtScreenInfo> list(@Param("ew") QueryWrapper<MtScreenInfo> queryWrapper);

    @Select(screenActiveByDateSql)
    List<DateAmountInfo<Integer>> getScreenActiveStatByDate(@Param(value = "startDate") Date startDate, @Param(value = "endDate") Date endDate,
                                                            @Param(value = "filter") String filter);

    @Select(screenActiveCountTodaySql)
    List<TopicAmountInfo<Integer>> getActiveScreenCountToday();
}
