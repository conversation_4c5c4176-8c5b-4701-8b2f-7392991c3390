package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.domain.SysAppeal;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 申诉信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysAppealMapper extends BaseMapper<SysAppeal>
{
    /**
     * 查询申诉信息
     * 
     * @param id 申诉信息主键
     * @return 申诉信息
     */
    public SysAppeal selectSysAppealById(Long id);

    /**
     * 查询申诉信息列表
     * 
     * @param sysAppeal 申诉信息
     * @return 申诉信息集合
     */
    public List<SysAppeal> selectSysAppealList(SysAppeal sysAppeal);

    /**
     * 新增申诉信息
     * 
     * @param sysAppeal 申诉信息
     * @return 结果
     */
    public int insertSysAppeal(SysAppeal sysAppeal);

    /**
     * 修改申诉信息
     * 
     * @param sysAppeal 申诉信息
     * @return 结果
     */
    public int updateSysAppeal(SysAppeal sysAppeal);

    /**
     * 删除申诉信息
     * 
     * @param id 申诉信息主键
     * @return 结果
     */
    public int deleteSysAppealById(Long id);

    /**
     * 批量删除申诉信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAppealByIds(Long[] ids);
}
