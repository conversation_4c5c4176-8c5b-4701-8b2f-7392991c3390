package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.SysCompanyOrderLog;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 企业订单操作记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysCompanyOrderLogMapper extends BaseMapper<SysCompanyOrderLog> {
    /**
     * 查询企业订单操作记录
     *
     * @param id 企业订单操作记录主键
     * @return 企业订单操作记录
     */
    public SysCompanyOrderLog selectSysCompanyOrderLogById(Long id);

    /**
     * 查询企业订单操作记录列表
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 企业订单操作记录集合
     */
    public List<SysCompanyOrderLog> selectSysCompanyOrderLogList(SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 新增企业订单操作记录
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 结果
     */
    public int insertSysCompanyOrderLog(SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 修改企业订单操作记录
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 结果
     */
    public int updateSysCompanyOrderLog(SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 删除企业订单操作记录
     *
     * @param id 企业订单操作记录主键
     * @return 结果
     */
    public int deleteSysCompanyOrderLogById(Long id);

    /**
     * 批量删除企业订单操作记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysCompanyOrderLogByIds(Long[] ids);
    }
