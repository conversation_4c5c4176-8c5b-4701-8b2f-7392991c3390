package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.entity.domain.system.SysCompanyResConfig;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface SysCompanyResConfigMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysCompanyResConfig selectSysCompanyResConfigByCompanyId(Long companyId);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysCompanyResConfig> selectSysCompanyResConfigList(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    public int insertSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    public int updateSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 删除【请填写功能名称】
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysCompanyResConfigByCompanyId(Long companyId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param companyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysCompanyResConfigByCompanyIds(Long[] companyIds);

    /**
     * 根据公司id和类型查询公司资源配置数据
     * @param companyId
     * @param type
     * @return
     */
    SysCompanyResConfig selectCompResConfigDataByType(@Param("companyId") Long companyId, @Param("type") String type);
}
