package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.domain.SysComplaintInfo;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 投诉信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-16
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysComplaintInfoMapper extends BaseMapper<SysComplaintInfo>
{
    /**
     * 查询投诉信息
     * 
     * @param id 投诉信息主键
     * @return 投诉信息
     */
    public SysComplaintInfo selectSysComplaintInfoById(Long id);

    /**
     * 查询投诉信息列表
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 投诉信息集合
     */
    public List<SysComplaintInfo> selectSysComplaintInfoList(SysComplaintInfo sysComplaintInfo);

    /**
     * 新增投诉信息
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 结果
     */
    public int insertSysComplaintInfo(SysComplaintInfo sysComplaintInfo);

    /**
     * 修改投诉信息
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 结果
     */
    public int updateSysComplaintInfo(SysComplaintInfo sysComplaintInfo);

    /**
     * 删除投诉信息
     * 
     * @param id 投诉信息主键
     * @return 结果
     */
    public int deleteSysComplaintInfoById(Long id);

    /**
     * 批量删除投诉信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysComplaintInfoByIds(Long[] ids);
}
