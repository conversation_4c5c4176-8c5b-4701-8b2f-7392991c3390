package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.domain.SysComplaint;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 投诉信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysComplaintMapper extends BaseMapper<SysComplaint>
{
    /**
     * 查询投诉信息
     * 
     * @param id 投诉信息主键
     * @return 投诉信息
     */
    public SysComplaint selectSysComplaintById(Long id);

    /**
     * 查询投诉信息列表
     * 
     * @param sysComplaint 投诉信息
     * @return 投诉信息集合
     */
    public List<SysComplaint> selectSysComplaintList(SysComplaint sysComplaint);

    /**
     * 新增投诉信息
     * 
     * @param sysComplaint 投诉信息
     * @return 结果
     */
    public int insertSysComplaint(SysComplaint sysComplaint);

    /**
     * 修改投诉信息
     * 
     * @param sysComplaint 投诉信息
     * @return 结果
     */
    public int updateSysComplaint(SysComplaint sysComplaint);

    /**
     * 删除投诉信息
     * 
     * @param id 投诉信息主键
     * @return 结果
     */
    public int deleteSysComplaintById(Long id);

    /**
     * 批量删除投诉信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysComplaintByIds(Long[] ids);
}
