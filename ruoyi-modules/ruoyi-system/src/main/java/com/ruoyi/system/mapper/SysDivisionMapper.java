package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.SysDivision;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 区域信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface SysDivisionMapper extends BaseMapper<SysDivision> {

}
