package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.SysDownload;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 安装包下载Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-26
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface SysDownloadMapper extends BaseMapper<SysDownload> {
}
