package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysNewsNotification;

import java.util.List;

/**
 * 消息管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
//@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysNewsNotificationMapper extends BaseMapper<SysNewsNotification> {
    /**
     * 查询消息管理
     *
     * @param id 消息管理主键
     * @return 消息管理
     */
    public SysNewsNotification selectSysNewsNotificationById(Long id);

    /**
     * 查询消息管理列表
     *
     * @param sysNewsNotification 消息管理
     * @return 消息管理集合
     */
    public List<SysNewsNotification> selectList(SysNewsNotification sysNewsNotification);

    /**
     * 新增消息管理
     *
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    public int insertSysNewsNotification(SysNewsNotification sysNewsNotification);

    /**
     * 修改消息管理
     *
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    public int updateSysNewsNotification(SysNewsNotification sysNewsNotification);

    /**
     * 删除消息管理
     *
     * @param id 消息管理主键
     * @return 结果
     */
    public int deleteSysNewsNotificationById(Long id);

    /**
     * 批量删除消息管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysNewsNotificationByIds(Long[] ids);
    }
