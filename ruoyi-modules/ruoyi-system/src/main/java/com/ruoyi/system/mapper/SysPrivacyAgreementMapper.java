package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysPrivacyAgreement;

import java.util.List;

/**
 * 隐私协议Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-15
 */
//@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysPrivacyAgreementMapper extends BaseMapper<SysPrivacyAgreement> {
    /**
     * 查询隐私协议
     *
     * @param id 隐私协议主键
     * @return 隐私协议
     */
    public SysPrivacyAgreement selectSysPrivacyAgreementById(Long id);

    /**
     * 查询隐私协议列表
     *
     * @param sysPrivacyAgreement 隐私协议
     * @return 隐私协议集合
     */
    public List<SysPrivacyAgreement> selectSysPrivacyAgreementList(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 新增隐私协议
     *
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    public int insertSysPrivacyAgreement(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 修改隐私协议
     *
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    public int updateSysPrivacyAgreement(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 删除隐私协议
     *
     * @param id 隐私协议主键
     * @return 结果
     */
    public int deleteSysPrivacyAgreementById(Long id);

    /**
     * 批量删除隐私协议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysPrivacyAgreementByIds(Long[] ids);
    }
