package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysProblemFeedback;

import java.util.List;

/**
 * 问题反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
//@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysProblemFeedbackMapper extends BaseMapper<SysProblemFeedback> {
    /**
     * 查询问题反馈
     *
     * @param id 问题反馈主键
     * @return 问题反馈
     */
    public SysProblemFeedback selectSysProblemFeedbackById(Long id);

    /**
     * 查询问题反馈列表
     *
     * @param sysProblemFeedback 问题反馈
     * @return 问题反馈集合
     */
    public List<SysProblemFeedback> selectSysProblemFeedbackList(SysProblemFeedback sysProblemFeedback);

    /**
     * 新增问题反馈
     *
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    public int insertSysProblemFeedback(SysProblemFeedback sysProblemFeedback);

    /**
     * 修改问题反馈
     *
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    public int updateSysProblemFeedback(SysProblemFeedback sysProblemFeedback);

    /**
     * 删除问题反馈
     *
     * @param id 问题反馈主键
     * @return 结果
     */
    public int deleteSysProblemFeedbackById(Long id);

    /**
     * 批量删除问题反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysProblemFeedbackByIds(Long[] ids);
    }
