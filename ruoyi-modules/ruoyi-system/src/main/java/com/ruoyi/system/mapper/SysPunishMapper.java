package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.entity.domain.SysPunish;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 处罚信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysPunishMapper extends BaseMapper<SysPunish>
{
    /**
     * 查询处罚信息
     * 
     * @param id 处罚信息主键
     * @return 处罚信息
     */
    public SysPunish selectSysPunishById(Long id);

    /**
     * 查询处罚信息列表
     * 
     * @param sysPunish 处罚信息
     * @return 处罚信息集合
     */
    public List<SysPunish> selectSysPunishList(SysPunish sysPunish);

    /**
     * 新增处罚信息
     * 
     * @param sysPunish 处罚信息
     * @return 结果
     */
    public int insertSysPunish(SysPunish sysPunish);

    /**
     * 修改处罚信息
     * 
     * @param sysPunish 处罚信息
     * @return 结果
     */
    public int updateSysPunish(SysPunish sysPunish);

    /**
     * 删除处罚信息
     * 
     * @param id 处罚信息主键
     * @return 结果
     */
    public int deleteSysPunishById(Long id);

    /**
     * 批量删除处罚信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysPunishByIds(Long[] ids);
}
