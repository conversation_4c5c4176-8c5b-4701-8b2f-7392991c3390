package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.redis.service.MybatisRedisCache;
import com.ruoyi.system.domain.SysSalesManager;
import com.ruoyi.system.domain.statistics.SalesManagerStatInfo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 销售经理信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface SysSalesManagerMapper extends BaseMapper<SysSalesManager> {
    String salesManagerStatSql = "<script>select m.*,IFNULL(sum(o.device_count),0) device_count from sys_sales_manager m" +
            " left join sys_company_order o on o.sales_manager_id = m.id " +
            " and o.order_state <![CDATA[ = ]]> 6 " +
            "<if test='startTime != null'> and o.update_time <![CDATA[ >= ]]> #{startTime} </if> " +
            "<if test='endTime != null'> and o.update_time <![CDATA[ < ]]> #{endTime} </if>" +
            " where m.enable = 0 " +
            " group by m.id order by device_count desc </script>";

    @Options(useCache = false)
    @Select(salesManagerStatSql)
    List<SalesManagerStatInfo> getSalesManagerStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
