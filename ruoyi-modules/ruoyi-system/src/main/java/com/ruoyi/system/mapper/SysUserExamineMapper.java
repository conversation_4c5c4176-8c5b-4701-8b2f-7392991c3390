package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysUserExamine;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户申请企业审核Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
//@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface SysUserExamineMapper extends BaseMapper<SysUserExamine> {
    String selectListSql = "<script>select e.* from sys_user_examine e " +
            "<if test='nickName != null or userName != null'> left join sys_app_user u on e.user_id=u.user_id </if>"+
            "<where>"+
            "<if test='companyId!=null'> e.company_id = #{companyId} </if>"+
            "<if test='nickName!=null'> and u.nick_name like concat('%', #{nickName}, '%') </if>"+
            "<if test='userName!=null'> and u.user_name like concat('%', #{userName}, '%') </if>"+
            "<if test='queryStatus!=null and queryStatus == 0'> and e.status = #{queryStatus} </if>"+
            "<if test='queryStatus!=null and queryStatus == 1'> and e.status <![CDATA[ >=  ]]> 1 </if>"+
            "</where>" +
            " order by e.create_time desc,e.update_time desc "+
            "</script>";

    @Select(selectListSql)
    List<SysUserExamine> selectExamineList(SysUserExamine userExamine);
}
