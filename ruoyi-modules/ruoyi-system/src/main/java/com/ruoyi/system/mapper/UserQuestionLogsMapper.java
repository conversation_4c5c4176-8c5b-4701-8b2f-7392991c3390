package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserQuestionLogs;

/**
 * 用户问答记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface UserQuestionLogsMapper 
{
    /**
     * 查询用户问答记录
     * 
     * @param id 用户问答记录主键
     * @return 用户问答记录
     */
    public UserQuestionLogs selectUserQuestionLogsById(Long id);

    /**
     * 查询用户问答记录列表
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 用户问答记录集合
     */
    public List<UserQuestionLogs> selectUserQuestionLogsList(UserQuestionLogs userQuestionLogs);

    /**
     * 新增用户问答记录
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 结果
     */
    public int insertUserQuestionLogs(UserQuestionLogs userQuestionLogs);

    /**
     * 修改用户问答记录
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 结果
     */
    public int updateUserQuestionLogs(UserQuestionLogs userQuestionLogs);

    /**
     * 删除用户问答记录
     * 
     * @param id 用户问答记录主键
     * @return 结果
     */
    public int deleteUserQuestionLogsById(Long id);

    /**
     * 批量删除用户问答记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserQuestionLogsByIds(Long[] ids);
}
