package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserQuestionStat;

/**
 * 用户问答统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface UserQuestionStatMapper 
{
    /**
     * 查询用户问答统计
     * 
     * @param id 用户问答统计主键
     * @return 用户问答统计
     */
    public UserQuestionStat selectUserQuestionStatById(Long id);

    /**
     * 查询用户问答统计列表
     * 
     * @param userQuestionStat 用户问答统计
     * @return 用户问答统计集合
     */
    public List<UserQuestionStat> selectUserQuestionStatList(UserQuestionStat userQuestionStat);

    /**
     * 新增用户问答统计
     * 
     * @param userQuestionStat 用户问答统计
     * @return 结果
     */
    public int insertUserQuestionStat(UserQuestionStat userQuestionStat);

    /**
     * 修改用户问答统计
     * 
     * @param userQuestionStat 用户问答统计
     * @return 结果
     */
    public int updateUserQuestionStat(UserQuestionStat userQuestionStat);

    /**
     * 删除用户问答统计
     * 
     * @param id 用户问答统计主键
     * @return 结果
     */
    public int deleteUserQuestionStatById(Long id);

    /**
     * 批量删除用户问答统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserQuestionStatByIds(Long[] ids);

    /**
     * 新增多条
     * @param list
     * @return
     */
    int batchInsert(List<UserQuestionStat> list);
}
