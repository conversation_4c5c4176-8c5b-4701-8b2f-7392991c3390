package com.ruoyi.system.queue;

import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * app崩溃日志阻塞队列
 *
 * <AUTHOR>
 * @date 2022-09-22
 */
@Component
public class AppErrorLogQueue {
    private static final int MAX_LOG_NUMBER = 1;
    private static AppErrorLogQueue appErrorLogQueue = new AppErrorLogQueue();


    /**
     * 阻塞队列
     */
    private BlockingQueue<String> logBlockingQueue = new LinkedBlockingQueue<>(MAX_LOG_NUMBER);

    private AppErrorLogQueue() {

    }

    /**
     * 获得单例
     *
     * @return
     */
    public static AppErrorLogQueue getInstance() {
        return appErrorLogQueue;
    }

    /**
     * 入队
     *
     * @param str
     * @return
     */
    public boolean push(String str) {
        boolean result = false;
        try{
            result = this.logBlockingQueue.add(str);
        }catch (IllegalStateException e){
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 出队
     *
     * @return
     */
    public String pop() {
        String take = null;
        try {
            take = this.logBlockingQueue.take();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return take;
    }

    /**
     * 返回队列元素个数
     *
     * @return
     */
    public int size() {
        return this.logBlockingQueue.size();
    }
}
