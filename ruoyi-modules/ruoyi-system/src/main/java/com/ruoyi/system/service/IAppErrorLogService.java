package com.ruoyi.system.service;

import com.ruoyi.system.domain.AppErrorLog;

import java.util.List;

/**
 * APP崩溃日志Service接口
 * 
 * <AUTHOR>
 * @date 2022-09-21
 */
public interface IAppErrorLogService 
{
    /**
     * 查询APP崩溃日志
     * 
     * @param id APP崩溃日志主键
     * @return APP崩溃日志
     */
    public AppErrorLog selectAppErrorLogById(Long id);

    /**
     * 查询APP崩溃日志列表
     * 
     * @param appErrorLog APP崩溃日志
     * @return APP崩溃日志集合
     */
    public List<AppErrorLog> selectAppErrorLogList(AppErrorLog appErrorLog);

    /**
     * 新增APP崩溃日志
     *
     * @param appErrorLog APP崩溃日志
     * @return 结果
     */
    public int insertRedisAppErrorLog(AppErrorLog appErrorLog);

    /**
     * 新增APP崩溃日志
     *
     * @return 结果
     */
    public void insertBatch(List<String> list);

    /**
     * 批量删除APP崩溃日志
     * 
     * @param ids 需要删除的APP崩溃日志主键集合
     * @return 结果
     */
    public int deleteAppErrorLogByIds(Long[] ids);
}
