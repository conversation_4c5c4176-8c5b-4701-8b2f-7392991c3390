package com.ruoyi.system.service;

import com.ruoyi.system.domain.AppUpdateInfo;

import java.util.List;

/**
 * 应用程序更新信息Service接口
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface IAppUpdateInfoService {


    /**
     * 查询应用程序更新信息
     *
     * @param id 应用程序更新信息主键
     * @return 应用程序更新信息
     */
    public AppUpdateInfo selectById(Long id);

    /**
     * 查询应用程序更新信息列表
     *
     * @param appUpdateInfo 应用程序更新信息
     * @return 应用程序更新信息集合
     */
    public List<AppUpdateInfo> selectList(AppUpdateInfo appUpdateInfo);
    /**
     * 新增应用程序更新信息
     *
     * @param appUpdateInfo 应用程序更新信息
     * @return 结果
     */
    public int insert(AppUpdateInfo appUpdateInfo);

    /**
     * 修改应用程序更新信息
     *
     * @param appUpdateInfo 应用程序更新信息
     * @return 结果
     */
    public int update(AppUpdateInfo appUpdateInfo);


    /**
     * 根据appType获取信息
     *
     * @param appType 应用程序更新信息
     * @return 结果
     */
    public AppUpdateInfo queryByType(String appType);

    /**
     * 批量删除应用程序更新信息
     *
     * @param ids 需要删除的应用程序更新信息主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 检查是否存在相同类型和版本的应用程序更新信息
     *
     * @param appType 应用程序类型
     * @param appVersion 应用程序版本
     * @return 存在返回true，不存在返回false
     */
    public boolean existsByTypeAndVersion(String appType, Integer appVersion);

    /**
     * 检查是否存在相同类型和版本的应用程序更新信息（用于编辑）
     *
     * @param appType 应用程序类型
     * @param appVersion 应用程序版本
     * @param id 应用程序更新信息主键
     * @return 存在返回true，不存在返回false
     */
    public boolean existsByTypeAndVersion(String appType, Integer appVersion, Long id);

}
