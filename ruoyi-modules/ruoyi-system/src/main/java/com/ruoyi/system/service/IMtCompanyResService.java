package com.ruoyi.system.service;

import com.ruoyi.system.domain.MtCompanyRes;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;


/**
 * 公司与会议资源Service接口
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@CacheConfig(cacheNames = "IMtCompanyResService")
public interface IMtCompanyResService {

    /**
     * 查询公司与会议资源列表
     *
     * @param companyId 公司id
     * @return 公司与会议资源集合
     */
    public List<MtCompanyRes> selectListByCompanyId(Long companyId);

    /**
     * 查询公司与会议资源列表
     *
     * @return 公司与会议资源集合
     */
    @Cacheable
    public List<MtCompanyRes> selectListAll();

    /**
     * 查询所有资源信息并返回公司名称
     *
     * @return 公司与会议资源集合
     */
    public List<MtCompanyRes> selectCompanyInfoRes();

    /**
     * 新增公司与会议资源
     *
     * @param mtCompanyRes 公司与会议资源
     * @return 结果
     */
    public int insert(MtCompanyRes mtCompanyRes);

    /**
     * 修改公司与会议资源
     *
     * @param mtCompanyRes 公司与会议资源
     * @return 结果
     */
    public int update(MtCompanyRes mtCompanyRes);


    /**
     * 删除公司与会议资源信息
     *
     * @param id 公司与会议资源主键
     * @return 结果
     */
    public int delete(Long id);
}
