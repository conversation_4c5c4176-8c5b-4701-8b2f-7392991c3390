package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.MtMeetingRoom;

import java.util.List;

/**
 * 会议室Service接口
 * 
 * <AUTHOR>
 * @date 2021-12-07
 */
public interface IMtMeetingRoomService extends IService<MtMeetingRoom>
{
    /**
     * 查询会议室
     * 
     * @param id 会议室主键
     * @return 会议室
     */
    public MtMeetingRoom selectMtMeetingRoomById(Long id);

    public MtMeetingRoom selectMtMeetingRoomByDeviceCode(String deviceCode);

    /**
     * 查询会议室列表
     * 
     * @param mtMeetingRoom 会议室
     * @return 会议室集合
     */
    public List<MtMeetingRoom> selectMtMeetingRoomList(MtMeetingRoom mtMeetingRoom);

    /**
     * 新增会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    public int insertMtMeetingRoom(MtMeetingRoom mtMeetingRoom);

    /**
     * 修改会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    public int updateMtMeetingRoom(MtMeetingRoom mtMeetingRoom);

    /**
     * 批量删除会议室
     * 
     * @param ids 需要删除的会议室主键集合
     * @return 结果
     */
    public int deleteMtMeetingRoomByIds(Long[] ids);

    /**
     * 删除会议室信息
     * 
     * @param id 会议室主键
     * @return 结果
     */
    public int deleteMtMeetingRoomById(Long id);
}
