package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.dto.DateAmountInfo;
import com.ruoyi.common.entity.dto.TopicAmountInfo;
import com.ruoyi.common.entity.domain.MtScreenInfo;

import java.util.Date;
import java.util.List;

/**
 * 会议大屏信息Service接口
 *
 * <AUTHOR>
 * @date 2021-12-08
 */
public interface IMtScreenInfoService extends IService<MtScreenInfo> {
    /**
     * 查询会议大屏信息
     *
     * @param deviceCode 会议大屏信息主键
     * @return 会议大屏信息
     */
    public MtScreenInfo selectByDeviceCode(String deviceCode);

    /**
     * 查询会议大屏信息
     *
     * @param id 主键
     * @return 会议大屏信息
     */
    public MtScreenInfo selectOneById(String id);

    /**
     * 查询会议大屏信息列表
     *
     * @return 会议大屏信息集合
     */
    public List<MtScreenInfo> selectList(MtScreenInfo info);

    /**
     * 新增会议大屏信息
     *
     * @param mtScreenInfo 会议大屏信息
     * @return 结果
     */
    public int insert(MtScreenInfo mtScreenInfo);

    /**
     * 修改会议大屏信息
     *
     * @param mtScreenInfo 会议大屏信息
     * @return 结果
     */
    public int update(MtScreenInfo mtScreenInfo);

    /**
     * 批量删除会议大屏信息
     *
     * @param ids 需要删除的会议大屏信息主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除会议大屏信息信息
     *
     * @param deviceCode 会议大屏
     * @return 结果
     */
    public int deleteByDeviceCode(String deviceCode);

    /**
     * 根据订单删除设备
     *
     * @param orderId 订单ID
     * @return 结果
     */
    int deleteByOrderId(Long orderId);

    /**
     * 根据订单ID获取未激活的设备数量
     *
     * @param orderId
     * @return
     */
    public Long selectUnActiveCountByOrder(Long orderId);


    public Long selectCountOfCompanyOrderByState(final int deviceState);

    /**
     * 导入数据
     *
     * @param screenInfos
     * @return
     */

    String importData(List<MtScreenInfo> screenInfos);


    MtScreenInfo selectByActivationCode(String deviceCode);

    List<DateAmountInfo<Integer>> getScreenActiveStatByDate(Date startDate, Date endDate, char filterUnit);

    TopicAmountInfo<Integer> getActiveScreenCountToday();

    List<MtScreenInfo> selectListByOrderCode(String orderCode);
}
