package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.vo.SysUserVo;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.dto.SysAdminUserDto;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface ISysAdminUserService extends IService<SysAdminUser> {

    /**
     * 根据用户名获取用户对象
     *
     * @param userName 用户名
     * @return 操作结果
     */
    SysAdminUser selectUserByUserName(final String userName);

    /**
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysAdminUser user);

    /**
     * 更新用户登录信息
     *
     * @param user 用户信息
     * @return 操作结果
     */
    int updateUserLoginInfo(SysAdminUser user);

    /**
     * 更新用户个人信息
     *
     * @param user 用户信息
     * @return 操作结果
     */
    int updateUserProfileInfo(SysAdminUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysAdminUser> selectAllocatedList(SysAdminUser user, Long roleId);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysAdminUser> selectUnallocatedList(SysAdminUser user, Long roleId);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    String checkPhoneUnique(SysAdminUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    String checkEmailUnique(SysAdminUser user);

    int updatePassword(SysAdminUser user);

    int updateUserAvatar(SysAdminUser user);

    List<SysAdminUser> selectUserList(SysAdminUser user);

    SysAdminUser selectUserById(@NotNull Long userId);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户
     * @return 结果
     */
    String checkUserNameUnique(SysAdminUser user);

    int insertUser(SysAdminUser user);

    int updateUserStatus(SysAdminUser user);

    void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    String importUser(List<SysAdminUser> userList, Boolean isUpdateSupport, String operName);


    /**
     * 根据企业id获取指定企业管理员用户
     * @param companyId
     * @return
     */
    List<SysAdminUser> getCompanyUser(Long companyId);

    /**
     * 获取调试人员列表
     */
    List<SysAdminUser> selectByDeBugList();

    /**
     * 更新用户缓存信息
     * @param user
     * @return
     */
    int resUserInfo(LoginUser user);

    /**
     * 根据权限字符获取列表
     * @param key
     * @return
     */
    List<SysAdminUser> selectUserListByRoleKey(String key);

    /**
     * 查询标识
     * @param userId
     * @return
     */
    List<String> selectSubSystemFlag(Long userId);

    /**
     * 查询企业用户列表
     * @return
     */
    List<SysAdminUser> selectCompanyUserList(SysAdminUserDto adminUserDto);

    /**
     * 根据ids查询用户
     * @param ids
     * @return
     */
    List<SysAdminUser> selectUserByIds(List<Long> ids);

    /**
     * 根据条件分页查询用户列表（排除当前用户）
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysAdminUser> selectUserListExcludeCurrent(SysAdminUser user);

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);
}
