package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysAppUser;
import com.ruoyi.common.entity.domain.SysAppUserRole;

import java.util.List;

public interface ISysAppUserRoleService extends IService<SysAppUserRole> {

    int insertByRole(String roleKey, List<SysAppUser> users);

    int editByUser(Long userId, Long companyId, List<String> roleKeys);

    List<SysAppUserRole> selectByRoleKey(String roleKey, Long companyId);

    List<SysAppUserRole> selectByUser(Long userId);

    int deleteByUser(Long userId);

    int deleteByCompany(Long companyId);
}
