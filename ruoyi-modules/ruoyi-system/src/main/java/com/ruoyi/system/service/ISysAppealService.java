package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysAppeal;

import java.util.List;

/**
 * 申诉信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
public interface ISysAppealService extends IService<SysAppeal>
{
    /**
     * 查询申诉信息
     *
     * @param id 申诉信息主键
     * @return 申诉信息
     */
    public SysAppeal selectSysAppealById(Long id);
    /**
     * 查询申诉信息
     *
     * @param no 投诉单号
     * @return 申诉信息
     */
    public List<SysAppeal> selectSysAppealByNo(String no);

    /**
     * 查询申诉信息列表
     *
     * @param sysAppeal 申诉信息
     * @return 申诉信息集合
     */
    public List<SysAppeal> selectSysAppealList(SysAppeal sysAppeal);

    /**
     * 根据投诉单号参数最后一次申诉信息
     *
     * @param complaintNo 申诉信息
     * @return 申诉信息集合
     */
    public SysAppeal selectLastSysAppeal(String complaintNo);

    /**
     * 新增申诉信息
     *
     * @param sysAppeal 申诉信息
     * @return 结果
     */
    public int insertSysAppeal(SysAppeal sysAppeal);

    /**
     * 修改申诉信息
     *
     * @param sysAppeal 申诉信息
     * @return 结果
     */
    public int updateSysAppeal(SysAppeal sysAppeal);

    /**
     * 批量删除申诉信息
     *
     * @param ids 需要删除的申诉信息主键集合
     * @return 结果
     */
    public int deleteSysAppealByIds(Long[] ids);

    /**
     * 删除申诉信息信息
     *
     * @param id 申诉信息主键
     * @return 结果
     */
    public int deleteSysAppealById(Long id);

    SysAppeal selectUnique(String complaintNo, String uniqueValue);
}
