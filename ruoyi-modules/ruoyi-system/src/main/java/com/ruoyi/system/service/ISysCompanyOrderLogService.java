package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysCompanyOrder;
import com.ruoyi.system.domain.SysCompanyOrderEvent;
import com.ruoyi.system.domain.SysCompanyOrderLog;

import java.util.List;

/**
 * 企业订单操作记录Service接口
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
public interface ISysCompanyOrderLogService extends IService<SysCompanyOrderLog> {
    /**
     * 查询企业订单操作记录
     *
     * @param id 企业订单操作记录主键
     * @return 企业订单操作记录
     */
    public SysCompanyOrderLog selectById(Long id);

    /**
     * 查询企业订单操作记录列表
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 企业订单操作记录集合
     */
    public List<SysCompanyOrderLog> selectList(SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 新增企业订单操作记录
     *
     * @param sysCompanyOrderEvent 企业订单操作事件
     * @return 结果
     */
    public int insert(SysCompanyOrder sysCompanyOrder, SysCompanyOrderEvent sysCompanyOrderEvent);

    /**
     * 新增企业订单操作记录
     *
     * @param sysCompanyOrderEvent 企业订单操作事件
     * @param sysCompanyOrderLog   企业订单操作记录
     * @return 结果
     */
    public int insert(SysCompanyOrder sysCompanyOrder, SysCompanyOrderEvent sysCompanyOrderEvent, SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 修改企业订单操作记录
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 结果
     */
    public int update(SysCompanyOrderLog sysCompanyOrderLog);

    /**
     * 批量删除企业订单操作记录
     *
     * @param ids 需要删除的企业订单操作记录主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除企业订单操作记录信息
     *
     * @param id 企业订单操作记录主键
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 查询完成的签收单照片
     *
     * @param id
     * @param reviewed
     * @return
     */
    String getReceiptImg(Long id, SysCompanyOrderEvent reviewed);
}
