package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.entity.domain.system.SysCompanyResConfig;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface ISysCompanyResConfigService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysCompanyResConfig selectSysCompanyResConfigByCompanyId(Long companyId);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysCompanyResConfig> selectSysCompanyResConfigList(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    public int insertSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    public int updateSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param companyIds 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSysCompanyResConfigByCompanyIds(Long[] companyIds);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysCompanyResConfigByCompanyId(Long companyId);

    /**
     * 根据公司id和用户类型查询公司资源配置
     * @param companyId
     * @param type
     * @return
     */
    SysCompanyResConfig selectCompResConfigDataByType(Long companyId, String type);
}
