package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysComplaintInfo;

import java.util.List;

/**
 * 投诉信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-16
 */
public interface ISysComplaintInfoService extends IService<SysComplaintInfo>
{
    /**
     * 查询投诉信息
     *
     * @param id 投诉信息主键
     * @return 投诉信息
     */
    public SysComplaintInfo selectSysComplaintInfoById(Long id);

    /**
     * 查询投诉信息
     *
     * @param no 投诉单号
     * @return 投诉信息
     */
    public SysComplaintInfo selectSysComplaintInfoByNo(String no);

    /**
     * 查询投诉信息列表
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 投诉信息集合
     */
    public List<SysComplaintInfo> selectSysComplaintInfoList(SysComplaintInfo sysComplaintInfo);

    /**
     * 新增投诉信息
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 结果
     */
    public int insertSysComplaintInfo(SysComplaintInfo sysComplaintInfo);

    /**
     * 修改投诉信息
     * 
     * @param sysComplaintInfo 投诉信息
     * @return 结果
     */
    public int updateSysComplaintInfo(SysComplaintInfo sysComplaintInfo);

    /**
     * 批量删除投诉信息
     * 
     * @param ids 需要删除的投诉信息主键集合
     * @return 结果
     */
    public int deleteSysComplaintInfoByIds(Long[] ids);

    /**
     * 删除投诉信息信息
     * 
     * @param id 投诉信息主键
     * @return 结果
     */
    public int deleteSysComplaintInfoById(Long id);
}
