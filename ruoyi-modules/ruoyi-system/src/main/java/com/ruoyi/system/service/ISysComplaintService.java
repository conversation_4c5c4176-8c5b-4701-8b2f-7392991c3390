package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysComplaint;

import java.util.List;

/**
 * 投诉信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
public interface ISysComplaintService extends IService<SysComplaint>
{
    /**
     * 查询投诉信息
     * 
     * @param id 投诉信息主键
     * @return 投诉信息
     */
    public SysComplaint selectSysComplaintById(Long id);

    /**
     * 查询投诉信息列表
     * 
     * @param sysComplaint 投诉信息
     * @return 投诉信息集合
     */
    public List<SysComplaint> selectSysComplaintList(SysComplaint sysComplaint);

    /**
     * 新增投诉信息
     * 
     * @param sysComplaint 投诉信息
     * @return 结果
     */
    public int insertSysComplaint(SysComplaint sysComplaint);

    /**
     * 修改投诉信息
     * 
     * @param sysComplaint 投诉信息
     * @return 结果
     */
    public int updateSysComplaint(SysComplaint sysComplaint);

    /**
     * 批量删除投诉信息
     * 
     * @param ids 需要删除的投诉信息主键集合
     * @return 结果
     */
    public int deleteSysComplaintByIds(Long[] ids);

    /**
     * 删除投诉信息信息
     * 
     * @param id 投诉信息主键
     * @return 结果
     */
    public int deleteSysComplaintById(Long id);

    SysComplaint selectComplaint(Long userId, Long targetObjId);

    SysComplaint selectByComplaintNo(String complaintNo);
}
