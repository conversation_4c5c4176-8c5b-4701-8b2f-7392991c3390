package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.vo.TreeSelect;

import java.util.List;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService extends IService<SysDept> {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询部门管理数据
     *
     * @param parentId
     * @return
     */
    public List<SysDept> selectOneLevelDepts(Long parentId);

    List<SysDept> selectDeptByPhone(String phone);

    /**
     * 查询全部公司数据
     *
     * @param includeDisabled 是否包含停用数据
     * @return 部门信息集合
     */
    public List<SysDept> selectAllCompanyList(boolean includeDisabled);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    // @Cacheable(value = "sys_dept")
    public SysDept selectDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public String checkDeptNameUnique(SysDept dept);

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    public void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 根据ID查找相应的TreeSelect对象
     *
     * @param treeSelectList TreeSelect对象列表
     * @param id             TreeSelect ID
     * @return
     */
    public TreeSelect findTreeSelectByID(List<TreeSelect> treeSelectList, Long id);

    /**
     * 导入部门数据
     *
     * @param deptList 部门数据列表
     * @return 结果
     */
    public String importData(List<SysDept> deptList, boolean isUpdateSupport, String operName);

    List<SysDept> selectDeptByList(SysDept dept);

    List<SysDept> selectSumDept(Long deptId);

    /**
     * 查询部门列表排序集合
     *
     * @param dept
     * @return
     */
    List<SysDept> selectDeptListSort(SysDept dept);

    List<SysDept> selectDeptListByNotId(SysDept dept);
}
