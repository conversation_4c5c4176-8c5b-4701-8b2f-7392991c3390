package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysDivision;

import java.util.List;

/**
 * 区域信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-07-18
 */
public interface ISysDivisionService extends IService<SysDivision>
{
    /**
     * 查询区域信息
     *
     * @param id 区域信息主键
     * @return 区域信息
     */
    public SysDivision selectById(Long id);

    /**
     * 查询区域信息列表
     *
     * @param sysDivision 区域信息
     * @return 区域信息集合
     */
    public List<SysDivision> selectList(SysDivision sysDivision);

    /**
     * 新增区域信息
     *
     * @param sysDivision 区域信息
     * @return 结果
     */
    public int insert(SysDivision sysDivision);

    /**
     * 修改区域信息
     *
     * @param sysDivision 区域信息
     * @return 结果
     */
    public int update(SysDivision sysDivision);

    /**
     * 删除区域信息信息
     *
     * @param id 区域信息主键
     * @return 结果
     */
    public int deleteById(Long id);

    public List<SysDivision> selectByParent(Long parentId);

    String selectDivision(String divisionNames);

    SysDivision selectDivisionName(String name,Long parentId);
}
