package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysDownload;

import java.util.List;

/**
 * 安装包下载Service接口
 * 
 * <AUTHOR>
 * @date 2022-05-26
 */
public interface ISysDownloadService extends IService<SysDownload>
{
    /**
     * 查询安装包下载
     * 
     * @param id 安装包下载主键
     * @return 安装包下载
     */
    public SysDownload selectById(Long id);

    /**
     * 根据应用类型查询安装包下载
     * @param appType
     * @return
     */
    public SysDownload selectByAppType(String appType);

    /**
     * 查询安装包下载列表
     * 
     * @param sysDownload 安装包下载
     * @return 安装包下载集合
     */
    public List<SysDownload> selectList(SysDownload sysDownload);

    /**
     * 新增安装包下载
     * 
     * @param sysDownload 安装包下载
     * @return 结果
     */
    public int insert(SysDownload sysDownload);

    /**
     * 修改安装包下载
     * 
     * @param sysDownload 安装包下载
     * @return 结果
     */
    public int update(SysDownload sysDownload);

    /**
     * 批量删除安装包下载
     * 
     * @param ids 需要删除的安装包下载主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除安装包下载信息
     * 
     * @param id 安装包下载主键
     * @return 结果
     */
    public int deleteById(Long id);
}
