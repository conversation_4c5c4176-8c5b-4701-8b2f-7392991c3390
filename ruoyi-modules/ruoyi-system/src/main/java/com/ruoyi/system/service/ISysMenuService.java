package com.ruoyi.system.service;

import com.ruoyi.common.entity.domain.SysMenu;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.vo.TreeSelect;
import com.ruoyi.common.redis.service.IBaseService;
import com.ruoyi.system.domain.vo.RouterVo;

import java.util.List;
import java.util.Set;

/**
 * 菜单 业务层
 *
 * <AUTHOR>
 */
public interface ISysMenuService extends IBaseService {
    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu   菜单信息
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId);

    /**
     * 根据用户ID查询权限
     *
     * @param user 用户
     * @return 权限列表
     */
    public Set<String> selectMenuPermsByUserId(SysUser user,String flag);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId, Long parentId,String subSystem);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Integer> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysMenu> menus, Long parentId);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkMenuExistRole(Long menuId);

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public int insertMenu(SysMenu menu);

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public String checkMenuNameUnique(SysMenu menu);

    /**
     * 获取同父级目录
     *
     * @param parentId
     * @return
     */
    public List<SysMenu> selectParenId(Long parentId);

    /**
     * 查询所有菜单Id
     *
     * @return
     */
    public List<Long> getByMenuId();

    /**
     * 获取用户指定menu
     *
     * @param roleIds
     * @param parentId
     * @return
     */
    public List<SysMenu> selectGetUserMenu(String roleIds, Long parentId,String subSystem);

    /**
     * 获取所有的菜单,依据是子系统的父级Id
     *
     * @param parentId
     * @return
     */
    public List<SysMenu> selectMenuTreeAll(Long parentId);

    /**
     * 根据权限字符查询信息
     *
     * @param subSystem 子系统标识
     * @return 菜单信息
     */
    public SysMenu selectByPerms(String subSystem);

    /**
     * 查询非平台级权限菜单
     *
     * @param menu
     * @return
     */
    public List<SysMenu> selectNonPlatformMenuList(SysMenu menu);

    /**
     * 查询子级菜单
     *
     * @param menuId
     * @return
     */
    public List<SysMenu> selectSubMenu(Long menuId);

    /**
     * 更新菜单权限
     *
     * @param menuId
     * @param level
     * @param list
     * @return
     */
    public int updateMenuLevel(Long menuId, String level, List<SysMenu> list);

    List<String> selectMenuByRoleIds(String strIds);

    /**
     * 删除对应菜单所有权限
     *
     * @param menuId 菜单ID
     * @param level 菜单层级
     */
    void deleteMenuRole(Long menuId, String level);

    List<SysMenu> selectChilds(Long menuId);
}
