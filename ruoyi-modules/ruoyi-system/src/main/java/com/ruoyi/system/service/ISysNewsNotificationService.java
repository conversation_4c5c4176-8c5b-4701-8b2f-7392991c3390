package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysNewsNotification;

import java.util.List;

/**
 * 消息管理Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-21
 */
public interface ISysNewsNotificationService extends IService<SysNewsNotification>
{
    /**
     * 查询消息管理
     * 
     * @param id 消息管理主键
     * @return 消息管理
     */
    public SysNewsNotification selectById(Long id);

    /**
     * 查询消息管理列表
     * 
     * @param sysNewsNotification 消息管理
     * @return 消息管理集合
     */
    public List<SysNewsNotification> selectList(SysNewsNotification sysNewsNotification);

    /**
     * 新增消息管理
     * 
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    public int insert(SysNewsNotification sysNewsNotification);

    /**
     * 修改消息管理
     * 
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    public int update(SysNewsNotification sysNewsNotification);

    /**
     * 批量删除消息管理
     * 
     * @param ids 需要删除的消息管理主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除消息管理信息
     * 
     * @param id 消息管理主键
     * @return 结果
     */
    public int deleteById(Long id);


    SysNewsNotification selectByType(Integer type);
}
