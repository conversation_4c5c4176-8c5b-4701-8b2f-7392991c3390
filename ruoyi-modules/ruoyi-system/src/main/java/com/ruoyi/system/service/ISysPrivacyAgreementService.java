package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysPrivacyAgreement;

import java.util.List;


/**
 * 隐私协议Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-15
 */
public interface ISysPrivacyAgreementService extends IService<SysPrivacyAgreement>
{
    /**
     * 查询隐私协议
     * 
     * @param id 隐私协议主键
     * @return 隐私协议
     */
    public SysPrivacyAgreement selectById(Long id);

    /**
     * 查询隐私协议列表
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 隐私协议集合
     */
    public List<SysPrivacyAgreement> selectList(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 新增隐私协议
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    public int insert(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 修改隐私协议
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    public int update(SysPrivacyAgreement sysPrivacyAgreement);

    /**
     * 批量删除隐私协议
     * 
     * @param ids 需要删除的隐私协议主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除隐私协议信息
     * 
     * @param id 隐私协议主键
     * @return 结果
     */
    public int deleteById(Long id);
}
