package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysProblemFeedback;

import java.util.List;

/**
 * 问题反馈Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-14
 */
public interface ISysProblemFeedbackService extends IService<SysProblemFeedback>
{
    /**
     * 查询问题反馈
     * 
     * @param id 问题反馈主键
     * @return 问题反馈
     */
    public SysProblemFeedback selectById(Long id);

    /**
     * 查询问题反馈列表
     * 
     * @param sysProblemFeedback 问题反馈
     * @return 问题反馈集合
     */
    public List<SysProblemFeedback> selectList(SysProblemFeedback sysProblemFeedback);

    /**
     * 新增问题反馈
     * 
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    public int insert(SysProblemFeedback sysProblemFeedback);

    /**
     * 修改问题反馈
     * 
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    public int update(SysProblemFeedback sysProblemFeedback);

    /**
     * 批量删除问题反馈
     * 
     * @param ids 需要删除的问题反馈主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除问题反馈信息
     * 
     * @param id 问题反馈主键
     * @return 结果
     */
    public int deleteById(Long id);
}
