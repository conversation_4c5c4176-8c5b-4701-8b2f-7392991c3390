package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.entity.domain.SysPunish;

import java.util.List;

/**
 * 处罚信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
public interface ISysPunishService extends IService<SysPunish>
{
    /**
     * 查询处罚信息
     *
     * @param id 处罚信息主键
     * @return 处罚信息
     */
    public SysPunish selectSysPunishById(Long id);
    /**
     * 查询处罚信息
     *
     * @param no 投诉单号
     * @return 处罚信息
     */
    public SysPunish selectSysPunishByNo(String no);

    /**
     * 查询处罚信息列表
     *
     * @param sysPunish 处罚信息
     * @return 处罚信息集合
     */
    public List<SysPunish> selectSysPunishList(SysPunish sysPunish);

    /**
     * 查询处罚信息列表
     *
     * @param targetObjId 被投诉人ID
     * @return 处罚信息集合
     */
    public List<SysPunish> selectSysPunishList(Long targetObjId);

    /**
     * 新增处罚信息
     * 
     * @param sysPunish 处罚信息
     * @return 结果
     */
    public int insertSysPunish(SysPunish sysPunish);

    /**
     * 修改处罚信息
     * 
     * @param sysPunish 处罚信息
     * @return 结果
     */
    public int updateSysPunish(SysPunish sysPunish);

    /**
     * 批量删除处罚信息
     * 
     * @param ids 需要删除的处罚信息主键集合
     * @return 结果
     */
    public int deleteSysPunishByIds(Long[] ids);

    /**
     * 删除处罚信息信息
     * 
     * @param id 处罚信息主键
     * @return 结果
     */
    public int deleteSysPunishById(Long id);

    /**
     * 更新订单处罚状态
     * @param complaintNo
     * @return
     */
    SysPunish selectSysPunish(String complaintNo);
}
