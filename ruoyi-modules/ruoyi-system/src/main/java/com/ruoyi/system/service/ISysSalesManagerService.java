package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysSalesManager;
import com.ruoyi.system.domain.statistics.SalesManagerStatInfo;

import java.util.Date;
import java.util.List;

/**
 * 销售经理信息Service接口
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
public interface ISysSalesManagerService extends IService<SysSalesManager>
{
    /**
     * 查询销售经理信息
     *
     * @param id 销售经理信息主键
     * @return 销售经理信息
     */
    public SysSalesManager selectById(Long id);

    /**
     * 查询销售经理信息列表
     *
     * @param sysSalesManager 销售经理信息
     * @return 销售经理信息集合
     */
    public List<SysSalesManager> selectList(SysSalesManager sysSalesManager);


    public List<SalesManagerStatInfo> getSalesManagerStats(Date startTime, Date endTime);

    /**
     * 新增销售经理信息
     *
     * @param sysSalesManager 销售经理信息
     * @return 结果
     */
    public int insert(SysSalesManager sysSalesManager);

    /**
     * 修改销售经理信息
     *
     * @param sysSalesManager 销售经理信息
     * @return 结果
     */
    public int update(SysSalesManager sysSalesManager);

    /**
     * 批量删除销售经理信息
     *
     * @param ids 需要删除的销售经理信息主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除销售经理信息信息
     *
     * @param id 销售经理信息主键
     * @return 结果
     */
    public int deleteById(Long id);

    SysSalesManager selectSalesManager(String phone,String name);

    SysSalesManager selectSalesManager(SysSalesManager salesManager);
}
