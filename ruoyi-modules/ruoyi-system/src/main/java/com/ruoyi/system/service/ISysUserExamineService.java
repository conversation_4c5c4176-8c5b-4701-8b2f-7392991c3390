package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.SysUserExamine;

import java.util.List;

/**
 * 用户申请企业审核Service接口
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
public interface ISysUserExamineService extends IService<SysUserExamine> {
    /**
     * 查询用户申请企业审核
     *
     * @param id 用户申请企业审核主键
     * @return 用户申请企业审核
     */
    public SysUserExamine selectById(Long id);

    /**
     * 查询用户申请企业审核列表
     *
     * @param sysUserExamine 用户申请企业审核
     * @return 用户申请企业审核集合
     */
    public List<SysUserExamine> selectList(SysUserExamine sysUserExamine);

    /**
     * 查询用户申请企业审核总数
     *
     * @param sysUserExamine 用户申请企业审核
     * @return 数量
     */
    public Long selectCount(SysUserExamine sysUserExamine);

    /**
     * 新增用户申请企业审核
     *
     * @param sysUserExamine 用户申请企业审核
     * @return 结果
     */
    public int insert(SysUserExamine sysUserExamine);

    /**
     * 修改用户申请企业审核
     *
     * @param sysUserExamine 用户申请企业审核
     * @return 结果
     */
    public int update(SysUserExamine sysUserExamine);

    /**
     * 批量删除用户申请企业审核
     *
     * @param ids 需要删除的用户申请企业审核主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除用户申请企业审核信息
     *
     * @param id 用户申请企业审核主键
     * @return 结果
     */
    public int deleteById(Long id);

    boolean examine(SysUserExamine sysUserExamine);

    /**
     * 获取用户最后一条审核信息
     * @param userId 用户ID
     * @return 审核信息
     */
    SysUserExamine selectUserLastExamine(Long userId);

    /**
     * 用户取消申请
     * @param userId 用户ID
     * @return 结果
     */
    int cancelExamineByUser(Long userId);

    Long selectExamineCount(Long companyId);
}
