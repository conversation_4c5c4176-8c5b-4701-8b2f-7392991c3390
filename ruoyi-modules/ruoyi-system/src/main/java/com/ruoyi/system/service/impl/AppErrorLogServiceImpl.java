package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.util.JSON;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.system.domain.AppErrorLog;
import com.ruoyi.system.mapper.AppErrorLogMapper;
import com.ruoyi.system.queue.AppErrorLogQueue;
import com.ruoyi.system.service.IAppErrorLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * APP崩溃日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-09-21
 */
@Service
public class AppErrorLogServiceImpl extends ServiceImpl<AppErrorLogMapper, AppErrorLog> implements IAppErrorLogService
{
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AppErrorLogMapper appErrorLogMapper;

    /**
     * 查询APP崩溃日志
     * 
     * @param id APP崩溃日志主键
     * @return APP崩溃日志
     */
    @Override
    public AppErrorLog selectAppErrorLogById(Long id)
    {
        return appErrorLogMapper.selectAppErrorLogById(id);
    }

    /**
     * 查询APP崩溃日志列表
     * 
     * @param appErrorLog APP崩溃日志
     * @return APP崩溃日志
     */
    @Override
    public List<AppErrorLog> selectAppErrorLogList(AppErrorLog appErrorLog)
    {
        return appErrorLogMapper.selectAppErrorLogList(appErrorLog);
    }

    /**
     * 新增APP崩溃日志至redis
     *
     * @param appErrorLog APP崩溃日志
     * @return hKey
     */
    @Override
    public int insertRedisAppErrorLog(AppErrorLog appErrorLog)
    {
        appErrorLog.setLogTime(DateUtils.getNowDate());
        long rows = redisCache.setCacheListLpush(Constants.APP_ERROR_LOGKEY, JSON.toJSONString(appErrorLog));
        AppErrorLogQueue appErrorLogQueue = AppErrorLogQueue.getInstance();
        if(rows != 0 && appErrorLogQueue.size() == 0){
            appErrorLogQueue.push(Constants.APP_ERROR_LOGKEY);
        }
        return rows == 0 ? 0 : 1;
    }

    /**
     * 批量新增APP崩溃日志
     *
     * @return 结果
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void insertBatch(List<String> list){
        List<AppErrorLog> objList = new ArrayList<>();
        for (String str: list) {
            AppErrorLog appErrorLog = JSON.parseObject(str, AppErrorLog.class);
            objList.add(appErrorLog);
        }
        appErrorLogMapper.insertBatch(objList);
    }

    /**
     * 批量删除APP崩溃日志
     * 
     * @param ids 需要删除的APP崩溃日志主键
     * @return 结果
     */
    @Override
    public int deleteAppErrorLogByIds(Long[] ids)
    {
        return appErrorLogMapper.deleteAppErrorLogByIds(ids);
    }
}
