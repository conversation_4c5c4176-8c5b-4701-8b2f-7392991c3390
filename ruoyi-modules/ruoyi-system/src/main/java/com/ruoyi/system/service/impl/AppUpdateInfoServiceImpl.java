package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.TimeUtils;
import com.ruoyi.system.domain.AppUpdateInfo;
import com.ruoyi.system.mapper.AppUpdateInfoMapper;
import com.ruoyi.system.service.IAppUpdateInfoService;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 应用程序更新信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Service
public class AppUpdateInfoServiceImpl implements IAppUpdateInfoService {
    @Autowired
    private AppUpdateInfoMapper mapper;

    @Value("${oss.showAddress}")
    private String showAddress;

    @Value("${oss.localAddress}")
    private String localAddress;


    @Override
    public AppUpdateInfo selectById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public List<AppUpdateInfo> selectList(AppUpdateInfo info) {
        QueryWrapper<AppUpdateInfo> queryWrapper = new QueryWrapper<>();
        Long id = info.getId();
        if (id != null) {
            queryWrapper.eq("id", id);
        }
        String appType = info.getAppType();
        if (!TextUtils.isEmpty(appType)) {
            queryWrapper.eq("app_type", appType);
        }
        Integer appVersion = info.getAppVersion();
        if (appVersion != null) {
            queryWrapper.eq("app_version", appVersion);
        }
        queryWrapper.orderByDesc("create_time");
        return mapper.selectList(queryWrapper);
    }

    @Override
    public int insert(AppUpdateInfo appUpdateInfo) {
//        if("1".equals(appUpdateInfo.getFileType())){
//            appUpdateInfo.setAppUrl(appUpdateInfo.getAppUrl().replace(showAddress,localAddress));
//        }

        appUpdateInfo.setCreateTime(TimeUtils.getNowDate());
        return mapper.insert(appUpdateInfo);
    }

    @Override
    public int update(AppUpdateInfo appUpdateInfo) {
        appUpdateInfo.setUpdateTime(TimeUtils.getNowDate());
        return mapper.updateById(appUpdateInfo);
    }

    @Override
    public AppUpdateInfo queryByType(String appType) {
        QueryWrapper<AppUpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_type", appType);
        queryWrapper.eq("status",0);
        queryWrapper.orderByDesc("app_version");
        queryWrapper.last("limit 1");

        List<AppUpdateInfo> list = mapper.selectList(queryWrapper);
        if (!StringUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int deleteByIds(Long[] ids) {
        return mapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public boolean existsByTypeAndVersion(String appType, Integer appVersion) {
        QueryWrapper<AppUpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_type", appType);
        queryWrapper.eq("app_version", appVersion);
        return mapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByTypeAndVersion(String appType, Integer appVersion, Long id) {
        QueryWrapper<AppUpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_type", appType);
        queryWrapper.eq("app_version", appVersion);
        queryWrapper.ne("id", id); // Exclude the current record
        return mapper.selectCount(queryWrapper) > 0;
    }

}
