package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.MtCompanyRes;
import com.ruoyi.system.mapper.MtCompanyResMapper;
import com.ruoyi.system.service.IMtCompanyResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 公司与会议资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Service
public class MtCompanyResServiceImpl implements IMtCompanyResService {

    @Autowired
    private MtCompanyResMapper mtCompanyResMapper;

    @Override
    public List<MtCompanyRes> selectListByCompanyId(Long companyId) {
        Map<String, Object> map = new HashMap<>();
        map.put("company_id", companyId);
        return mtCompanyResMapper.selectByMap(map);
    }

    @Override
    public List<MtCompanyRes> selectListAll() {
        return mtCompanyResMapper.selectList(null);
    }

    @Override
    public List<MtCompanyRes> selectCompanyInfoRes() {
        return mtCompanyResMapper.selectCompanyInfoRes();
    }

    @Override
    public int insert(MtCompanyRes mtCompanyRes) {
        return mtCompanyResMapper.insert(mtCompanyRes);
    }

    @Override
    public int update(MtCompanyRes mtCompanyRes) {
        return mtCompanyResMapper.updateById(mtCompanyRes);
    }

    @Override
    public int delete(Long id) {
        return mtCompanyResMapper.deleteById(id);
    }
}
