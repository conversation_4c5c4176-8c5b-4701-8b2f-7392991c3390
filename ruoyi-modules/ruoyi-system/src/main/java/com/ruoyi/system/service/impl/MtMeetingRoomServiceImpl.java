package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.MtMeetingRoom;
import com.ruoyi.system.mapper.MtMeetingRoomMapper;
import com.ruoyi.system.service.IMtMeetingRoomService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 会议室Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-12-07
 */
@Service
public class MtMeetingRoomServiceImpl extends ServiceImpl<MtMeetingRoomMapper, MtMeetingRoom> implements IMtMeetingRoomService
{

    /**
     * 查询会议室
     * 
     * @param id 会议室主键
     * @return 会议室
     */
    @Override
    public MtMeetingRoom selectMtMeetingRoomById(Long id)
    {
        return baseMapper.selectMtMeetingRoomById(id);
    }

    @Override
    public MtMeetingRoom selectMtMeetingRoomByDeviceCode(String deviceCode) {
        LambdaQueryWrapper<MtMeetingRoom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MtMeetingRoom::getCreateBy, deviceCode);
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 查询会议室列表
     * 
     * @param mtMeetingRoom 会议室
     * @return 会议室
     */
    @Override
    public List<MtMeetingRoom> selectMtMeetingRoomList(MtMeetingRoom mtMeetingRoom)
    {
        return baseMapper.selectMtMeetingRoomList(mtMeetingRoom);
    }

    /**
     * 新增会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    @Override
    public int insertMtMeetingRoom(MtMeetingRoom mtMeetingRoom)
    {
        mtMeetingRoom.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insertMtMeetingRoom(mtMeetingRoom);
    }

    /**
     * 修改会议室
     * 
     * @param mtMeetingRoom 会议室
     * @return 结果
     */
    @Override
    public int updateMtMeetingRoom(MtMeetingRoom mtMeetingRoom)
    {
        mtMeetingRoom.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateMtMeetingRoom(mtMeetingRoom);
    }

    /**
     * 批量删除会议室
     * 
     * @param ids 需要删除的会议室主键
     * @return 结果
     */
    @Override
    public int deleteMtMeetingRoomByIds(Long[] ids)
    {
        return baseMapper.deleteMtMeetingRoomByIds(ids);
    }

    /**
     * 删除会议室信息
     * 
     * @param id 会议室主键
     * @return 结果
     */
    @Override
    public int deleteMtMeetingRoomById(Long id)
    {
        return baseMapper.deleteMtMeetingRoomById(id);
    }
}
