package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.ExceptionUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.OrderIdGenerator;
import com.ruoyi.common.entity.dto.DateAmountInfo;
import com.ruoyi.common.entity.dto.TopicAmountInfo;
import com.ruoyi.common.entity.domain.MtScreenInfo;
import com.ruoyi.system.mapper.MtScreenInfoMapper;
import com.ruoyi.system.service.IMtScreenInfoService;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.*;

/**
 * 会议大屏信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-08
 */
@Service
public class MtScreenInfoServiceImpl extends ServiceImpl<MtScreenInfoMapper, MtScreenInfo> implements IMtScreenInfoService {
    @Override
    public MtScreenInfo selectByDeviceCode(String deviceCode) {
        QueryWrapper<MtScreenInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_code", deviceCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public MtScreenInfo selectByActivationCode(String deviceCode) {
        QueryWrapper<MtScreenInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activation_code", deviceCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<DateAmountInfo<Integer>> getScreenActiveStatByDate(Date startDate, Date endDate, char filterUnit) {
        String filter = DateAmountInfo.getFilterStrByFilterUnit(filterUnit);
        return baseMapper.getScreenActiveStatByDate(startDate, endDate, filter);
    }

    @Override
    public TopicAmountInfo<Integer> getActiveScreenCountToday() {
        List<TopicAmountInfo<Integer>> list = baseMapper.getActiveScreenCountToday();
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<MtScreenInfo> selectListByOrderCode(String orderCode) {
        LambdaQueryWrapper<MtScreenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(orderCode!=null ,MtScreenInfo::getOrderNumber,orderCode);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public MtScreenInfo selectOneById(String id) {
        return baseMapper.selectById(id);
    }


    @Override
    public List<MtScreenInfo> selectList(MtScreenInfo info) {
        QueryWrapper<MtScreenInfo> queryWrapper = QueryBuilder.toQueryWrapper(info);
        if (StringUtils.isNotBlank(info.getActivateBeginTime())) {
            queryWrapper.lambda().ge(MtScreenInfo::getActivateTime, DateUtils.fillUpDateTime(info.getActivateBeginTime()));
        }
        if (StringUtils.isNotBlank(info.getActivateEndTime())) {
            Date activateEndTime = DateUtils.parseDate(info.getActivateEndTime());
            Date activateEndDate = DateUtils.addDays(activateEndTime, 1);
            queryWrapper.lambda().lt(MtScreenInfo::getActivateTime, DateUtils.fillUpDateTime(DateUtils.dateTime(activateEndDate)));
        }
        if (StringUtils.isNotBlank(info.getInstallBeginTime())) {
            queryWrapper.lambda().ge(MtScreenInfo::getInstallTime, info.getInstallBeginTime());
        }
        if (StringUtils.isNotBlank(info.getInstallEndTime())) {
            queryWrapper.lambda().le(MtScreenInfo::getInstallTime, info.getInstallEndTime());
        }
        if (StringUtils.isNotEmpty(info.getDeviceCode())) {
            queryWrapper.lambda().like(MtScreenInfo::getDeviceCode, info.getDeviceCode());
        }
        // sql中子查询已经增加了排序，所以不在需要外层进行排序
//        queryWrapper.lambda().orderByDesc(MtScreenInfo::getCreateTime);
        return baseMapper.list(queryWrapper);
    }

    @Override
    public int insert(MtScreenInfo mtScreenInfo) {
        int insert = -1;
        try {
            if (mtScreenInfo.getDepts()==null) {
                mtScreenInfo.setDepts(mtScreenInfo.getCompanyId()+"");
            }
            mtScreenInfo.setActivationCode(OrderIdGenerator.getCode(6, null, true, true));
            mtScreenInfo.setCreateTime(DateUtils.getNowDate());
            insert = baseMapper.insert(mtScreenInfo);
        } catch (DataAccessException dataAccessException) {
            Throwable cause = dataAccessException.getCause();
            if (cause instanceof SQLIntegrityConstraintViolationException) {
                ExceptionUtils.throwException("设备码不能重复");
            }
        }

        return insert;
    }

    @Override
    public int update(MtScreenInfo mtScreenInfo) {
        int insert = -1;
        try {
            insert = baseMapper.updateById(mtScreenInfo);
        } catch (DataAccessException dataAccessException) {
            Throwable cause = dataAccessException.getCause();
            if (cause instanceof SQLIntegrityConstraintViolationException) {
                ExceptionUtils.throwException("设备码不能重复");
            }
        }
        return insert;
    }

    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public int deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<MtScreenInfo> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(MtScreenInfo::getOrderId, orderId);

        return baseMapper.delete(deleteWrapper);
    }

    @Override
    public int deleteByDeviceCode(String deviceCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("device_code", deviceCode);
        return baseMapper.deleteByMap(map);
    }

    @Override
    public Long selectUnActiveCountByOrder(Long orderId) {
        LambdaQueryWrapper<MtScreenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MtScreenInfo::getOrderId, orderId);
        queryWrapper.eq(MtScreenInfo::getDeviceState, 0);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Long selectCountOfCompanyOrderByState(final int deviceState) {
        LambdaQueryWrapper<MtScreenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(MtScreenInfo::getOrderId);
        queryWrapper.eq(MtScreenInfo::getDeviceState, deviceState);

        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public String importData(List<MtScreenInfo> screenInfos) {
        if (StringUtils.isNull(screenInfos) || screenInfos.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (MtScreenInfo info : screenInfos) {

            try {
                QueryWrapper<MtScreenInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("device_code", info.getDeviceCode());
                // 验证是否存在这个用户
                MtScreenInfo screenInfo;
                if (StringUtils.isNotEmpty(info.getDeviceCode())) {
                    screenInfo = baseMapper.selectOne(wrapper);
                } else {
                    screenInfo = null;
                    //置空，防止插入时校验数据列UNIQUE
                    info.setDeviceCode(null);
                }

                if (StringUtils.isNull(screenInfo) || StringUtils.isBlank(info.getDeviceCode())) {
                    info.setCreateTime(DateUtils.getNowDate());
                    info.setActivationCode(OrderIdGenerator.getCode(6, null, true, true));
                    baseMapper.insert(info);
                    successNum++;
                    //  successMsg.append("<br/>" + successNum + "、账号 " + info.getDeviceCode() + " 导入成功");
                } else {
                    baseMapper.update(info, wrapper);
                    successNum++;
                    //   successMsg.append("<br/>" + successNum + "、账号 " + info.getDeviceCode() + " 更新成功");
                }
            } catch (Exception e) {
                failureNum++;
               /* String msg = "<br/>" + failureNum + "、账号 " + info.getDeviceCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());*/
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }


}
