package com.ruoyi.system.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.enums.SmsCodeUsage;
import com.ruoyi.system.service.ISMService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class SMServiceImpl implements ISMService {

    @Autowired
    private ISysConfigService configService;

    private static final String SYSCONFIG_KEY_SMS_AKID = "sms.akid";
    private static final String SYSCONFIG_KEY_SMS_AS = "sms.as";

    private static final Map<String, String> errorMessageMap = new HashMap<String, String>() {{
        put("OK", "短信发送成功");
        put("isp.RAM_PERMISSION_DENY", "RAM权限被拒绝");
        put("isv.OUT_OF_SERVICE", "短信服务不可用");
        put("isv.PRODUCT_UN_SUBSCRIPT", "未开通云通信产品");
        put("isv.PRODUCT_UNSUBSCRIBE", "产品未开通");
        put("isv.ACCOUNT_NOT_EXISTS", "短信发送账户不存在");
        put("isv.ACCOUNT_ABNORMAL", "短信发送账户异常");
        put("isv.TTS_TEMPLATE_ILLEGAL", "短信发送模板不合法");
        put("isv.DISPLAY_NUMBER_ILLEGAL", "号显不合法");
        put("isv.TEMPLATE_MISSING_PARAMETERS", "文本转语音模板参数缺失");
        put("sv.BLACK_KEY_CONTROL_LIMIT", "模板变量中存在黑名单关键字");
        put("isv.INVALID_PARAMETERS", "参数异常");
        put("isv.PARAM_NOT_SUPPORT_URL", "变量不支持URL参数");
        put("isp.SYSTEM_ERROR", "系统错误");
        put("isv.MOBILE_NUMBER_ILLEGAL", "号码格式非法");
        put("isv.BUSINESS_LIMIT_CONTROL", "短信发送已达到今日最大条数");
        put("isv.PARAM_LENGTH_LIMIT", "参数长度受限");
    }};

    @Override
    public boolean SendMessage(String telephone, String templateParam, SmsCodeUsage smsCodeUsage) {
        // 可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        String smsAkId = configService.selectConfigByKey(SYSCONFIG_KEY_SMS_AKID);
        String smsAs = configService.selectConfigByKey(SYSCONFIG_KEY_SMS_AS);
        // 初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsAkId, smsAs);
        //DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
        IAcsClient acsClient = new DefaultAcsClient(profile);

        // 组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName("汇智云屏");
        // 必填:待发送手机号
        request.setPhoneNumbers(telephone);
        String templateCodeKey = smsCodeUsage.getSmsConfigKey();
        request.setTemplateCode(configService.selectConfigByKey(templateCodeKey));

        if (StringUtils.isNotEmpty(templateParam)) {
            request.setTemplateParam(templateParam);
        }

        // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        // request.setSmsUpExtendCode("90997");

        // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        //request.setOutId("yourOutId");

        // hint 此处可能会抛出异常，注意catch
        try {
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                return true;
            }
            log.error("SMS发送短信失败:" + sendSmsResponse.getCode());

        } catch (ClientException e) {
            log.error("SMS发送短信失败:" + e.getMessage());
        }
        return false;
    }

    public String getErrorMessage(String errorCode) {
        return errorMessageMap.get(errorCode);
    }
}
