package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.Binder;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.domain.vo.SysUserVo;
import com.ruoyi.common.entity.mapper.SysAdminUserMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.utils.UserRedisUtils;
import com.ruoyi.knowledgebase.api.RemoteKnowledgebaseService;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.domain.dto.SysAdminUserDto;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SysAdminUserServiceImpl extends ServiceImpl<SysAdminUserMapper, SysAdminUser> implements ISysAdminUserService {
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private RemoteKnowledgebaseService remoteKnowledgebaseService;

    @Override
    public SysAdminUser selectUserByUserName(String userName) {
        SysAdminUser adminUser = baseMapper.selectByName(userName);
        if (adminUser != null) {
            Binder.bindRelations(adminUser);
        }
        return adminUser;
    }

    @Override
    public int updateUser(SysAdminUser user) {
        user.setUpdateTime(DateUtils.getNowDate());
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(user.getUserId());
        // 新增用户与角色管理
        Long[] ids = user.getRoleIds().toArray(new Long[0]);
        insertUserRole(user.getUserId(), ids);

        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(user.getUserName() != null, SysAdminUser::getUserName, user.getUserName());
        updateWrapper.set(user.getEmail() != null, SysAdminUser::getEmail, user.getEmail());
        updateWrapper.set(user.getPhonenumber() != null, SysAdminUser::getPhonenumber, user.getPhonenumber());
        updateWrapper.set(user.getNickName() != null, SysAdminUser::getNickName, user.getNickName());
        updateWrapper.set(SysAdminUser::getCompanyId, user.getCompanyId());
        updateWrapper.set(user.getRemark() != null, SysAdminUser::getRemark, user.getRemark());
        //公司如果为空，update语句需要置空
        //updateWrapper.set(user.getAvatar() != null, SysAdminUser::getAvatar, user.getAvatar());
        updateWrapper.set(user.getUpdateBy() != null, SysAdminUser::getUpdateBy, user.getUpdateBy());
        updateWrapper.set(user.getUpdateTime() != null, SysAdminUser::getUpdateTime, user.getUpdateTime());
        updateWrapper.set(user.getStatus() != null, SysAdminUser::getStatus, user.getStatus());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int updateUserLoginInfo(SysAdminUser user) {
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysAdminUser::getLoginIp, user.getLoginIp());
        updateWrapper.set(SysAdminUser::getLoginDate, user.getLoginDate());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int updateUserProfileInfo(SysAdminUser user) {
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(user.getNickName() != null, SysAdminUser::getNickName, user.getNickName());
        updateWrapper.set(user.getPhonenumber() != null, SysAdminUser::getPhonenumber, user.getPhonenumber());
        updateWrapper.set(user.getEmail() != null, SysAdminUser::getEmail, user.getEmail());
        updateWrapper.set(user.getAvatar() != null, SysAdminUser::getAvatar, user.getAvatar());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<SysAdminUser> selectAllocatedList(SysAdminUser user, Long roleId) {
        LambdaQueryWrapper<SysAdminUser> queryWrapper = getQueryWrapperForAllocateQuery(user);
        return baseMapper.selectAllocatedList(queryWrapper, roleId);
    }

    @Override
    public List<SysAdminUser> selectUnallocatedList(SysAdminUser user, Long roleId) {
        LambdaQueryWrapper<SysAdminUser> queryWrapper = getQueryWrapperForAllocateQuery(user);
        return baseMapper.selectUnallocatedList(queryWrapper, roleId);
    }

    private LambdaQueryWrapper<SysAdminUser> getQueryWrapperForAllocateQuery(SysAdminUser adminUser) {
        LambdaQueryWrapper<SysAdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(adminUser.getUserName() != null, SysAdminUser::getUserName, adminUser.getUserName());
        queryWrapper.like(adminUser.getNickName() != null, SysAdminUser::getNickName, adminUser.getNickName());
        queryWrapper.like(adminUser.getPhonenumber() != null, SysAdminUser::getPhonenumber, adminUser.getPhonenumber());
        return queryWrapper;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysAdminUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        LambdaQueryWrapper<SysAdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(user.getPhonenumber() != null, SysAdminUser::getPhonenumber, user.getPhonenumber());
        List<SysAdminUser> list = baseMapper.selectList(queryWrapper);
        if (list == null || list.size() == 0){
            return UserConstants.UNIQUE;
        }
        SysAdminUser info = list.get(0);
        if (StringUtils.isNotNull(info)
                && info.getUserId().longValue() != userId.longValue()
                && info.getUserTypeEnum() == user.getUserTypeEnum()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysAdminUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        LambdaQueryWrapper<SysAdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(user.getEmail() != null, SysAdminUser::getEmail, user.getEmail());
        SysAdminUser info = baseMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public int updatePassword(SysAdminUser user) {
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysAdminUser::getPassword, user.getPassword());
        updateWrapper.set(SysAdminUser::getUpdateBy, user.getUpdateBy());
        updateWrapper.set(SysAdminUser::getUpdateTime, DateUtils.getNowDate());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int updateUserAvatar(SysAdminUser user) {
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysAdminUser::getAvatar, user.getAvatar());
        updateWrapper.set(SysAdminUser::getUpdateBy, user.getUpdateBy());
        updateWrapper.set(SysAdminUser::getUpdateTime, DateUtils.getNowDate());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<SysAdminUser> selectUserList(SysAdminUser user) {
        QueryWrapper<SysAdminUser> sysUserQueryWrapper = new QueryWrapper<>();
        if (user.getRoleId() != null) {
            sysUserQueryWrapper.apply("admin.user_id in (select user_id from sys_user_role where role_id= " + user.getRoleId() + ")");
        }
        sysUserQueryWrapper.eq(user.getUserId() != null, "admin.user_id", user.getUserId());
        sysUserQueryWrapper.like(user.getUserName() != null, "admin.user_name", user.getUserName());
        sysUserQueryWrapper.like(user.getNickName() != null, "admin.nick_name", user.getNickName());
        sysUserQueryWrapper.eq(user.getStatus() != null, "admin.status", user.getStatus());
        sysUserQueryWrapper.like(user.getPhonenumber() != null, "admin.phonenumber", user.getPhonenumber());
        sysUserQueryWrapper.eq(user.getCompanyId() != null, "admin.company_id", user.getCompanyId());
        // 只查询未删除的用户
        sysUserQueryWrapper.eq("admin.del_flag", "0");
        Date beginDate = DateUtils.parseDate(user.getParams().get("beginTime"));
        Date endDate = DateUtils.parseDate(user.getParams().get("endTime"));
        endDate = endDate != null ? DateUtils.addDays(endDate, 1) : null;
        sysUserQueryWrapper.ge(user.getParams().get("beginTime") != null && beginDate != null, "admin.create_time", beginDate);
        sysUserQueryWrapper.lt(user.getParams().get("endTime") != null && endDate != null, "admin.create_time", endDate);

        sysUserQueryWrapper.orderByDesc("admin.create_time");
        List<SysAdminUser> list = baseMapper.selectUserList(sysUserQueryWrapper, user.getRoleId());
        Binder.bindRelations(list);
        return list;
    }

    @Override
    public SysAdminUser selectUserById(@NotNull Long userId) {
        return baseMapper.selectById(userId);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysAdminUser user) {
        LambdaQueryWrapper<SysAdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAdminUser::getUserName, user.getUserName());
        queryWrapper.eq(SysAdminUser::getDelFlag, '0');

        SysAdminUser info = baseMapper.selectOne(queryWrapper);
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public int insertUser(SysAdminUser user) {
        user.setCreateTime(DateUtils.getNowDate());
        int rows = baseMapper.insert(user);
        // 新增用户与角色管理
        Long[] ids = user.getRoleIds().toArray(new Long[0]);
        insertUserRole(user.getUserId(), ids);
        return rows;
    }

    /**
     * 更新用户可用状态
     *
     * @param user
     * @return
     */
    @Override
    public int updateUserStatus(SysAdminUser user) {
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysAdminUser::getStatus, user.getStatus());
        updateWrapper.set(SysAdminUser::getUpdateBy, user.getUpdateBy());
        updateWrapper.set(SysAdminUser::getUpdateTime, DateUtils.getNowDate());
        updateWrapper.eq(SysAdminUser::getUserId, user.getUserId());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    private void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotNull(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysAdminUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysAdminUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysAdminUser u = selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    user.setCreateTime(DateUtils.getNowDate());
                    if (baseMapper.insert(user) > 0) {
                        if (user.getRoleName() != null) {
                            String[] split = user.getRoleName().split(",");
                            for (String name : split) {
                                Long roleId = sysRoleService.selectRoleId(name);
                                userRoleMapper.insertComAdminUserRole(user.getUserId(), roleId);
                            }
                        }
                    }
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    user.setUpdateBy(operName);
                    baseMapper.updateById(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！<b>默认密码为："+ password +"</b>，共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<SysAdminUser> getCompanyUser(Long companyId) {
        return baseMapper.getCompanyUser(companyId);
    }

    @Override
    public List<SysAdminUser> selectByDeBugList() {
        return baseMapper.selectByDeBugList();
    }

    @Override
    public int resUserInfo(LoginUser loginUser) {
        try {
            SysUser users = loginUser.getUser();
            addRole(users, true);
            UserRedisUtils.resUserRedisInfo(loginUser);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public List<SysAdminUser> selectUserListByRoleKey(String key) {
        QueryWrapper<SysAdminUser> queryWrapper = new QueryWrapper<>();
        if (key != null && !key.equals("")) {
            queryWrapper.apply(
                    "user_id in " +
                            "(SELECT DISTINCT ur.user_id FROM `sys_role_menu` rm LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id " +
                            "LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE m.perms = '" + key + "')");
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> selectSubSystemFlag(Long userId) {
        List<String> list = baseMapper.selectSubSystemFlag(userId);
        return list;
    }

    /**
     * 查询企业用户列表
     * @return
     */
    @Override
    public List<SysAdminUser> selectCompanyUserList(SysAdminUserDto adminUserDto) {
        LambdaQueryWrapper<SysAdminUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(adminUserDto.getUserName()!=null ,SysAdminUser::getUserName,adminUserDto.getUserName());
        lambdaQueryWrapper.like(adminUserDto.getNickName()!=null ,SysAdminUser::getNickName,adminUserDto.getNickName());
        if (adminUserDto.getCompanyId()!=null){
            lambdaQueryWrapper.eq(SysAdminUser::getCompanyId,adminUserDto.getCompanyId());
        }else{
            lambdaQueryWrapper.ne(SysAdminUser::getCompanyId,100L);
        }
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据ids查询用户信息
     * @param ids
     * @return
     */
    @Override
    public List<SysAdminUser> selectUserByIds(List<Long> ids) {
        List<SysAdminUser> list = baseMapper.selectBatchIds(ids);
        Binder.bindRelations(list);
        return list;
    }

    /**
     * 用户角色信息补充
     *
     * @param sysUsers 用户信息
     * @param isRole   是否填充roles
     */
    private void addRole(SysUser sysUsers, boolean isRole) {
        List<SysRole> sysRoles = sysRoleService.selectRolePermissionByUserId(sysUsers.getUserId());
        if (isRole) {
            sysUsers.setRoles(sysRoles);
        }
        List<Long> ids = new ArrayList<>();
        for (SysRole sysRole : sysRoles) {
            Long roleId = sysRole.getRoleId();
            ids.add(roleId);
            // 组装roleIds
            sysUsers.setRoleIds(ids);
        }
    }

    @Override
    public List<SysAdminUser> selectUserListExcludeCurrent(SysAdminUser user) {
        // 获取当前登录用户ID
        Long currentUserId = SecurityUtils.getUserId();
        // 构建查询条件
        LambdaQueryWrapper<SysAdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(SysAdminUser::getUserId, currentUserId)
                .eq(user.getCompanyId() != null, SysAdminUser::getCompanyId, user.getCompanyId())
                .like(StringUtils.isNotEmpty(user.getUserName()), SysAdminUser::getUserName, user.getUserName())
                .like(StringUtils.isNotEmpty(user.getNickName()), SysAdminUser::getNickName, user.getNickName())
                .eq(StringUtils.isNotEmpty(user.getStatus()), SysAdminUser::getStatus, '0')
                .eq(SysAdminUser::getDelFlag, "0");  // 只查询未删除的用户
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 1. 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        
        // 2. 处理知识库相关数据
        remoteKnowledgebaseService.deleteByUserId(userId, SecurityConstants.INNER);
        
        // 3. 逻辑删除用户
        LambdaUpdateWrapper<SysAdminUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysAdminUser::getDelFlag, "2")
                    .set(SysAdminUser::getUpdateBy, SecurityUtils.getUsername())
                    .set(SysAdminUser::getUpdateTime, DateUtils.getNowDate())
                    .eq(SysAdminUser::getUserId, userId);
        
        return baseMapper.update(null, updateWrapper);
    }

}
