package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysAppUser;
import com.ruoyi.common.entity.domain.SysAppUserRole;
import com.ruoyi.common.entity.mapper.SysAppUserRoleMapper;
import com.ruoyi.system.service.ISysAppUserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysAppUserRoleServiceImpl extends ServiceImpl<SysAppUserRoleMapper, SysAppUserRole> implements ISysAppUserRoleService {

    @Override
    @Transactional
    public int insertByRole(String roleKey, List<SysAppUser> users) {
        deleteRoles(roleKey, users);

        int rows = 0;
        for (SysAppUser user : users) {
            SysAppUserRole userRole = new SysAppUserRole();
            userRole.setUserId(user.getUserId());
            userRole.setCompanyId(user.getCompanyId());
            userRole.setRoleKey(roleKey);

            rows += baseMapper.insert(userRole);
        }
        return rows;
    }

    @Override
    @Transactional
    public int editByUser(Long userId, Long companyId, List<String> roleKeys) {
        int deleteRows = deleteByUser(userId);

        if (StringUtils.isEmpty(roleKeys)) {
            return deleteRows;
        }

        int rows = 0;
        for (String roleKey : roleKeys) {
            SysAppUserRole userRole = new SysAppUserRole();
            userRole.setUserId(userId);
            userRole.setCompanyId(companyId);
            userRole.setRoleKey(roleKey);

            rows += baseMapper.insert(userRole);
        }
        return rows;
    }

    @Override
    public List<SysAppUserRole> selectByRoleKey(String roleKey, Long companyId) {
        LambdaQueryWrapper<SysAppUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAppUserRole::getRoleKey, roleKey);
        queryWrapper.eq(companyId != null, SysAppUserRole::getCompanyId, companyId);

        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysAppUserRole> selectByUser(Long userId) {
        LambdaQueryWrapper<SysAppUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAppUserRole::getUserId, userId);

        return baseMapper.selectList(queryWrapper);
    }

    private int deleteRoles(String roleKey, List<SysAppUser> users) {
        LambdaQueryWrapper<SysAppUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SysAppUserRole::getRoleKey, roleKey);
        deleteWrapper.in(SysAppUserRole::getUserId, users.stream().map(s -> s.getUserId()).collect(Collectors.toList()));

        return baseMapper.delete(deleteWrapper);
    }

    @Override
    public int deleteByUser(Long userId) {
        LambdaQueryWrapper<SysAppUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SysAppUserRole::getUserId, userId);

        return baseMapper.delete(deleteWrapper);
    }

    @Override
    public int deleteByCompany(Long companyId) {
        LambdaQueryWrapper<SysAppUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SysAppUserRole::getCompanyId, companyId);

        return baseMapper.delete(deleteWrapper);
    }
}
