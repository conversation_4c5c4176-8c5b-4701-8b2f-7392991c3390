package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.entity.domain.SysAppeal;
import com.ruoyi.system.mapper.SysAppealMapper;
import com.ruoyi.system.service.ISysAppealService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 申诉信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@Service
public class SysAppealServiceImpl extends ServiceImpl<SysAppealMapper, SysAppeal> implements ISysAppealService
{
    @Override
    public SysAppeal selectSysAppealById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<SysAppeal> selectSysAppealByNo(String no) {
        LambdaQueryWrapper<SysAppeal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(no != null, SysAppeal::getComplaintNo, no);
        List<SysAppeal> list = baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public List<SysAppeal> selectSysAppealList(SysAppeal sysAppeal) {
        LambdaQueryWrapper<SysAppeal> queryWrapper = new LambdaQueryWrapper<>(sysAppeal);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据投诉单号参数最后一次申诉信息
     *
     * @param complaintNo 申诉信息
     * @return 申诉信息集合
     */
    public SysAppeal selectLastSysAppeal(String complaintNo){
        LambdaQueryWrapper<SysAppeal> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(complaintNo != null, SysAppeal::getComplaintNo, complaintNo);
        lambdaQueryWrapper.orderByDesc(SysAppeal::getId);
        List<SysAppeal> list = baseMapper.selectList(lambdaQueryWrapper);
        return (list != null && list.size()>0) ? list.get(0) : null;
    }

    @Override
    public int insertSysAppeal(SysAppeal sysAppeal) {
        return baseMapper.insert(sysAppeal);
    }

    @Override
    public int updateSysAppeal(SysAppeal sysAppeal) {
        return baseMapper.updateById(sysAppeal);
    }

    @Override
    public int deleteSysAppealByIds(Long[] ids) {
        List<Long> list = Arrays.asList(ids);
        return baseMapper.deleteBatchIds(list);
    }

    @Override
    public int deleteSysAppealById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public SysAppeal selectUnique(String complaintNo, String uniqueValue) {
        LambdaQueryWrapper<SysAppeal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(uniqueValue!=null,SysAppeal::getUniCode,uniqueValue);
        queryWrapper.eq(complaintNo!=null,SysAppeal::getComplaintNo,complaintNo);
        return baseMapper.selectOne(queryWrapper);
    }
}
