package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysCompanyOrder;
import com.ruoyi.system.domain.SysCompanyOrderEvent;
import com.ruoyi.system.domain.SysCompanyOrderLog;
import com.ruoyi.system.mapper.SysCompanyOrderLogMapper;
import com.ruoyi.system.service.ISysCompanyOrderLogService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 企业订单操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
@Service
public class SysCompanyOrderLogServiceImpl extends ServiceImpl<SysCompanyOrderLogMapper, SysCompanyOrderLog> implements ISysCompanyOrderLogService {

    /**
     * 查询企业订单操作记录
     *
     * @param id 企业订单操作记录主键
     * @return 企业订单操作记录
     */
    @Override
    public SysCompanyOrderLog selectById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询企业订单操作记录列表
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 企业订单操作记录
     */
    @Override
    public List<SysCompanyOrderLog> selectList(SysCompanyOrderLog sysCompanyOrderLog) {
        QueryWrapper<SysCompanyOrderLog> queryWrapper = QueryBuilder.toQueryWrapper(sysCompanyOrderLog);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 新增企业订单操作记录
     *
     * @param sysCompanyOrderEvent 企业订单操作事件
     * @return 结果
     */
    @Override
    public int insert(SysCompanyOrder sysCompanyOrder, SysCompanyOrderEvent sysCompanyOrderEvent) {
        return insert(sysCompanyOrder, sysCompanyOrderEvent, null);
    }

    /**
     * 新增企业订单操作记录
     *
     * @param sysCompanyOrderEvent 企业订单操作事件
     * @param sysCompanyOrderLog   企业订单操作记录
     * @return 结果
     */
    @Override
    public int insert(SysCompanyOrder sysCompanyOrder, SysCompanyOrderEvent sysCompanyOrderEvent, SysCompanyOrderLog sysCompanyOrderLog) {
        try {
            if (sysCompanyOrderLog == null) {
                sysCompanyOrderLog = new SysCompanyOrderLog();
            }
            if (sysCompanyOrderEvent.eventCode.equals(SysCompanyOrderEvent.Assigned.eventCode)){
                sysCompanyOrderLog.setReceiptImg(sysCompanyOrder.getDebugUserId()+"");
            }
            sysCompanyOrderLog.setReceiptImg(sysCompanyOrderLog.getReceiptImg());
            sysCompanyOrderLog.setOrderId(sysCompanyOrder.getId());
            sysCompanyOrderLog.setEventNo(sysCompanyOrderEvent.eventCode);
            sysCompanyOrderLog.setEventReason(sysCompanyOrderEvent.eventLabel);
            sysCompanyOrderLog.setCreateTime(DateUtils.getNowDate());
            sysCompanyOrderLog.setRemark(sysCompanyOrderLog.getRemark());

            if (!SysCompanyOrderEvent.CreatOrder.eventCode.equals(sysCompanyOrderEvent.eventCode)) {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                sysCompanyOrderLog.setCreateUserId(loginUser.getUserId());
                sysCompanyOrderLog.setCreateBy(loginUser.getUsername());
            }
            return baseMapper.insert(sysCompanyOrderLog);
        } catch (Exception e) {
            throw new ServiceException("保存订单操作记录异常");
        }
    }

    /**
     * 修改企业订单操作记录
     *
     * @param sysCompanyOrderLog 企业订单操作记录
     * @return 结果
     */
    @Override
    public int update(SysCompanyOrderLog sysCompanyOrderLog) {
        return baseMapper.updateById(sysCompanyOrderLog);
    }

    /**
     * 批量删除企业订单操作记录
     *
     * @param ids 需要删除的企业订单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除企业订单操作记录信息
     *
     * @param id 企业订单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public String getReceiptImg(Long id, SysCompanyOrderEvent reviewed) {
        LambdaQueryWrapper<SysCompanyOrderLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCompanyOrderLog::getOrderId, id);
        queryWrapper.eq(SysCompanyOrderLog::getEventNo, reviewed.eventCode);
        queryWrapper.orderByDesc(SysCompanyOrderLog::getCreateTime);
        List<SysCompanyOrderLog> sysCompanyOrderLog = baseMapper.selectList(queryWrapper);
        if (StringUtils.isEmpty(sysCompanyOrderLog) || sysCompanyOrderLog.size() == 0) {
            return null;
        }
        return sysCompanyOrderLog.get(0).getReceiptImg();
    }
}
