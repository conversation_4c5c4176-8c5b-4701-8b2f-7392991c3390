package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.SysDictData;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.utils.DictUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysCompanyResConfigMapper;
import com.ruoyi.common.entity.domain.system.SysCompanyResConfig;
import com.ruoyi.system.service.ISysCompanyResConfigService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-18
 */
@Slf4j
@Service
public class SysCompanyResConfigServiceImpl implements ISysCompanyResConfigService 
{
    @Autowired
    private SysCompanyResConfigMapper sysCompanyResConfigMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SysCompanyResConfig selectSysCompanyResConfigByCompanyId(Long companyId)
    {
        return sysCompanyResConfigMapper.selectSysCompanyResConfigByCompanyId(companyId);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param companyResConfig 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SysCompanyResConfig> selectSysCompanyResConfigList(SysCompanyResConfig companyResConfig)
    {
        Long companyId = companyResConfig.getCompanyId();
        List<SysCompanyResConfig> resConfigs = sysCompanyResConfigMapper.selectSysCompanyResConfigList(companyResConfig);
        List<SysDictData> dictData = DictUtils.getDictCache(SysDictDataEnum.COMPANY_RES_CONFIG.getName());
        if (dictData == null || dictData.size() == 0) {
            throw new RuntimeException("操作失败，资源配置列表未能获取到相关字典配置");
        }
        if (resConfigs == null){
            log.error("resConfigs is null");
            resConfigs = new ArrayList<>();

        }
        if (resConfigs.size() != dictData.size()) {
            Map<String, Long> resMap = new HashMap<>();
            if (resConfigs.size() > 0) {

                log.error("resConfigs size :"+ resConfigs.size());

                Map<String, Long> map = resConfigs.stream().collect(Collectors.
                        toMap(SysCompanyResConfig :: getResourceType, SysCompanyResConfig :: getResourceLimit));
                resMap.putAll(map);
            }
            for (SysDictData sysDictData : dictData) {
                if (resMap.containsKey(sysDictData.getDictLabel())) {
                    continue;
                }
                SysCompanyResConfig sysCompanyResConfig = new SysCompanyResConfig();
                sysCompanyResConfig.setCompanyId(companyId);
                sysCompanyResConfig.setResourceType(sysDictData.getDictLabel());
                sysCompanyResConfig.setResourceLimit(Long.valueOf(sysDictData.getDictValue()));
                sysCompanyResConfig.setStatus("0");
                sysCompanyResConfig.setDelFlag("0");
                resConfigs.add(sysCompanyResConfig);
            }
        }
        return resConfigs;
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig)
    {
        return sysCompanyResConfigMapper.insertSysCompanyResConfig(sysCompanyResConfig);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysCompanyResConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSysCompanyResConfig(SysCompanyResConfig sysCompanyResConfig)
    {
        sysCompanyResConfig.setUpdateTime(DateUtils.getNowDate());
        return sysCompanyResConfigMapper.updateSysCompanyResConfig(sysCompanyResConfig);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param companyIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysCompanyResConfigByCompanyIds(Long[] companyIds)
    {
        return sysCompanyResConfigMapper.deleteSysCompanyResConfigByCompanyIds(companyIds);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param companyId 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysCompanyResConfigByCompanyId(Long companyId)
    {
        return sysCompanyResConfigMapper.deleteSysCompanyResConfigByCompanyId(companyId);
    }

    /**
     * 根据公司id查询公司资源配置数据
     * @param companyId
     * @param type
     * @return
     */
    @Override
    public SysCompanyResConfig selectCompResConfigDataByType(Long companyId, String type) {
        return sysCompanyResConfigMapper.selectCompResConfigDataByType(companyId,type);
    }
}
