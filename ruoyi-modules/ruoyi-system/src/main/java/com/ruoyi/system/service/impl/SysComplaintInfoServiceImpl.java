package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.SysComplaintInfo;
import com.ruoyi.system.mapper.SysComplaintInfoMapper;
import com.ruoyi.system.service.ISysComplaintInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 投诉信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-16
 */
@Service
public class SysComplaintInfoServiceImpl extends ServiceImpl<SysComplaintInfoMapper, SysComplaintInfo> implements ISysComplaintInfoService
{

    @Override
    public SysComplaintInfo selectSysComplaintInfoById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public SysComplaintInfo selectSysComplaintInfoByNo(String no){
        LambdaQueryWrapper<SysComplaintInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(no != null, SysComplaintInfo::getComplaintNo, no);
        List<SysComplaintInfo> list = baseMapper.selectList(queryWrapper);
        return (list != null && list.size()>0) ? list.get(0) : null;
    }

    @Override
    public List<SysComplaintInfo> selectSysComplaintInfoList(SysComplaintInfo sysComplaintInfo) {
        return null;
    }

    @Override
    public int insertSysComplaintInfo(SysComplaintInfo sysComplaintInfo) {
        sysComplaintInfo.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysComplaintInfo);
    }

    @Override
    public int updateSysComplaintInfo(SysComplaintInfo sysComplaintInfo) {
        LambdaUpdateWrapper<SysComplaintInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(sysComplaintInfo.getComplaintResult() != null, SysComplaintInfo::getComplaintResult, sysComplaintInfo.getComplaintResult());
        updateWrapper.set(sysComplaintInfo.getAllowAppealFlag() != null, SysComplaintInfo::getAllowAppealFlag, sysComplaintInfo.getAllowAppealFlag());
        updateWrapper.set(sysComplaintInfo.getPunishType() != null, SysComplaintInfo::getPunishType, sysComplaintInfo.getPunishType());
        updateWrapper.set(sysComplaintInfo.getPunishDuration() != null, SysComplaintInfo::getPunishDuration, sysComplaintInfo.getPunishDuration());
        updateWrapper.set(sysComplaintInfo.getUpdateBy() != null, SysComplaintInfo::getUpdateBy, sysComplaintInfo.getUpdateBy());
        updateWrapper.set(sysComplaintInfo.getUpdateTime() != null, SysComplaintInfo::getUpdateTime, sysComplaintInfo.getUpdateTime());
        updateWrapper.set(sysComplaintInfo.getRemark() != null, SysComplaintInfo::getRemark, sysComplaintInfo.getRemark());
        updateWrapper.eq(sysComplaintInfo.getComplaintNo() != null, SysComplaintInfo::getComplaintNo, sysComplaintInfo.getComplaintNo());
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteSysComplaintInfoByIds(Long[] ids) {
        return 0;
    }

    @Override
    public int deleteSysComplaintInfoById(Long id) {
        return 0;
    }
}
