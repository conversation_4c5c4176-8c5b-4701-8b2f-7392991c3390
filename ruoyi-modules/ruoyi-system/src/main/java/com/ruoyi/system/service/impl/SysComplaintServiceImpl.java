package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.Binder;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.SysComplaint;
import com.ruoyi.system.mapper.SysComplaintMapper;
import com.ruoyi.system.service.ISysComplaintService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 投诉信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
@Service
public class SysComplaintServiceImpl extends ServiceImpl<SysComplaintMapper, SysComplaint> implements ISysComplaintService {
    @Override
    public SysComplaint selectSysComplaintById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<SysComplaint> selectSysComplaintList(SysComplaint sysComplaint) {
        LambdaQueryWrapper<SysComplaint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(sysComplaint.getComplaintNo() != null, SysComplaint::getComplaintNo, sysComplaint.getComplaintNo());
        queryWrapper.eq(sysComplaint.getComplaintType() != null, SysComplaint::getComplaintType, sysComplaint.getComplaintType());
        if (sysComplaint.getNickName()!=null){
            queryWrapper.and((wrapper -> wrapper
                    .like(SysComplaint::getNickName, sysComplaint.getNickName())
                    .or().like(SysComplaint::getUserName, sysComplaint.getNickName())
            ));
        }
        if (sysComplaint.getTargetNickName()!=null){
            queryWrapper.and((wrapper -> wrapper
                    .like(SysComplaint::getTargetNickName, sysComplaint.getTargetNickName())
                    .or().like(SysComplaint::getTargetObjName, sysComplaint.getTargetNickName())
            ));
        }
        queryWrapper.eq(sysComplaint.getComplaintStatus() != null, SysComplaint::getComplaintStatus, sysComplaint.getComplaintStatus());
        queryWrapper.eq(sysComplaint.getComplaintResult() != null, SysComplaint::getComplaintResult, sysComplaint.getComplaintResult());
        queryWrapper.gt(sysComplaint.getStartCreatTime() != null, SysComplaint::getCreateTime, sysComplaint.getStartCreatTime());
        queryWrapper.lt(sysComplaint.getEndCreatTime() != null, SysComplaint::getCreateTime, sysComplaint.getEndCreatTime());
        queryWrapper.orderByAsc(SysComplaint::getComplaintStatus);
        queryWrapper.orderByDesc(SysComplaint::getUpdateTime);
        queryWrapper.orderByAsc(SysComplaint::getCreateTime);
        List<SysComplaint> list = baseMapper.selectList(queryWrapper);
        Binder.bindRelations(list);
        return list;
    }

    @Override
    public int insertSysComplaint(SysComplaint sysComplaint) {
        sysComplaint.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysComplaint);
    }

    @Override
    public int updateSysComplaint(SysComplaint sysComplaint) {
        sysComplaint.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(sysComplaint);
    }

    @Override
    public int deleteSysComplaintByIds(Long[] ids) {
        List<Long> list = Arrays.asList(ids);
        return baseMapper.deleteBatchIds(list);
    }

    @Override
    public int deleteSysComplaintById(Long id) {
        return baseMapper.deleteSysComplaintById(id);
    }

    @Override
    public SysComplaint selectComplaint(Long userId, Long targetObjId) {
        LambdaUpdateWrapper<SysComplaint> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SysComplaint::getUserId,userId);
        wrapper.ne(SysComplaint::getComplaintStatus,2);
        wrapper.eq(SysComplaint::getTargetObjId,targetObjId);
        wrapper.gt(SysComplaint::getCreateTime,DateUtils.advanceDate(0));
        wrapper.lt(SysComplaint::getCreateTime,DateUtils.advanceDate(1));
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public SysComplaint selectByComplaintNo(String complaintNo) {
        LambdaQueryWrapper<SysComplaint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysComplaint::getComplaintNo,complaintNo);
        return baseMapper.selectOne(queryWrapper);
    }
}
