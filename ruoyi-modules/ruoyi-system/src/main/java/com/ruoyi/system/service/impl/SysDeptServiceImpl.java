package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.annotation.DataScope;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.mapper.SysDeptMapper;
import com.ruoyi.common.entity.vo.TreeSelect;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysRoleService;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService {
    private static final Logger log = LoggerFactory.getLogger(SysDeptServiceImpl.class);

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private RedisService redisService;

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        List<SysDept> sysDepts = deptMapper.selectDeptList(dept);
        return sysDepts;
    }

    /**
     * 查询部门管理数据
     *
     * @param parentId
     * @return
     */
    @Override
    public List<SysDept> selectOneLevelDepts(Long parentId) {
        QueryWrapper<SysDept> sysDeptQueryWrapper = new QueryWrapper<>();
        sysDeptQueryWrapper.lambda().eq(SysDept :: getParentId, parentId);
        return deptMapper.selectList(sysDeptQueryWrapper);
    }

    @Override
    public List<SysDept> selectDeptByPhone(String phone) {
        return deptMapper.selectDeptByPhone(phone);
    }

    /**
     * 查询全部公司数据
     *
     * @param includeDisabled 是否包含停用数据
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectAllCompanyList(boolean includeDisabled) {
        return deptMapper.selectAllCompanyList(includeDisabled);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = new ArrayList<Long>();
        for (SysDept dept : depts) {
            tempList.add(dept.getDeptId());
        }
        for (Iterator<SysDept> iterator = depts.iterator(); iterator.hasNext(); ) {
            SysDept dept = (SysDept) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect :: new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Integer> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleService.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
//        int result = appUserService.selectUserCountByDept(deptId);
//        return result > 0;
        return false;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public String checkDeptNameUnique(SysDept dept) {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts)) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDept(SysDept dept) {
        SysDept info = baseMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException("部门停用，不允许新增");
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        int deptResult = baseMapper.insertDept(dept);
        if (deptResult > 0) {
            // 更新部门信息
            editDeptRedis(dept);
        }
        return deptResult;
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDept(SysDept dept) {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            //重新组装祖级列表
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);
        if (result > 0) {
            // 更新企业关联平台
            editDeptRedis(dept);
            // 更新企业详情
            if (dept.getDetail() != null && !dept.getDetail().isEmpty()) {
                dept.getDetail().setCompanyId(dept.getDeptId());
                //公司具体信息（位置、所属行业...）
                deptMapper.insertOrUpdateCompanyDetail(dept.getDetail());
            } else {
                deptMapper.deleteCompanyDetail(dept.getDeptId());
            }
        }
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        //查询指定公司下的部门
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children) {
            //替换原来的祖级
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId) {
        return deptMapper.deleteDeptById(deptId);
    }

    /**
     * 根据ID查找相应的TreeSelect对象
     *
     * @param treeSelectList TreeSelect对象列表
     * @param id             TreeSelect ID
     * @return
     */
    @Override
    public TreeSelect findTreeSelectByID(List<TreeSelect> treeSelectList, Long id) {
        for (TreeSelect treeSelect : treeSelectList) {
            if (treeSelect.getId().equals(id)) {
                return treeSelect;
            }
            TreeSelect target = findTreeSelectByID(treeSelect.getChildren(), id);
            if (target != null) {
                return target;
            }
        }
        return null;
    }

    @Override
    public String importData(List<SysDept> deptList, boolean isUpdateSupport, String operName) {
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int failureNum = 0;
        for (SysDept dept : deptList) {
            try {
                SysDept deptInDb = deptMapper.selectDeptByName(dept.getDeptName());
                if (deptInDb == null) {
                    dept.setParentId(100L);
                    dept.setAncestors("0,100");
                    dept.setCreateBy(operName);
                    dept.setCompanySerial(RandomStringUtils.random(6, true, true).toUpperCase(Locale.ROOT));
                    // 新增
                    this.insertDept(dept);

                    successMsg.append("<br/>" + ++successNum + "、企业 " + dept.getDeptName() + " 导入成功");
                } else if (isUpdateSupport) {
                    dept.setDeptId(deptInDb.getDeptId());
                    dept.setUpdateBy(operName);
                    // 更新
                    this.updateDept(dept);

                    successMsg.append("<br/>" + ++successNum + "、企业 " + dept.getDeptName() + " 更新成功");
                } else {
                    failureMsg.append("<br/>" + ++failureNum + "、企业 " + dept.getDeptName() + " 已存在");
                }


            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、企业 " + dept.getDeptName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return getChildList(list, t).size() > 0;
    }


    @Override
    public List<SysDept> selectDeptByList(SysDept dept) {
        return deptMapper.selectDeptByList(dept);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectSumDept(Long deptId) {
        return deptMapper.selectSumDept(deptId);
    }

    /**
     * 查询部门列表排序集合
     *
     * @param dept
     * @return
     */
    @Override
    public List<SysDept> selectDeptListSort(SysDept dept) {
        return deptMapper.selectDeptListSort(dept);
    }

    @Override
    public List<SysDept> selectDeptListByNotId(SysDept dept) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(dept.getDeptId()!=null,SysDept::getDeptId, dept.getDeptId());
        wrapper.like(dept.getDeptName()!=null,SysDept::getDeptName, dept.getDeptName());
        wrapper.eq(dept.getDelFlag()!=null,SysDept::getDelFlag, dept.getDelFlag());
        wrapper.orderByAsc(SysDept::getOrderNum).orderByDesc(SysDept::getCreateTime);
        return deptMapper.selectList(wrapper);
    }


    /**
     * 更新企业平台关联数据
     */
    public void editDeptRedis(SysDept dept) {
        String key = Constants.SYS_DEPT + dept.getDeptId();
        if (redisService.hasKey(key)) {
            redisService.deleteObject(key);
        }
        redisService.setCacheObject(key, dept.getDeptName());
    }

    /**
     * 处理缓存问题
     * 模块启动后5秒后执行重新写入一遍,确保每次数据都是最新的
     */
    @Scheduled(initialDelay = 5000, fixedRate = Long.MAX_VALUE)
    protected void rewrite() {
        String key = Constants.SYS_DEPT;
        Collection<String> keys = redisService.keys(key + "*");
        if (keys != null && keys.size() > 0) {
            redisService.deleteObject(keys);
        }
        List<SysDept> deptList = deptMapper.selectCompanyList(new SysDept());
        for (SysDept dept : deptList) {
            redisService.setCacheObject(key + dept.getDeptId(), dept.getDeptName());
        }
    }

}
