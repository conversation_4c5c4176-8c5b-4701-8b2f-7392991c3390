package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.domain.SysDivision;
import com.ruoyi.system.mapper.SysDivisionMapper;
import com.ruoyi.system.service.ISysDivisionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 区域信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Service
public class SysDivisionServiceImpl extends ServiceImpl<SysDivisionMapper, SysDivision> implements ISysDivisionService {
    /**
     * 查询区域信息
     *
     * @param id 区域信息主键
     * @return 区域信息
     */
    @Override
    public SysDivision selectById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询区域信息列表
     *
     * @param sysDivision 区域信息
     * @return 区域信息
     */
    @Override
    public List<SysDivision> selectList(SysDivision sysDivision) {
        QueryWrapper<SysDivision> queryWrapper = QueryBuilder.toQueryWrapper(sysDivision);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 新增区域信息
     *
     * @param sysDivision 区域信息
     * @return 结果
     */
    @Override
    public int insert(SysDivision sysDivision) {
        return baseMapper.insert(sysDivision);
    }

    /**
     * 修改区域信息
     *
     * @param sysDivision 区域信息
     * @return 结果
     */
    @Override
    public int update(SysDivision sysDivision) {
        return baseMapper.updateById(sysDivision);
    }

    /**
     * 删除区域信息信息
     *
     * @param id 区域信息主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        LambdaQueryWrapper<SysDivision> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SysDivision::getId, id);
        return baseMapper.delete(deleteWrapper);
    }

    @Override
    public List<SysDivision> selectByParent(Long parentId) {
        LambdaQueryWrapper<SysDivision> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDivision::getParentId, parentId == null ? 0L : parentId);
        queryWrapper.eq(SysDivision::getEnable, 0);
        queryWrapper.orderByAsc(SysDivision::getName);

        List<SysDivision> list = baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public String selectDivision(String divisionNames) {
        String[] split = divisionNames.split("/");
        StringBuilder divisionIds = new StringBuilder();
        Long parentId = 0L;
        for (int i = 0; i < split.length; i++) {
            Long id = getDivision(split[i], parentId);
            if (id == null) {
                return null;
            }
            parentId = id;
            divisionIds.append(id);
            divisionIds.append(",");
        }
        return divisionIds.substring(0, divisionIds.length() - 1);
    }

    @Override
    public SysDivision selectDivisionName(String name, Long parentId) {
        LambdaQueryWrapper<SysDivision> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(parentId != null, SysDivision::getParentId, parentId);
        queryWrapper.eq(name != null, SysDivision::getName, name);
        return baseMapper.selectOne(queryWrapper);
    }

    private Long getDivision(String name, Long parentId) {
        LambdaQueryWrapper<SysDivision> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDivision::getName, name);
        queryWrapper.eq(SysDivision::getParentId, parentId);
        queryWrapper.eq(SysDivision::getEnable, 0);
        List<SysDivision> sysDivisions = baseMapper.selectList(queryWrapper);
        if (sysDivisions == null || sysDivisions.size() == 0) {
            return null;
        }
        return sysDivisions.get(0).getId();
    }

}
