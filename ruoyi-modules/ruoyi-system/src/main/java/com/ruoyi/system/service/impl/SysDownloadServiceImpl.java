package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.system.domain.SysDownload;
import com.ruoyi.system.mapper.SysDownloadMapper;
import com.ruoyi.system.service.ISysDownloadService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 安装包下载Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-26
 */
@Service
public class SysDownloadServiceImpl extends ServiceImpl<SysDownloadMapper, SysDownload> implements ISysDownloadService {

    /**
     * 查询安装包下载
     *
     * @param id 安装包下载主键
     * @return 安装包下载
     */
    @Override
    public SysDownload selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public SysDownload selectByAppType(String appType) {
        LambdaQueryWrapper<SysDownload> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDownload::getAppType, appType);
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 查询安装包下载列表
     *
     * @param sysDownload 安装包下载
     * @return 安装包下载
     */
    @Override
    public List<SysDownload> selectList(SysDownload sysDownload) {
        QueryWrapper<SysDownload> queryWrapper = QueryBuilder.toQueryWrapper(sysDownload);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 新增安装包下载
     *
     * @param sysDownload 安装包下载
     * @return 结果
     */
    @Override
    public int insert(SysDownload sysDownload) {
        return baseMapper.insert(sysDownload);
    }

    /**
     * 修改安装包下载
     *
     * @param sysDownload 安装包下载
     * @return 结果
     */
    @Override
    public int update(SysDownload sysDownload) {
        return baseMapper.updateById(sysDownload);
    }

    /**
     * 批量删除安装包下载
     *
     * @param ids 需要删除的安装包下载主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除安装包下载信息
     *
     * @param id 安装包下载主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }
}
