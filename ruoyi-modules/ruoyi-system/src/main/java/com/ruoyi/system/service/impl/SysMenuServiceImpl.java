package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysMenu;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.mapper.SysMenuMapper;
import com.ruoyi.common.entity.vo.TreeSelect;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.vo.MetaVo;
import com.ruoyi.system.domain.vo.RouterVo;
import com.ruoyi.system.mapper.SysRoleMenuMapper;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl implements ISysMenuService {

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private RedisService redisService;


    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId) {
        List<SysMenu> menuList;
        // 管理员显示所有菜单信息
//        if (SysUser.isAdmin(userId)) {
        menuList = menuMapper.selectMenuList(menu);
//        } else {
//            menu.getParams().put("userId", userId);
//            menuList = menuMapper.selectMenuListByUserId(menu);
//        }
        return menuList;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param user 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(SysUser user, String systemFlag) {
        List<String> perms = menuMapper.selectMenuPermsByUserId(user.getUserId(), systemFlag);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户名称
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Long userId, Long parentId, String subSystem) {
        List<SysMenu> menus;
        // 超级管理员
        if (SecurityUtils.isAdmin(userId)) {
            menus = filterMenu(filterPlatform(menuMapper.selectMenuTreeAll()), parentId);
        } else {
            List<Long> ids = SecurityUtils.getRoleIds();
            String roleIds = StringUtils.join(ids, ",");
            List<SysMenu> menuList = menuMapper.selectMenuListByRoleIds(roleIds, subSystem);
            menus = filterMenu(filterPlatform(menuList), parentId);
        }
        return getChildPerms(menus, parentId.intValue());
    }


    /**
     * 只过滤到二级菜单
     * 组装路由地址
     */
    public List<SysMenu> filterMenu(List<SysMenu> menus, Long parentId) {
        List<SysMenu> menuList = new ArrayList<>();
        for (SysMenu menu : menus) {
            if (parentId.equals(menu.getParentId())) {
                // 一级
                menuList.add(menu);
            } else {
                // 二级
                SysMenu sysMenu = menuMapper.selectMenuById(menu.getParentId());
                // 确认二级身份
                if (sysMenu != null && parentId.equals(sysMenu.getParentId())) {
                    //查看对应二级下是否存在三级
                    List<SysMenu> sysMenus;
                    // 参数“1”无效
                    sysMenus = menuMapper.selectParenIds(menu.getMenuId(), "1");
                    if (sysMenus.size() > 0) {
                        String url = buildDefaultJump(sysMenus, menu.getMenuId());
                        menu.setPath(menu.getPath() + "/" + url);
                        menu.setRedirect(menu.getPath() + "/" + url);
                    } else {
                        menu.setRedirect(menu.getPath());
                    }
                    menuList.add(menu);
                }
            }
        }
        return menuList;
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Integer> selectMenuListByRoleId(Long roleId) {
        SysRole role = roleService.selectRoleById(roleId);
        return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenu> menus, Long parentId) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            // getRouteRedirect：组装二三级路由依据
            router.setRedirect(getRouteRedirect(menu, parentId));
            // getRouteWebPath：组装一二级路由依据
            router.setWebPath(getRouteWebPath(menu, parentId));
            router.setName(getRouteName(menu, parentId));
            router.setPath(getRouterPath(menu, parentId));
            router.setComponent(getComponent(menu, parentId));
            router.setQuery(menu.getQuery());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
            List<SysMenu> cMenus = menu.getChildren();
            //当子菜单不为空并且长度大于0 并且 是目录
            if (!cMenus.isEmpty() && cMenus.size() > 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus, parentId));
                //是否为菜单内部跳转
            } else if (isMenuFrame(menu, parentId)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                children.setQuery(menu.getQuery());
                childrenList.add(children);
                router.setChildren(childrenList);
                //是否是一级  并且是 目录
            } else if (parentId.equals(menu.getParentId()) && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/inner");
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                String routerPath = StringUtils.replaceEach(menu.getPath(), new String[]{Constants.HTTP, Constants.HTTPS}, new String[]{"", ""});
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
                //给三级菜单的active赋值
            } else if (!parentId.equals(menu.getParentId()) && isTertiaryMenu(menu, parentId)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath(), getActive(menu, parentId)));
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        List<SysMenu> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (SysMenu dept : menus) {
            tempList.add(dept.getMenuId());
        }
        for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext(); ) {
            SysMenu menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
        List<SysMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId) {
        return menuMapper.selectMenuById(menuId);
    }

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId) {
        int result = menuMapper.hasChildByMenuId(menuId);
        return result > 0;
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(Long menuId) {
        int result = roleMenuMapper.checkMenuExistRole(menuId);
        return result > 0;
    }

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int insertMenu(SysMenu menu) {
        return menuMapper.insertMenu(menu);
    }

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int updateMenu(SysMenu menu) {
        return menuMapper.updateMenu(menu);
    }

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public int deleteMenuById(Long menuId) {
        return menuMapper.deleteMenuById(menuId);
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public String checkMenuNameUnique(SysMenu menu) {
        Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
        SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId(), menuId);
        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu, Long parentId) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu, parentId)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取Redirect
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteRedirect(SysMenu menu, Long parentId) {
        String routeRedrect = menu.getRedirect();
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu, parentId)) {
            routeRedrect = StringUtils.EMPTY;
        }
        return routeRedrect;
    }


    /**
     * 重新拼接active
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getActive(SysMenu menu, Long parentId) {
        if (menu != null) {
            //三级的父亲：二级
            SysMenu sysMenu = menuMapper.selectMenuById(menu.getParentId());
            if (sysMenu != null) {
                //二级的父亲：一级
                SysMenu SuperSysMenu = menuMapper.selectMenuById(sysMenu.getParentId());
                if (SuperSysMenu != null && parentId.equals(SuperSysMenu.getParentId())) {
                    //查是否存在三级
                    List<SysMenu> sysMenus = menuMapper.selectParenIds(sysMenu.getMenuId(), "1");
                    // 待优化，需整理逻辑
                    List<SysMenu> list = menuMapper.selectMenusByUserId(sysMenu.getMenuId(), SecurityUtils.getUser().getRoleId(), "1");
                    List<SysMenu> menus = list.stream().filter(s -> "0".equals(s.getIsJump())).collect(Collectors.toList());
                    if (sysMenus.size() > 0) {
                        String url;
                        if ((menus == null || menus.size() == 0) && (list != null && list.size() > 0)) {
                            url = list.get(0).getPath();
                        } else {
                            url = sysMenus.get(0).getPath();
                            for (SysMenu menuOne : sysMenus) {
                                if ("0".equals(menuOne.getIsJump())) {
                                    url = menuOne.getPath();
                                }
                            }
                        }

                        return "/" + SuperSysMenu.getPath() + "/" + sysMenu.getPath() + "/" + url;
                    }
                }
            }
        }
        return null;
    }


    /**
     * 获取WebPath
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteWebPath(SysMenu menu, Long parentId) {
        String routeRedrect = menu.getWebPath();
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu, parentId)) {
            routeRedrect = StringUtils.EMPTY;
        }
        return routeRedrect;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu, Long parentId) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (!parentId.equals(menu.getParentId()) && isInnerLink(menu)) {
            routerPath = StringUtils.replaceEach(routerPath, new String[]{Constants.HTTP, Constants.HTTPS}, new String[]{"", ""});
        }
        // 非外链并且是一级目录（类型为目录）
        if (parentId.equals(menu.getParentId()) && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu, parentId)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu, Long parentId) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu, parentId)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && !parentId.equals(menu.getParentId()) && isInnerLink(menu)) {
            component = UserConstants.INNER_LINK;
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu, parentId)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenu menu, Long parentId) {
        return parentId.equals(menu.getParentId()) && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(SysMenu menu) {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenu menu, Long parentId) {
        return !parentId.equals(menu.getParentId()) && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId) {
        List<SysMenu> returnList = new ArrayList<>();
        if (list == null || list.size() == 0) {
            return returnList;
        }
        for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext(); ) {
            SysMenu t = iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId().intValue() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param t
     */
    private void recursionFn(List<SysMenu> list, SysMenu t) {
        // 得到子节点列表
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
        List<SysMenu> tlist = new ArrayList<>();
        Iterator<SysMenu> it = list.iterator();
        while (it.hasNext()) {
            SysMenu n = it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t) {
        return getChildList(list, t).size() > 0;
    }


    /**
     * 获取同父级目录
     *
     * @param parentId
     */
    @Override
    public List<SysMenu> selectParenId(Long parentId) {
        return menuMapper.selectParenId(parentId);
    }

    @Override
    public List<Long> getByMenuId() {
        return menuMapper.getByMenuId();
    }

    @Override
    public List<SysMenu> selectGetUserMenu(String roleIds, Long parentId, String subSystem) {
        List<SysMenu> sysMenus = menuMapper.selectMenuListByRoleIds(roleIds, subSystem);
        List<SysMenu> menus = addWebPath(sysMenus, parentId);
        return getChildPerms(menus, parentId.intValue());
    }

    @Override
    public List<SysMenu> selectMenuTreeAll(Long parentId) {
        List<SysMenu> sysMenus = menuMapper.selectMenuTreeAll();
        List<SysMenu> menus = addWebPath(filterPlatform(sysMenus), parentId);
        return getChildPerms(menus, parentId.intValue());
    }

    @Override
    public SysMenu selectByPerms(String subSystem) {
        return menuMapper.selectByPerms(subSystem);
    }

    //平台级过滤
    public List<SysMenu> filterPlatform(List<SysMenu> sysMenus) {
        List<SysMenu> sysMenuList = new ArrayList<>();
        for (SysMenu sysMenu : sysMenus) {
            // 只添加目录和菜单至列表
            if (sysMenu.getMenuLevel() != null) {
                if ("0".equals(sysMenu.getMenuLevel()) || "1".equals(sysMenu.getMenuLevel())) {
                    sysMenuList.add(sysMenu);
                }
            }
        }
        return sysMenuList;
    }

    /**
     * 给webpath赋值
     */
    public List<SysMenu> addWebPath(List<SysMenu> menus, Long parentId) {
        List<SysMenu> menuList = new ArrayList<>();
        for (SysMenu menu : menus) {
            if (parentId.equals(menu.getParentId())) {
                menuList.add(menu);
            } else {
                SysMenu sysMenu = menuMapper.selectMenuById(menu.getParentId());
                //如果getparentId==0说明是二级
                if (sysMenu != null && parentId.equals(sysMenu.getParentId())) {
                    SysMenu menuWebPath = isPath(menu);
                    menuList.add(menuWebPath);
                } else {
                    menuList.add(menu);
                }
            }
        }
        return menuList;
    }

    /**
     * 判断是否为三级菜单
     *
     * @param menu
     */
    public boolean isTertiaryMenu(SysMenu menu, Long parentId) {
        SysMenu sysMenu = menuMapper.selectMenuById(menu.getParentId());
        //一级、二级直接返回
        if (parentId.equals(menu.getParentId()) || parentId.equals(sysMenu.getParentId())) {
            return false;
        }
        //查看是否存在三级
        List<SysMenu> sysMenus = menuMapper.selectParenIds(menu.getParentId(), "1");
        return sysMenus.size() > 0;
    }


    public SysMenu isPath(SysMenu menu) {
        List<SysMenu> sysMenuList = menuMapper.selectParenId(menu.getMenuId());
        if (sysMenuList.size() > 0) {
            //不是平台管理的公司id就是企业级
            List<SysMenu> sysMenus = menuMapper.selectParenIds(menu.getMenuId(), "1");
            if (sysMenus.size() > 0) {
                String url = buildDefaultJump(sysMenus, menu.getMenuId());
                menu.setWebPath(menu.getPath() + "/" + url);
            }
        } else {
            menu.setWebPath(menu.getPath());
        }
        return menu;
    }


    /**
     * 获取操作角色时展示的菜单
     */
    @Override
    public List<SysMenu> selectNonPlatformMenuList(SysMenu menu) {
        List<SysMenu> list = menuMapper.selectNonPlatformMenuList(menu);
        // 过滤菜单`
        return filterPlat(list);

    }

    /**
     * 查询子级菜单
     *
     * @param menuId
     * @return
     */
    @Override
    public List<SysMenu> selectSubMenu(Long menuId) {
        return menuMapper.selectParenIds(menuId, null);
    }

    @Override
    public List<SysMenu> selectChilds(Long menuId){
        return menuMapper.selectChilds(menuId);
    }

    /**
     * 更新菜单级别并将其删除所有权限
     *
     * @param menuId
     * @param level
     * @param list
     * @return
     */
    @Override
    public int updateMenuLevel(Long menuId, String level, List<SysMenu> list) {
        try {
            int row = menuMapper.updateMenuLevel(menuId, level);
            if (row > 0) {
                deleteMenuRole(menuId, level);
                for (SysMenu sysMenu : list) {
                    menuMapper.updateMenuLevel(sysMenu.getMenuId(), level);
                    deleteMenuRole(sysMenu.getMenuId(), level);
                }
            }
        } catch (Exception e) {
            return 0;
        }
        return 1;
    }

    @Override
    public List<String> selectMenuByRoleIds(String strIds) {
        List<String> list = menuMapper.selectMenuByRoleIds(strIds);
        return list;
    }

    /**
     * 删除对应菜单所有权限
     *
     * @param menuId
     * @param level
     */
    public void deleteMenuRole(Long menuId, String level) {
        if ("0".equals(level)) {
            List<SysMenu> menus = menuMapper.selectButtonMenu(menuId);
            if (menus != null && menus.size() != 0) {
                menus.forEach(m -> roleMenuMapper.deleteMenuId(m.getMenuId()));
            }
            roleMenuMapper.deleteMenuId(menuId);
        }
    }

    /**
     * 角色修改列表中需要的菜单层级展示
     *
     * @param sysMenus
     * @return
     */
    public List<SysMenu> filterPlat(List<SysMenu> sysMenus) {
        List<SysMenu> sysMenuList = new ArrayList<>();
        for (SysMenu sysMenu : sysMenus) {
            if (sysMenu.getMenuLevel() != null) {   // 过滤非平台级的菜单
                if ("1".equals(sysMenu.getMenuLevel())) {
                    sysMenuList.add(sysMenu);
                }
            } else if ("F".equals(sysMenu.getMenuType())) {   // 过滤父级为非平台级的按钮
                SysMenu selectMenuById = selectMenuById(sysMenu.getParentId());
                if ("1".equals(selectMenuById.getMenuLevel())) {
                    sysMenuList.add(sysMenu);
                }
            } else if ("S".equals(sysMenu.getMenuType())) {   // 过滤子系统
                sysMenuList.add(sysMenu);
            }
        }
        return sysMenuList;
    }

    public String buildDefaultJump(List<SysMenu> sysMenus, Long menuId) {
        LoginUser user = SecurityUtils.getLoginUser();
        // 如果全部都没设置默认跳转，就走第一个
        SysMenu menu = sysMenus.get(0);
        String url = menu.getPath();
        Map<Long, SysMenu> map = sysMenus.stream().collect(Collectors.toMap(SysMenu::getMenuId, s -> s));
        if (user.isSuperAdmin()) {
            for (SysMenu menuOne : sysMenus) {
                if ("0".equals(menuOne.getIsJump())) {
                    url = menuOne.getPath();
                }
            }
            return url;
        }
        List<SysMenu> menus = menuMapper.buildDefaultJump(menuId, user.getUserId());
        // 走用户所得子菜单第一个
        boolean flag = true;
        // 先取默认跳转的
        if (sysMenus.size() == menus.size()) {
            for (SysMenu menuOne : sysMenus) {
                if ("0".equals(menuOne.getIsJump())) {
                    url = menuOne.getPath();
                    flag = false;
                }
            }
        }
        if (flag && menus != null && menus.size() != 0) {
            SysMenu sysMenu = map.get(menus.get(0).getMenuId());
            url = sysMenu.getPath();
            for (SysMenu menuData : menus) {
                SysMenu menuOne = map.get(menuData.getMenuId());
                if ("0".equals(menuOne.getIsJump())) {
                    url = menuOne.getPath();
                }
            }
        }
        return url;
    }

    @Override
    public void loadingConfigCache() {
        try {
            SysMenu sysMenu = new SysMenu();
            sysMenu.setStatus("0");
            List<SysMenu> list = menuMapper.selectMenuList(sysMenu);
            list.forEach(s -> redisService.setCacheObject(Constants.SYS_MENU + s.getMenuId(), s));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void clearConfigCache() {
        try {
            Collection<String> keys = redisService.keys(Constants.SYS_MENU + "*");
            redisService.deleteObject(keys);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Async
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }
}
