package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.Binder;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.SysNewsNotification;
import com.ruoyi.system.mapper.SysNewsNotificationMapper;
import com.ruoyi.system.service.ISysNewsNotificationService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 消息管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Service
public class SysNewsNotificationServiceImpl extends ServiceImpl<SysNewsNotificationMapper, SysNewsNotification> implements ISysNewsNotificationService {

    /**
     * 查询消息管理
     *
     * @param id 消息管理主键
     * @return 消息管理
     */
    @Override
    public SysNewsNotification selectById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询消息管理列表
     *
     * @param sysNewsNotification 消息管理
     * @return 消息管理
     */
    @Override
    public List<SysNewsNotification> selectList(SysNewsNotification sysNewsNotification) {
        QueryWrapper<SysNewsNotification> queryWrapper = QueryBuilder.toQueryWrapper(sysNewsNotification);
          queryWrapper.orderByDesc("create_time");
        List<SysNewsNotification> sysNewsNotificationList = baseMapper.selectList(queryWrapper);
        Binder.bindRelations(sysNewsNotificationList);
        return sysNewsNotificationList;
    }

    /**
     * 新增消息管理
     *
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    @Override
    public int insert(SysNewsNotification sysNewsNotification) {


        sysNewsNotification.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysNewsNotification);
    }

    /**
     * 修改消息管理
     *
     * @param sysNewsNotification 消息管理
     * @return 结果
     */
    @Override
    public int update(SysNewsNotification sysNewsNotification) {
        sysNewsNotification.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(sysNewsNotification);
    }

    /**
     * 批量删除消息管理
     *
     * @param ids 需要删除的消息管理主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除消息管理信息
     *
     * @param id 消息管理主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }


    @Override
    public SysNewsNotification selectByType(Integer type) {
        QueryWrapper<SysNewsNotification> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysNewsNotification::getType, type);
        return baseMapper.selectOne(queryWrapper);
    }
}
