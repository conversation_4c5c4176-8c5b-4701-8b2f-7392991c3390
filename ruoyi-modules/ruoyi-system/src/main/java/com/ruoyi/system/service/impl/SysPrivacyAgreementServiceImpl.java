package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.SysPrivacyAgreement;
import com.ruoyi.system.mapper.SysPrivacyAgreementMapper;
import com.ruoyi.system.service.ISysPrivacyAgreementService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 隐私协议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-15
 */
@Service
public class SysPrivacyAgreementServiceImpl extends ServiceImpl<SysPrivacyAgreementMapper, SysPrivacyAgreement> implements ISysPrivacyAgreementService
{
    
    /**
     * 查询隐私协议
     * 
     * @param id 隐私协议主键
     * @return 隐私协议
     */
    @Override
    public SysPrivacyAgreement selectById(Long id)
    {
        return baseMapper.selectById(id);
    }

    /**
     * 查询隐私协议列表
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 隐私协议
     */
    @Override
    public List<SysPrivacyAgreement> selectList(SysPrivacyAgreement sysPrivacyAgreement)
    {
        QueryWrapper<SysPrivacyAgreement> queryWrapper = QueryBuilder.toQueryWrapper(sysPrivacyAgreement);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 新增隐私协议
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    @Override
    public int insert(SysPrivacyAgreement sysPrivacyAgreement)
    {
        sysPrivacyAgreement.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysPrivacyAgreement);
    }

    /**
     * 修改隐私协议
     * 
     * @param sysPrivacyAgreement 隐私协议
     * @return 结果
     */
    @Override
    public int update(SysPrivacyAgreement sysPrivacyAgreement)
    {
        sysPrivacyAgreement.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(sysPrivacyAgreement);
    }

    /**
     * 批量删除隐私协议
     * 
     * @param ids 需要删除的隐私协议主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids)
    {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除隐私协议信息
     * 
     * @param id 隐私协议主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return baseMapper.deleteById(id);
    }
}
