package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.Binder;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.domain.SysProblemFeedback;
import com.ruoyi.system.mapper.SysProblemFeedbackMapper;
import com.ruoyi.system.service.ISysProblemFeedbackService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 问题反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@Service
public class SysProblemFeedbackServiceImpl extends ServiceImpl<SysProblemFeedbackMapper, SysProblemFeedback> implements ISysProblemFeedbackService {

    /**
     * 查询问题反馈
     *
     * @param id 问题反馈主键
     * @return 问题反馈
     */
    @Override
    public SysProblemFeedback selectById(Long id) {
        SysProblemFeedback sysProblemFeedback=  baseMapper.selectById(id);
        if (StringUtils.isNotBlank(sysProblemFeedback.getImgUrl())) {
            String[] list1 = sysProblemFeedback.getImgUrl().split(",");
            sysProblemFeedback.setImgUrls(Arrays.asList(list1));
        }
        Binder.bindRelations(sysProblemFeedback);

        return sysProblemFeedback;
    }

    /**
     * 查询问题反馈列表
     *
     * @param sysProblemFeedback 问题反馈
     * @return 问题反馈
     */
    @Override
    public List<SysProblemFeedback> selectList(SysProblemFeedback sysProblemFeedback) {
        QueryWrapper<SysProblemFeedback> queryWrapper = QueryBuilder.toQueryWrapper(sysProblemFeedback);
        queryWrapper.orderByDesc("create_time");
        List<SysProblemFeedback> sysProblemFeedbacks = baseMapper.selectList(queryWrapper);
        Binder.bindRelations(sysProblemFeedbacks);

        sysProblemFeedbacks.forEach(K -> {
            if (StringUtils.isNotBlank(K.getImgUrl())) {
                String[] list1 = K.getImgUrl().split(",");
                K.setImgUrls(Arrays.asList(list1));
            }


        });
        return sysProblemFeedbacks;
    }

    /**
     * 新增问题反馈
     *
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    @Override
    public int insert(SysProblemFeedback sysProblemFeedback) {
        sysProblemFeedback.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysProblemFeedback);
    }

    /**
     * 修改问题反馈
     *
     * @param sysProblemFeedback 问题反馈
     * @return 结果
     */
    @Override
    public int update(SysProblemFeedback sysProblemFeedback) {
        sysProblemFeedback.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(sysProblemFeedback);
    }

    /**
     * 批量删除问题反馈
     *
     * @param ids 需要删除的问题反馈主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除问题反馈信息
     *
     * @param id 问题反馈主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }
}
