package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.SysPunish;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysPunishMapper;
import com.ruoyi.system.service.ISysPunishService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 处罚信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@Service
public class SysPunishServiceImpl extends ServiceImpl<SysPunishMapper, SysPunish> implements ISysPunishService {
    @Override
    public SysPunish selectSysPunishById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public SysPunish selectSysPunishByNo(String no) {
        LambdaQueryWrapper<SysPunish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(no != null, SysPunish::getComplaintNo, no);
        List<SysPunish> list = baseMapper.selectList(queryWrapper);
        return (list != null && list.size()>0) ? list.get(0) : null;
    }

    @Override
    public List<SysPunish> selectSysPunishList(SysPunish sysPunish) {
        LambdaQueryWrapper<SysPunish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(sysPunish.getPunishType()!=null,SysPunish::getPunishType,sysPunish.getPunishType());
        queryWrapper.eq(sysPunish.getTargetObjId()!=null,SysPunish::getTargetObjId,sysPunish.getTargetObjId());
        queryWrapper.eq(sysPunish.getComplaintNo()!=null,SysPunish::getComplaintNo,sysPunish.getComplaintNo());
        queryWrapper.eq(sysPunish.getPunishDuration()!=null,SysPunish::getPunishDuration,sysPunish.getPunishDuration());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysPunish> selectSysPunishList(Long targetObjId) {
        LambdaQueryWrapper<SysPunish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(targetObjId != null, SysPunish::getTargetObjId, targetObjId);
        queryWrapper.isNull(SysPunish::getPunishRelieveTime);
        queryWrapper.isNull(SysPunish::getPunishRelieveName);
        queryWrapper.eq(SysPunish::getPunishType, "2");
        queryWrapper.orderByDesc(SysPunish::getPunishDuration);
        queryWrapper.orderByAsc(SysPunish::getPunishEndTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public int insertSysPunish(SysPunish sysPunish) {
        sysPunish.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(sysPunish);
    }

    @Override
    public int updateSysPunish(SysPunish sysPunish) {
        LambdaUpdateWrapper<SysPunish> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysPunish::getUpdateBy, SecurityUtils.getUsername());
        updateWrapper.eq(SysPunish::getUpdateTime,DateUtils.getNowDate());
        return baseMapper.updateById(sysPunish);
    }

    @Override
    public int deleteSysPunishByIds(Long[] ids) {
        List<Long> list = Arrays.asList(ids);
        return baseMapper.deleteBatchIds(list);
    }

    @Override
    public int deleteSysPunishById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public SysPunish selectSysPunish(String complaintNo) {
        LambdaQueryWrapper<SysPunish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(complaintNo != null, SysPunish::getComplaintNo, complaintNo);
        return baseMapper.selectOne(queryWrapper);
    }
}
