package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diboot.core.binding.Binder;
import com.diboot.core.binding.QueryBuilder;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.domain.SysSalesManager;
import com.ruoyi.system.domain.statistics.SalesManagerStatInfo;
import com.ruoyi.system.mapper.SysSalesManagerMapper;
import com.ruoyi.system.service.ISysSalesManagerService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 销售经理信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@Service
public class SysSalesManagerServiceImpl extends ServiceImpl<SysSalesManagerMapper, SysSalesManager> implements ISysSalesManagerService {

    /**
     * 查询销售经理信息
     *
     * @param id 销售经理信息主键
     * @return 销售经理信息
     */
    @Override
    public SysSalesManager selectById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询销售经理信息列表
     *
     * @param sysSalesManager 销售经理信息
     * @return 销售经理信息
     */
    @Override
    public List<SysSalesManager> selectList(SysSalesManager sysSalesManager) {
        LambdaQueryWrapper<SysSalesManager> queryWrapper = QueryBuilder.toLambdaQueryWrapper(sysSalesManager);
        queryWrapper.orderByDesc(SysSalesManager::getId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<SalesManagerStatInfo> getSalesManagerStats(Date startTime, Date endTime) {
        List<SalesManagerStatInfo> list = baseMapper.getSalesManagerStats(startTime, endTime);
        Binder.bindRelations(list);
        return list;
    }

    /**
     * 新增销售经理信息
     *
     * @param sysSalesManager 销售经理信息
     * @return 结果
     */
    @Override
    public int insert(SysSalesManager sysSalesManager) {
        return baseMapper.insert(sysSalesManager);
    }

    /**
     * 修改销售经理信息
     *
     * @param sysSalesManager 销售经理信息
     * @return 结果
     */
    @Override
    public int update(SysSalesManager sysSalesManager) {
        return baseMapper.updateById(sysSalesManager);
    }

    /**
     * 批量删除销售经理信息
     *
     * @param ids 需要删除的销售经理信息主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除销售经理信息信息
     *
     * @param id 销售经理信息主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public SysSalesManager selectSalesManager(String phone,String name) {
        LambdaQueryWrapper<SysSalesManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(phone!=null,SysSalesManager::getPhone, phone);
        queryWrapper.eq(name!=null,SysSalesManager::getName, name);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public SysSalesManager selectSalesManager(SysSalesManager salesManager) {
        LambdaQueryWrapper<SysSalesManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysSalesManager::getPhone, salesManager.getPhone());
        queryWrapper.eq(SysSalesManager::getName, salesManager.getName());
        queryWrapper.eq(StringUtils.isNotEmpty(salesManager.getCompanyName()),SysSalesManager::getCompanyName, salesManager.getCompanyName());
        return baseMapper.selectOne(queryWrapper);
    }
}
