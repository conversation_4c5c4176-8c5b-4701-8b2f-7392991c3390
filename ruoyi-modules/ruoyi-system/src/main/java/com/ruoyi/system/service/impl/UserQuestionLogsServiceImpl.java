package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserQuestionLogsMapper;
import com.ruoyi.system.domain.UserQuestionLogs;
import com.ruoyi.system.service.IUserQuestionLogsService;

/**
 * 用户问答记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
@Service
public class UserQuestionLogsServiceImpl implements IUserQuestionLogsService 
{
    @Autowired
    private UserQuestionLogsMapper userQuestionLogsMapper;

    /**
     * 查询用户问答记录
     * 
     * @param id 用户问答记录主键
     * @return 用户问答记录
     */
    @Override
    public UserQuestionLogs selectUserQuestionLogsById(Long id)
    {
        return userQuestionLogsMapper.selectUserQuestionLogsById(id);
    }

    /**
     * 查询用户问答记录列表
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 用户问答记录
     */
    @Override
    public List<UserQuestionLogs> selectUserQuestionLogsList(UserQuestionLogs userQuestionLogs)
    {
        return userQuestionLogsMapper.selectUserQuestionLogsList(userQuestionLogs);
    }

    /**
     * 新增用户问答记录
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 结果
     */
    @Override
    public int insertUserQuestionLogs(UserQuestionLogs userQuestionLogs)
    {
        userQuestionLogs.setCreateTime(DateUtils.getNowDate());
        return userQuestionLogsMapper.insertUserQuestionLogs(userQuestionLogs);
    }

    /**
     * 修改用户问答记录
     * 
     * @param userQuestionLogs 用户问答记录
     * @return 结果
     */
    @Override
    public int updateUserQuestionLogs(UserQuestionLogs userQuestionLogs)
    {
        return userQuestionLogsMapper.updateUserQuestionLogs(userQuestionLogs);
    }

    /**
     * 批量删除用户问答记录
     * 
     * @param ids 需要删除的用户问答记录主键
     * @return 结果
     */
    @Override
    public int deleteUserQuestionLogsByIds(Long[] ids)
    {
        return userQuestionLogsMapper.deleteUserQuestionLogsByIds(ids);
    }

    /**
     * 删除用户问答记录信息
     * 
     * @param id 用户问答记录主键
     * @return 结果
     */
    @Override
    public int deleteUserQuestionLogsById(Long id)
    {
        return userQuestionLogsMapper.deleteUserQuestionLogsById(id);
    }
}
