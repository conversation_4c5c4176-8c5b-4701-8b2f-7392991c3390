package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserQuestionStatMapper;
import com.ruoyi.system.domain.UserQuestionStat;
import com.ruoyi.system.service.IUserQuestionStatService;

/**
 * 用户问答统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
@Service
public class UserQuestionStatServiceImpl implements IUserQuestionStatService 
{
    @Autowired
    private UserQuestionStatMapper userQuestionStatMapper;

    /**
     * 查询用户问答统计
     * 
     * @param id 用户问答统计主键
     * @return 用户问答统计
     */
    @Override
    public UserQuestionStat selectUserQuestionStatById(Long id)
    {
        return userQuestionStatMapper.selectUserQuestionStatById(id);
    }

    /**
     * 查询用户问答统计列表
     * 
     * @param userQuestionStat 用户问答统计
     * @return 用户问答统计
     */
    @Override
    public List<UserQuestionStat> selectUserQuestionStatList(UserQuestionStat userQuestionStat)
    {
        return userQuestionStatMapper.selectUserQuestionStatList(userQuestionStat);
    }

    /**
     * 新增用户问答统计
     * 
     * @param userQuestionStat 用户问答统计
     * @return 结果
     */
    @Override
    public int insertUserQuestionStat(UserQuestionStat userQuestionStat)
    {
        userQuestionStat.setCreateTime(DateUtils.getNowDate());
        return userQuestionStatMapper.insertUserQuestionStat(userQuestionStat);
    }

    /**
     * 修改用户问答统计
     * 
     * @param userQuestionStat 用户问答统计
     * @return 结果
     */
    @Override
    public int updateUserQuestionStat(UserQuestionStat userQuestionStat)
    {
        return userQuestionStatMapper.updateUserQuestionStat(userQuestionStat);
    }

    /**
     * 批量删除用户问答统计
     * 
     * @param ids 需要删除的用户问答统计主键
     * @return 结果
     */
    @Override
    public int deleteUserQuestionStatByIds(Long[] ids)
    {
        return userQuestionStatMapper.deleteUserQuestionStatByIds(ids);
    }

    /**
     * 删除用户问答统计信息
     * 
     * @param id 用户问答统计主键
     * @return 结果
     */
    @Override
    public int deleteUserQuestionStatById(Long id)
    {
        return userQuestionStatMapper.deleteUserQuestionStatById(id);
    }

    /**
     * 新增多条
     * @param list
     * @return
     */
    @Override
    public int batchInsert(List<UserQuestionStat> list) {
        return userQuestionStatMapper.batchInsert(list);
    }
}
