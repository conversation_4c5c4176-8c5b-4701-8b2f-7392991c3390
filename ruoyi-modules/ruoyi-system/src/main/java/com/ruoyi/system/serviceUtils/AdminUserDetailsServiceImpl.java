package com.ruoyi.system.serviceUtils;

import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Service
public class AdminUserDetailsServiceImpl implements UserDetailsService {
//    private static final Logger log = LoggerFactory.getLogger(AppUserDetailsServiceImpl.class);

    @Autowired
    private ISysAdminUserService adminUserService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysRoleService roleService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        SysAdminUser adminUser = adminUserService.selectUserByUserName(username);

        if (StringUtils.isNull(adminUser)) {
//            log.info("登录用户：{} 不存在.", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        } else if (UserStatus.DELETED.getCode().equals(adminUser.getDelFlag())) {
//            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 不存在");
        } else if (UserStatus.DISABLE.getCode().equals(adminUser.getStatus())) {
//            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        adminUser.setRoles(roleService.selectUserRolesByUserId(adminUser.getUserId()));
        Map<String, Set<String>> permission = permissionService.getMenuPermission(adminUser);
        Set<String> set = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : permission.entrySet()) {
            set.addAll(entry.getValue());
        }
        return new LoginUser(adminUser, set);
    }
}
