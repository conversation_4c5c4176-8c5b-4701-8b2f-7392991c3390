package com.ruoyi.system.serviceUtils;

import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.*;
import com.ruoyi.system.service.ISysAppUserRoleService;
import com.ruoyi.system.service.ISysDeptService;

import java.util.List;

public class AjaxResultUtil {

    public static AjaxResult success(SysUser user, String token) {
        AjaxResult result = AjaxResult.success();
        result.put("token", token);
        result.put("userId", user.getUserId());
        result.put("userName", user.getUserName());
        result.put("nickName", user.getNickName());
        result.put("email", user.getEmail());
        result.put("avatar", user.getAvatar());
        if (user instanceof SysAdminUser) {
            SysAdminUser adminUser = (SysAdminUser) user;
            result.put("phonenumber", adminUser.getPhonenumber());
        } else if (user instanceof SysAppUser) {
            SysAppUser appUser = (SysAppUser) user;
            result.put("position", appUser.getPosition());
            result.put("sex", appUser.getSex());
            result.put("level", appUser.getLevel());
            result.put("userType", appUser.getUserType());

            Long deptId = appUser.getDeptId();
            if (deptId != null && !deptId.equals(user.getCompanyId())) {
                ISysDeptService deptService = SpringUtils.getBean(ISysDeptService.class);

                SysDept sysDept = deptService.selectDeptById(deptId);
                if (sysDept != null && sysDept.isEnabled()) {
                    result.put("deptName", appUser.getDeptNames());
                }
            }

            ISysAppUserRoleService userRoleService = SpringUtils.getBean(ISysAppUserRoleService.class);
            List<SysAppUserRole> userRoles = userRoleService.selectByUser(user.getUserId());
            result.put("roles", userRoles);
        }

        Long companyId = user.getCompanyId();
        //公司id不为空的话返回公司信息
        if (companyId != null) {
            ISysDeptService deptService = SpringUtils.getBean(ISysDeptService.class);
            SysDept sysDept = deptService.selectDeptById(companyId);
            result.put("company", sysDept);
        }

        return result;
    }
}
