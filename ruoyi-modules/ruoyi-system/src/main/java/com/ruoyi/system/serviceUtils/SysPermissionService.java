package com.ruoyi.system.serviceUtils;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.wplus.Constant;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysRole;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionService {
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysAdminUserService adminUserService;

    /**
     * 获取角色数据权限
     *
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user) {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isSuperAdmin()) {
            roles.add("admin");
        } else {
            List<SysRole> roleList = roleService.selectRolePermissionByUserId(user.getUserId());
            Set<String> permsSet = new HashSet<>();
            for (SysRole perm : roleList) {
                if (StringUtils.isNotNull(perm)) {
                    permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
                }
            }
            roles.addAll(permsSet);
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Map<String,Set<String>> getMenuPermission(SysUser user) {
        Set<String> perms = new HashSet<>();
        Map<String, Set<String>> map = new HashMap<>();
        // 管理员拥有所有权限
        if (user.isSuperAdmin()) {
            perms.add("*:*:*");
            map.put(StringUtils.toCamelCase(Constants.ROLE_PERMISSIONS+Constants.SUB_SYSTEM_WEB),perms);
            map.put(StringUtils.toCamelCase(Constants.ROLE_PERMISSIONS+Constants.SUB_SYSTEM_COM),perms);
            map.put(StringUtils.toCamelCase(Constants.ROLE_PERMISSIONS+Constants.SUB_SYSTEM_DEBUG),perms);
        } else {
            List<String> list = adminUserService.selectSubSystemFlag(user.getUserId());
            for (String key : list) {
                Set<String> set = menuService.selectMenuPermsByUserId(user, key);
                // 转驼峰
                String systemKey = StringUtils.toCamelCase(Constants.ROLE_PERMISSIONS + key);
                map.put(systemKey,set);
            }
        }
        return map;
    }

}
