package com.ruoyi.system.threadHandle;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.system.queue.AppErrorLogQueue;
import com.ruoyi.system.service.IAppErrorLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * app崩溃日志队列处理类
 *
 * <AUTHOR>
 * @date 2022-09-22
 */
@Component
public class DealAppErrorLogRedisThread {
    @Autowired
    private IAppErrorLogService appErrorLogService;

    @Autowired
    private RedisCache redisCache;

    // 单次获取redis的数量
    private static final int DEAL_LEN = 9;

    @PostConstruct
    public void startAppErrorLogThread() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 1, 10, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));
        threadPoolExecutor.submit(new PopAppErrorLog(appErrorLogService));
    }

    class PopAppErrorLog implements Runnable {

        private IAppErrorLogService appErrorLogService;

        public PopAppErrorLog(IAppErrorLogService appErrorLogService) {
            this.appErrorLogService = appErrorLogService;
        }

        @Override
        public void run() {
            while (true) {
                List<String> list = redisCache.getCacheListRange(Constants.APP_ERROR_LOGKEY, 0, DEAL_LEN);
                if (list != null && list.size() != 0) {
                    appErrorLogService.insertBatch(list);
                    // 为保障性能，默认持久化到MySQL成功，移除对应的redis
                    redisCache.trimCacheList(Constants.APP_ERROR_LOGKEY, DEAL_LEN+1, -1);
                }else{
                    AppErrorLogQueue appErrorLogQueue = AppErrorLogQueue.getInstance();
                    appErrorLogQueue.pop();
                }
            }
        }
    }
}
