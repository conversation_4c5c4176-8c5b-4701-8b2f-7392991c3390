/*
 Navicat Premium Data Transfer

 Source Server         : 109
 Source Server Type    : MySQL
 Source Server Version : 50739
 Source Host           : ************:3306
 Source Schema         : agent_service

 Target Server Type    : MySQL
 Target Server Version : 50739
 File Encoding         : 65001

 Date: 07/05/2025 18:05:51
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for kb_conversation
-- ----------------------------
DROP TABLE IF EXISTS `kb_conversation`;
CREATE TABLE `kb_conversation`
(
    `id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `group_id`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户id',
    `kb_id`       bigint(20) DEFAULT NULL COMMENT '知识库id',
    `title`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '标题',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kb_file_info
-- ----------------------------
DROP TABLE IF EXISTS `kb_file_info`;
CREATE TABLE `kb_file_info`
(
    `file_id`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `kb_id`              bigint(20) NULL DEFAULT NULL COMMENT '知识库id',
    `file_name`          text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '文件名',
    `file_original_name` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '原文件名',
    `content`            text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '文件内容',
    `file_extension`     varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件后缀',
    `allow_download`     int(11) NULL DEFAULT NULL COMMENT '是否允许下载: 0: 允许, 1: 不允许',
    `file_size`          int(11) NULL DEFAULT NULL COMMENT '文件大小',
    `file_state`         int(11) NULL DEFAULT NULL COMMENT '文件状态: 0: 正常, 1: 失效',
    `file_enable`        int(11) NULL DEFAULT NULL COMMENT '文件是否可用: 0: 可用, 1: 不可用',
    `trunk_count`        int(11) NULL DEFAULT NULL COMMENT '分片总数',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '更新时间',
    `upload_time`        datetime NULL DEFAULT NULL COMMENT '上传时间',
    `del_flag`           tinyint(1) DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (`file_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kb_file_trunk_info
-- ----------------------------
DROP TABLE IF EXISTS `kb_file_trunk_info`;
CREATE TABLE `kb_file_trunk_info`
(
    `id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `file_id`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件id',
    `kb_id`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '知识库id',
    `stored_vector_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '向量id',
    `content`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '文件内容',
    `sort`        int(11) NULL DEFAULT NULL COMMENT '排序',
    `status`      int(11) NOT NULL DEFAULT 0 COMMENT '正常: 0, 禁用: 1, 失效: 2 (用户修改后，待重新生成)',
    `meta_data`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '元数据',
    `keywords`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '关键词',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`    tinyint(1) DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件块信息表' ROW_FORMAT = DYNAMIC;

SET
FOREIGN_KEY_CHECKS = 1;
