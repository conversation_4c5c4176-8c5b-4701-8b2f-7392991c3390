/*
 Navicat Premium Data Transfer

 Source Server         : 109
 Source Server Type    : MySQL
 Source Server Version : 50739
 Source Host           : ************:3306
 Source Schema         : ai_kb_tian

 Target Server Type    : MySQL
 Target Server Version : 50739
 File Encoding         : 65001

 Date: 07/06/2025 11:13:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_certificate_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_certificate_info`;
CREATE TABLE `tb_certificate_info`  (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                        `certificate_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书/资质名称',
                                        `file_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书文件地址',
                                        `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '证书信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_education_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_education_info`;
CREATE TABLE `tb_education_info`  (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                      `education_level` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学历',
                                      `graduation_school` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业院校',
                                      `Introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介',
                                      `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业',
                                      `start_time` datetime(0) NULL DEFAULT NULL COMMENT '起止时间',
                                      `end_time` datetime(0) NULL DEFAULT NULL COMMENT '截至时间',
                                      `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教育信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_family_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_family_info`;
CREATE TABLE `tb_family_info`  (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                   `member_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
                                   `relationship` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关系',
                                   `workplace` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
                                   `position` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职务',
                                   `member_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
                                   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '家庭信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_personal_file
-- ----------------------------
DROP TABLE IF EXISTS `tb_personal_file`;
CREATE TABLE `tb_personal_file`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                     `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名称',
                                     `file_size` int(11) NULL DEFAULT NULL COMMENT '文件大小（单位：字节）',
                                     `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
                                     `upload_time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
                                     `del_flag` int(11) NULL DEFAULT NULL COMMENT '删除标识（0正常，1删除）',
                                     `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                     `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                     `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                     `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人档案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_personal_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_personal_info`;
CREATE TABLE `tb_personal_info`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `user_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
                                     `task_personal_id` bigint(20) NULL DEFAULT NULL COMMENT '寻才简历Id',
                                     `sex` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户性别',
                                     `age` int(11) NULL DEFAULT NULL COMMENT '年龄',
                                     `id_card` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份证号',
                                     `ethnicity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '民族',
                                     `marriage_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '婚姻状态（0未婚 1已婚）',
                                     `education` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '学历',
                                     `school_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业院校',
                                     `major` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所学专业',
                                     `position` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '职位',
                                     `phone` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '手机号码',
                                     `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '技能',
                                     `certificate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '证书标签',
                                     `email` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户邮箱',
                                     `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '照片地址',
                                     `current_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前地址',
                                     `political_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '政治面貌',
                                     `years_of_experience` int(11) NULL DEFAULT NULL COMMENT '工作经验',
                                     `employment_status` tinyint(1) NULL DEFAULT NULL COMMENT '入职状态',
                                     `old_work_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '入职前工作状态',
                                     `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '个人简介',
                                     `foreign_proficiency` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外语水平',
                                     `professional_level` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '专业水平',
                                     `job_intent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '求职意向',
                                     `salary_expectation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '期望薪资',
                                     `recruitment_channel` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '招聘渠道',
                                     `minimum_education_score` double(10, 1) NULL DEFAULT NULL COMMENT '最低学历分',
  `work_experience_score` double(10, 1) NULL DEFAULT NULL COMMENT '工作经验分',
  `job_hopping_rate_score` double(10, 1) NULL DEFAULT NULL COMMENT '跳槽频率分',
  `salary_range_score` double(10, 1) NULL DEFAULT NULL COMMENT '薪资范围分',
  `total_score` double(10, 1) NULL DEFAULT NULL COMMENT '总分',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标识 0正常，1删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 77 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人简历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_task_certificate_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_certificate_info`;
CREATE TABLE `tb_task_certificate_info`  (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                             `certificate_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书/资质名称',
                                             `file_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书文件地址',
                                             `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                             `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '证书信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_education_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_education_info`;
CREATE TABLE `tb_task_education_info`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                           `education_level` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学历',
                                           `graduation_school` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业院校',
                                           `Introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介',
                                           `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业',
                                           `skills` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '技能',
                                           `start_time` datetime(0) NULL DEFAULT NULL COMMENT '起止时间',
                                           `end_time` datetime(0) NULL DEFAULT NULL COMMENT '截至时间',
                                           `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                           `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                           `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教育信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_family_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_family_info`;
CREATE TABLE `tb_task_family_info`  (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                        `member_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
                                        `relationship` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关系',
                                        `workplace` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
                                        `position` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职务',
                                        `member_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
                                        `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '家庭信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_personal_file
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_personal_file`;
CREATE TABLE `tb_task_personal_file`  (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                          `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名称',
                                          `file_size` int(11) NULL DEFAULT NULL COMMENT '文件大小（单位：字节）',
                                          `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
                                          `upload_time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
                                          `del_flag` int(11) NULL DEFAULT NULL COMMENT '删除标识（0正常，1删除）',
                                          `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                          `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                          `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                          `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人档案表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_personal_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_personal_info`;
CREATE TABLE `tb_task_personal_info`  (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `job_id` bigint(20) NOT NULL COMMENT '任务Id',
                                          `user_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
                                          `sex` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户性别（0男 1女 2未知）',
                                          `age` int(11) NULL DEFAULT NULL COMMENT '年龄',
                                          `id_card` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份证号',
                                          `ethnicity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '民族',
                                          `marriage_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '婚姻状态（0未婚 1已婚）',
                                          `education` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '学历',
                                          `school_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业学校',
                                          `major` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业',
                                          `position` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '职位',
                                          `phone` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '手机号码',
                                          `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '技能',
                                          `certificate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关证书',
                                          `email` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户邮箱',
                                          `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '照片地址',
                                          `current_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前地址',
                                          `political_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '政治面貌',
                                          `years_of_experience` int(11) NULL DEFAULT NULL COMMENT '工作经验',
                                          `work_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前工作状态',
                                          `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '个人简介',
                                          `foreign_proficiency` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外语水平',
                                          `professional_level` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '专业水平',
                                          `job_intent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '求职意向',
                                          `salary_expectation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '期望薪资',
                                          `recruitment_channel` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '招聘渠道',
                                          `minimum_education_score` double(10, 1) NULL DEFAULT NULL COMMENT '最低学历分',
  `work_experience_score` double(10, 1) NULL DEFAULT NULL COMMENT '工作经验分',
  `job_hopping_rate_score` double(10, 1) NULL DEFAULT NULL COMMENT '跳槽频率分',
  `salary_range_score` double(10, 1) NULL DEFAULT NULL COMMENT '薪资范围分',
  `total_score` double(10, 1) NULL DEFAULT NULL COMMENT '总分',
  `talent_pool_status` int(11) NULL DEFAULT NULL COMMENT '纳入人才库状态（0否，1是）',
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '异常信息',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人简历信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_posting
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_posting`;
CREATE TABLE `tb_task_posting`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务Id',
                                    `user_id` bigint(20) NOT NULL COMMENT '用户Id',
                                    `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
                                    `channel` int(11) NULL DEFAULT NULL COMMENT '渠道',
                                    `query_modules` tinyint(1) NULL DEFAULT NULL COMMENT '查询模块 0:搜索牛人 1:推荐牛人',
                                    `position_type` tinyint(1) NULL DEFAULT NULL COMMENT '职位类型',
                                    `position_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位名称',
                                    `screening_count` int(11) NULL DEFAULT NULL COMMENT '筛选人数',
                                    `finish_count` int(11) NULL DEFAULT 0 COMMENT '完成人数',
                                    `minimum_education` tinyint(1) NULL DEFAULT NULL COMMENT '最低学历',
                                    `work_experience` tinyint(1) NULL DEFAULT NULL COMMENT '工作经验',
                                    `job_hopping_rate` tinyint(1) NULL DEFAULT NULL COMMENT '跳槽频率',
                                    `salary_range` tinyint(1) NULL DEFAULT NULL COMMENT '薪资范围',
                                    `screening_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '筛选条件',
                                    `status` int(11) NULL DEFAULT 0 COMMENT '状态 0:草稿 1:进行中 2:已完成 3:暂停 4:失败',
                                    `education_weight` int(11) NULL DEFAULT 0 COMMENT '学历评分侧重',
                                    `work_experience_weight` int(11) NULL DEFAULT 0 COMMENT '工作经验评分侧重',
                                    `job_hopping_rate_weight` int(11) NULL DEFAULT 0 COMMENT '跳槽频率评分侧重',
                                    `salary_range_weight` int(11) NULL DEFAULT 0 COMMENT '薪资范围评分侧重',
                                    `failure_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '失败原因',
                                    `pass_score` int(11) NULL DEFAULT 0 COMMENT '及格分',
                                    `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
                                    `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                    `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                    `experience_lower_bound` int(11) NULL DEFAULT NULL COMMENT '工作经验下限',
                                    `experience_upper_bound` int(11) NULL DEFAULT NULL COMMENT '工作经验上限',
                                    `job_hopping_rate_lower_bound` int(11) NULL DEFAULT NULL COMMENT '跳槽频率下限',
                                    `job_hopping_rate_upper_bound` int(11) NULL DEFAULT NULL COMMENT '跳槽频率上限',
                                    `salary_range_lower_bound` int(11) NULL DEFAULT NULL COMMENT '薪资范围下限',
                                    `salary_range_upper_bound` int(11) NULL DEFAULT NULL COMMENT '薪资范围上限',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 168 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '查找人才任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_task_work_experience
-- ----------------------------
DROP TABLE IF EXISTS `tb_task_work_experience`;
CREATE TABLE `tb_task_work_experience`  (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                            `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
                                            `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
                                            `resignation_reason` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '离职原因',
                                            `work_Introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工作介绍',
                                            `start_time` datetime(0) NULL DEFAULT NULL COMMENT '起止时间',
                                            `end_time` datetime(0) NULL DEFAULT NULL COMMENT '截至时间',
                                            `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                            `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                            `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                            `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 288 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作经历表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tb_work_experience
-- ----------------------------
DROP TABLE IF EXISTS `tb_work_experience`;
CREATE TABLE `tb_work_experience`  (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                       `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
                                       `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
                                       `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
                                       `resignation_reason` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '离职原因',
                                       `work_Introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工作介绍',
                                       `start_time` datetime(0) NULL DEFAULT NULL COMMENT '起止时间',
                                       `end_time` datetime(0) NULL DEFAULT NULL COMMENT '截至时间',
                                       `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                       `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 307 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作经历表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


--  招聘渠道
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES ('招聘渠道', 'recruitment_channels', '0', 'admin', NOW(), '', NULL, '招聘渠道信息');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 'Boss直聘', '0', 'recruitment_channels', '', '', 'Y', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '智联招聘', '1', 'recruitment_channels', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '58同城', '2', 'recruitment_channels', '', '', 'Y', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '脉脉', '3', 'recruitment_channels', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

-- 学历
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('学历', 'education', '0', 'admin', NOW(), '', NULL, '学历信息');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '初中及以下', '0', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '中专/中技', '1', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '高中', '2', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '大专', '3', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '本科', '4', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '硕士', '5', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '博士', '6', 'education', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

-- 职位类型
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES ('职位类型', 'turnover_frequency', '0', 'admin', NOW(), '', NULL, '职位类型');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '社招全职', '0', 'position_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '应届校园招聘', '1', 'position_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '实习生招聘', '2', 'position_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '兼职招聘', '3', 'position_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

-- 工作经验
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('入职状态', 'employment_status', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '入职状态');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '劳务派遣', '6', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '离职', '5', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '正式员工', '4', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '实习', '3', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '试用', '2', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '兼职', '1', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '未入职', '0', 'employment_status', '', '', 'N', '0', 'admin', '2025-06-05 17:40:30', '', NULL, '');
