CREATE TABLE `tb_offline_interview` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `avatar_red_dot` tinyint(1) DEFAULT '0' COMMENT '头像小红点标识（0-无，1-有）',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `education` varchar(20) DEFAULT NULL COMMENT '学历',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `job_intention` varchar(100) DEFAULT NULL COMMENT '面试职位',
  `talent_source` varchar(100) DEFAULT NULL COMMENT '人才来源',
  `interview_round` varchar(50) DEFAULT NULL COMMENT '面试轮次',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `interview_status` tinyint(1) DEFAULT NULL COMMENT '面试状态（0-未开始，1-进行中，2-已完成）',
  `interview_time` datetime DEFAULT NULL COMMENT '面试时间',
  `interview_end_time` datetime DEFAULT NULL COMMENT '面试结束时间',
  `offline_interview_source` tinyint(1) NOT NULL COMMENT '线下面试来源（0-人才表，1-寻才表）',
  `offline_interview_source_id` bigint(20) NOT NULL COMMENT '线下面试来源ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='线下面试管理表';

CREATE TABLE `tb_online_interview` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `avatar_red_dot` tinyint(1) DEFAULT '0' COMMENT '头像小红点标识（0-无，1-有）',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `education` varchar(20) DEFAULT NULL COMMENT '学历',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `job_interview` varchar(100) DEFAULT NULL COMMENT '求职意向',
  `job_intention` varchar(100) DEFAULT NULL COMMENT '面试职位',
  `talent_source` varchar(100) DEFAULT NULL COMMENT '人才来源',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `interview_status` tinyint(1) DEFAULT NULL COMMENT '面试状态（0-待面试，1-进行中，2-已完成）',
  `invite_time` datetime DEFAULT NULL COMMENT '邀请时间',
  `invite_url` varchar(500) DEFAULT NULL COMMENT '邀请链接',
  `invite_code` varchar(10) DEFAULT NULL COMMENT '邀请码',
  `job_paper_id` bigint(20) NOT NULL COMMENT '线上面试岗位试卷ID',
  `interview_time` datetime DEFAULT NULL COMMENT '面试时间',
  `online_interview_source` tinyint(1) NOT NULL COMMENT '线上面试来源（0-人才表，1-寻才表）',
  `online_interview_source_id` bigint(20) NOT NULL COMMENT '线上面试来源ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='线上面试管理表';

CREATE TABLE `tb_interview_job_paper` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `job_name` varchar(100) NOT NULL COMMENT '岗位名称',
  `valid_days` int(11) NOT NULL COMMENT '有效天数',
  `opening_remark` varchar(500) DEFAULT NULL COMMENT '开场话术',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位试卷表';

CREATE TABLE `tb_interview_job_paper_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `job_paper_id` bigint(20) NOT NULL COMMENT '岗位试卷ID',
  `question` varchar(500) NOT NULL COMMENT '题目',
  `answer` varchar(500) DEFAULT NULL COMMENT '答案',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_job_paper_id` (`job_paper_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位试卷试题表';
