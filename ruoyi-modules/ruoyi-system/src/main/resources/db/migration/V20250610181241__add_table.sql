-- 合同类型 contract_type
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`,
                            `remark`)
VALUES ('合同类型', 'contract_type', '0', 'admin', NOW(), '', NULL, '合同类型');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1, '无固定期限劳动合同', '0', 'contract_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (2, '固定期限劳动合同', '1', 'contract_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (3, '任务劳动合同', '2', 'contract_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

CREATE TABLE `tb_contract_info`
(
    `id`            BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`       BIGINT NOT NULL COMMENT '基本信息Id',
    `contract_type` TINYINT(1) DEFAULT NULL COMMENT '合同类型',
    `start_time`    DATETIME                                                      DEFAULT NULL COMMENT '起止时间',
    `end_time`      DATETIME                                                      DEFAULT NULL COMMENT '截至时间',
    `create_by`     VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '创建者',
    `create_time`   DATETIME                                                      DEFAULT NULL COMMENT '创建时间',
    `update_by`     VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '更新者',
    `update_time`   DATETIME                                                      DEFAULT NULL COMMENT '更新时间',
    `remark`        VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='合同信息表';


CREATE TABLE `tb_user_info`
(
    `id`               BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `base_personal_id` BIGINT(20) NOT NULL COMMENT '人才库信息Id',
    `dept_id`          BIGINT(20) DEFAULT NULL COMMENT '部门ID',
    `company_id`       BIGINT(20) DEFAULT NULL COMMENT '公司ID',
    `depts`            VARCHAR(50)  DEFAULT NULL COMMENT '公司部门路径',
    `position`         VARCHAR(20)  DEFAULT NULL COMMENT '职位',
    `position_level`   VARCHAR(20)  DEFAULT NULL COMMENT '职级',
    `insurance_base`   VARCHAR(20)  DEFAULT NULL COMMENT '五险一金基数',
    `insurance_ratio`  VARCHAR(20)  DEFAULT NULL COMMENT '五险一金比例',
    `salary`           VARCHAR(20)  DEFAULT NULL COMMENT '薪资',
    `entry_time`       DATETIME     DEFAULT NULL COMMENT '入职时间',
    `turnover_time`    DATETIME     DEFAULT NULL COMMENT '预计转正时间',
    `create_by`        VARCHAR(64)  DEFAULT '' COMMENT '创建者',
    `create_time`      DATETIME     DEFAULT NULL COMMENT '创建时间',
    `update_by`        VARCHAR(64)  DEFAULT '' COMMENT '更新者',
    `update_time`      DATETIME     DEFAULT NULL COMMENT '更新时间',
    `remark`           VARCHAR(500) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户档案信息';


