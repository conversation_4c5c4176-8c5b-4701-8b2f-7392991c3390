-- 追加字段
ALTER TABLE `tb_personal_info` ADD COLUMN `storage_display` tinyint(1) DEFAULT 0 COMMENT '入库展示（0:展示 1:不展示）';
ALTER TABLE `tb_personal_info` ADD COLUMN `filed_flag` tinyint(1) DEFAULT 0 COMMENT '是否建档(0:未建档 1:已建档)';
-- tb_personal_file
ALTER TABLE `tb_personal_file` ADD COLUMN `file_type` varchar(10) DEFAULT NULL COMMENT '文件类型';
ALTER TABLE `tb_personal_file` ADD COLUMN `file_source` tinyint(1) DEFAULT NULL COMMENT '文件来源( 0：基本信息，1：入职信息 2:离职文件)';
ALTER TABLE `tb_personal_file` ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注';
-- tb_user_info
ALTER TABLE `tb_user_info` ADD COLUMN `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）';
ALTER TABLE `tb_user_info` ADD COLUMN `leave_time` datetime DEFAULT NULL COMMENT '离职时间';
ALTER TABLE `tb_user_info` ADD COLUMN `leave_reason` varchar(500) DEFAULT NULL COMMENT '离职原因';
ALTER TABLE `tb_user_info` ADD COLUMN `formal_leave_time` datetime DEFAULT NULL COMMENT '正式离职时间';
-- 新增字段
ALTER TABLE `tb_task_personal_info`ADD COLUMN `filed_flag` tinyint(1) DEFAULT 0 COMMENT '是否建档(0:未建档 1:已建档)';