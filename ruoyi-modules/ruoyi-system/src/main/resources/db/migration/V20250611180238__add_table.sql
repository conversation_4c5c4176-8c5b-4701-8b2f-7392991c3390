-- 线下面试会话表
CREATE TABLE `tb_offline_interview_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `offline_interview_id` bigint(20) NOT NULL COMMENT '线下面试ID',
  `speak_num` int(11) NOT NULL COMMENT '发言序号',
  `speak_txt` text COMMENT '发言内容',
  `speak_time` datetime DEFAULT NULL COMMENT '发言时间',
  `timestamp` bigint(20) NOT NULL COMMENT '时间戳',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_offline_interview_id` (`offline_interview_id`),
  KEY `idx_speak_time` (`speak_time`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线下面试会话表';

-- 线下面试会话AI分析记录表
CREATE TABLE `tb_offline_interview_conversation_ai` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `offline_interview_id` bigint(20) NOT NULL COMMENT '线下面试ID',
  `timestamp` bigint(20) NOT NULL COMMENT '线下面试会话时间戳',
  `ai_txt` text COMMENT 'AI分析内容',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='线下面试会话AI分析记录表';

-- 线下面试会话配置表
CREATE TABLE `tb_offline_interview_conversation_setting` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `offline_interview_id` bigint(20) NOT NULL COMMENT '线下面试ID',
    `speak_num` int(11) NOT NULL COMMENT '发言序号',
    `speak_role` varchar(50) NOT NULL COMMENT '发言角色',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_interview_speak` (`offline_interview_id`, `speak_num`),
    KEY `idx_offline_interview_id` (`offline_interview_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线下面试会话配置表';

-- 线下面试会话录音文件表
CREATE TABLE `tb_offline_interview_audio_file` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `offline_interview_id` bigint(20) NOT NULL COMMENT '线下面试ID',
    `audio_file` text NOT NULL COMMENT '线下面试录音文件地址',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线下面试会话录音文件表';