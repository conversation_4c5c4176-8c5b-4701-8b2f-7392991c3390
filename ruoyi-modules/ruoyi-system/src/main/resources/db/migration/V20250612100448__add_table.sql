CREATE TABLE `tb_offline_interview_evaluation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `offline_interview_id` bigint(20) NOT NULL COMMENT '线下面试ID',
  `evaluation_content` text COMMENT '评价内容',
  `evaluation_type` char(1) DEFAULT '0' COMMENT '评价类型（0-AI 1-面试官）',
  `score` double DEFAULT NULL COMMENT '评分',
  `interviewer_name` varchar(50) DEFAULT NULL COMMENT '面试官姓名',
  `interviewer_user_name` varchar(50) DEFAULT NULL COMMENT '面试官用户名',
  `interviewer_user_id` bigint(20) DEFAULT NULL COMMENT '面试官ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_offline_interview_id` (`offline_interview_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='线下面试评价表';