-- 类型字典
INSERT INTO `sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`,
                            `remark`)
VALUES ('文件类型', 'file_type', '0', 'admin', NOW(), '', NULL, '文件类型');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1, 'PDF', 'PDF', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (2, 'DOC', 'DOC', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (3, 'DOCX', 'DOCX', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (4, 'XLS', 'XLS', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (5, 'XLSX', 'XLSX', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (6, 'MP3', 'MP3', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`,
                            `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7, 'MP4', 'MP4', 'file_type', '', '', 'N', '0', 'admin', NOW(), '', NULL, '');



CREATE TABLE `tb_user_undergo`
(
    `id`           BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
    `personal_id`  BIGINT NOT NULL COMMENT '基本信息Id',
    `file_code`    tinyint(1) DEFAULT NULL COMMENT '关联文件来源编码',
    `affairs_name` VARCHAR(50) COLLATE utf8mb4_general_ci                       DEFAULT NULL COMMENT '事件名称',
    `operate_time` DATETIME                                                     DEFAULT NULL COMMENT '操作时间',
    `remark`       TEXT COLLATE utf8mb4_general_ci                              DEFAULT NULL COMMENT '事件说明',
    `create_by`    VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
    `create_time`  DATETIME                                                     DEFAULT NULL COMMENT '创建时间',
    `update_by`    VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
    `update_time`  DATETIME                                                     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='人员事件记录';
