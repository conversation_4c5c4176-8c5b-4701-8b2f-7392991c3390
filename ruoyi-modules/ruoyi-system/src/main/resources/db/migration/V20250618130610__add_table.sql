CREATE TABLE `tb_online_interview_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `interview_id` bigint(20) NOT NULL COMMENT '线上面试ID',
  `job_paper_id` bigint(20) NOT NULL COMMENT '岗位试卷ID',
  `question_id` bigint(20) NOT NULL COMMENT '试题ID',
  `answer` text COMMENT '面试者答案',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='线上面试答题记录表';