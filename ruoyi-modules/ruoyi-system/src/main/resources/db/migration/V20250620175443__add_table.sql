CREATE TABLE `tb_analyse_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `position_name` varchar(100) NOT NULL COMMENT '职位名称',
  `position_requirement` text COMMENT '职位要求',
  `pass_score` int(11) DEFAULT '0' COMMENT '及格分',
  `education_weight` int(11) DEFAULT '0' COMMENT '学历评分权重',
  `work_experience_weight` int(11) DEFAULT '0' COMMENT '工作经验评分权重',
  `job_hopping_rate_weight` int(11) DEFAULT '0' COMMENT '跳槽频率评分权重',
  `salary_range_weight` int(11) DEFAULT '0' COMMENT '薪资范围评分权重',
  `status` int(11) DEFAULT '0' COMMENT '任务状态：1进行中 2已完成',
  `resume_count` int(11) DEFAULT '0' COMMENT '简历总数',
  `resume_success_count` int(11) DEFAULT '0' COMMENT '简历总数-分析成功',
  `resume_fail_count` int(11) DEFAULT '0' COMMENT '简历总数-分析失败',
  `resume_excellent_count` int(11) DEFAULT '0' COMMENT '简历数-优',
  `resume_good_count` int(11) DEFAULT '0' COMMENT '简历数-良',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='简历分析任务表';

CREATE TABLE `tb_analyse_personal_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `job_id` bigint(20) NOT NULL COMMENT '任务Id',
  `user_name` text NOT NULL COMMENT '用户姓名',
  `sex` text COMMENT '用户性别（0男 1女 2未知）',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `id_card` text COMMENT '身份证号',
  `ethnicity` text COMMENT '民族',
  `marriage_status` text COMMENT '婚姻状态（0未婚 1已婚）',
  `education` text COMMENT '学历',
  `school_name` varchar(255) DEFAULT NULL COMMENT '毕业学校',
  `major` varchar(255) DEFAULT NULL COMMENT '专业',
  `position` text COMMENT '职位',
  `phone` text COMMENT '手机号码',
  `skills` text COMMENT '技能',
  `certificate` text COMMENT '相关证书',
  `email` text COMMENT '用户邮箱',
  `avatar` text COMMENT '照片地址',
  `current_address` text COMMENT '当前地址',
  `political_status` text COMMENT '政治面貌',
  `years_of_experience` int(11) DEFAULT NULL COMMENT '工作经验',
  `work_status` text COMMENT '当前工作状态',
  `introduction` text COMMENT '个人简介',
  `foreign_proficiency` text COMMENT '外语水平',
  `professional_level` text COMMENT '专业水平',
  `job_intent` text COMMENT '求职意向',
  `salary_expectation` text COMMENT '期望薪资',
  `recruitment_channel` text COMMENT '招聘渠道',
  `minimum_education_score` double(10,1) DEFAULT NULL COMMENT '最低学历分',
  `work_experience_score` double(10,1) DEFAULT NULL COMMENT '工作经验分',
  `job_hopping_rate_score` double(10,1) DEFAULT NULL COMMENT '跳槽频率分',
  `salary_range_score` double(10,1) DEFAULT NULL COMMENT '薪资范围分',
  `total_score` double(10,1) DEFAULT NULL COMMENT '总分',
  `talent_pool_status` int(11) DEFAULT NULL COMMENT '纳入人才库状态（0否，1是）',
  `error_msg` text COMMENT '异常信息',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL,
  `filed_flag` tinyint(1) DEFAULT '0' COMMENT '是否建档(0:未建档 1:已建档)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='个人简历信息表';

CREATE TABLE `tb_analyse_education_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
  `education_level` varchar(100) DEFAULT NULL COMMENT '学历',
  `graduation_school` varchar(100) DEFAULT NULL COMMENT '毕业院校',
  `Introduction` text COMMENT '简介',
  `major` varchar(100) DEFAULT NULL COMMENT '专业',
  `skills` varchar(500) DEFAULT NULL COMMENT '技能',
  `start_time` datetime DEFAULT NULL COMMENT '起止时间',
  `end_time` datetime DEFAULT NULL COMMENT '截至时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='教育信息表';

CREATE TABLE `tb_analyse_family_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
  `member_name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `relationship` varchar(100) DEFAULT NULL COMMENT '关系',
  `workplace` varchar(200) DEFAULT NULL COMMENT '工作单位',
  `position` varchar(20) DEFAULT NULL COMMENT '职务',
  `member_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='家庭信息表';

CREATE TABLE `tb_analyse_work_experience` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
  `company_name` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `resignation_reason` varchar(300) DEFAULT NULL COMMENT '离职原因',
  `work_Introduction` text COMMENT '工作介绍',
  `start_time` datetime DEFAULT NULL COMMENT '起止时间',
  `end_time` datetime DEFAULT NULL COMMENT '截至时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=288 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工作经历表';

CREATE TABLE `tb_analyse_certificate_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `personal_id` bigint(20) NOT NULL COMMENT '基本信息Id',
  `certificate_name` varchar(100) DEFAULT NULL COMMENT '证书/资质名称',
  `file_url` varchar(100) DEFAULT NULL COMMENT '证书文件地址',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='证书信息表';

CREATE TABLE `tb_analyse_personal_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_id` bigint(20) NOT NULL COMMENT '简历分析任务ID',
  `personal_id` bigint(20) DEFAULT NULL COMMENT '基本信息Id',
  `file_name` varchar(100) DEFAULT NULL COMMENT '文件名称',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（单位：字节）',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件地址',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '解析结果（0成功，1解析中，2失败）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='个人档案表';