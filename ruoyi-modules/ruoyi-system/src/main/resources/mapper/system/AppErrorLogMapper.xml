<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AppErrorLogMapper">
    
    <resultMap type="AppErrorLog" id="AppErrorLogResult">
        <result property="id"    column="id"    />
        <result property="appCode"    column="app_code"    />
        <result property="level"    column="level"    />
        <result property="log"    column="log"    />
        <result property="ip"    column="ip"    />
        <result property="userId"    column="user_id"    />
        <result property="logTime"    column="log_time"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
    </resultMap>

    <sql id="selectAppErrorLogVo">
        select a.id, a.app_code, a.level, a.log, a.ip, a.user_id, a.log_time, s.user_name, s.nick_name from app_error_log a left join sys_app_user s on a.user_id = s.user_id
    </sql>

    <select id="selectAppErrorLogList" parameterType="AppErrorLog" resultMap="AppErrorLogResult">
        <include refid="selectAppErrorLogVo"/>
        <where>  
            <if test="appCode != null "> and BINARY a.app_code = #{appCode}</if>
            <if test="level != null "> and a.level = #{level}</if>
            <if test="log != null  and log != ''"> and a.log = #{log}</if>
            <if test="ip != null  and ip != ''"> and a.ip like concat('%', #{ip}, '%')</if>
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                <!-- 开始时间检索 -->
            and date_format(a.log_time,'%y%m%d %H:%i:%s') &gt;= date_format(#{params.beginTime},'%y%m%d %H:%i:%s')
        </if>
            <if test="params.endTime != null and params.endTime != ''">
                <!-- 结束时间检索 -->
                and date_format(a.log_time,'%y%m%d %H:%i:%s') &lt;= date_format(#{params.endTime},'%y%m%d %H:%i:%s')
            </if>
        </where>
        order by a.log_time desc
    </select>
    
    <select id="selectAppErrorLogById" parameterType="Long" resultMap="AppErrorLogResult">
        <include refid="selectAppErrorLogVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO app_error_log(app_code,`level`,log,ip,user_id,log_time)
        VALUES
        <foreach collection="list" close="" index="index" item="item" open="" separator=",">
            (#{item.appCode},#{item.level},#{item.log},#{item.ip},#{item.userId},#{item.logTime})
        </foreach>
    </insert>

    <delete id="deleteAppErrorLogByIds" parameterType="String">
        delete from app_error_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>