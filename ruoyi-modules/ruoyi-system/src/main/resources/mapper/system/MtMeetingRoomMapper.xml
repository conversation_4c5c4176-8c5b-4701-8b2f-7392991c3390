<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MtMeetingRoomMapper">

    <resultMap type="MtMeetingRoom" id="MtMeetingRoomResult">
        <result property="id"    column="id"    />
        <result property="roomName"    column="room_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="companyName" column="dept_name" />
        <result property="volume"    column="volume"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMtMeetingRoomVo">
        select r.id, r.room_name, r.company_id, d.dept_name, r.volume, r.expire_time, r.create_time, r.create_by, r.update_by, r.update_time, r.remark from mt_meeting_room r
        left join sys_dept d on d.dept_id = r.company_id
    </sql>

    <select id="selectMtMeetingRoomList" parameterType="MtMeetingRoom" resultMap="MtMeetingRoomResult">
        <include refid="selectMtMeetingRoomVo"/>
        <where>  
            <if test="roomName != null  and roomName != ''"> and r.room_name like concat('%', #{roomName}, '%')</if>
            <if test="companyId != null "> and r.company_id = #{companyId}</if>
            <if test="volume != null "> and r.volume = #{volume}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(r.expire_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(r.expire_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>

        order by create_time desc
    </select>
    
    <select id="selectMtMeetingRoomById" parameterType="Long" resultMap="MtMeetingRoomResult">
        <include refid="selectMtMeetingRoomVo"/>
        where r.id = #{id}
    </select>
        
    <insert id="insertMtMeetingRoom" parameterType="MtMeetingRoom" useGeneratedKeys="true" keyProperty="id">
        insert into mt_meeting_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name,</if>
            <if test="companyId != null">company_id,</if>
            <if test="volume != null">volume,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">#{roomName},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="volume != null">#{volume},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMtMeetingRoom" parameterType="MtMeetingRoom">
        update mt_meeting_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name = #{roomName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="volume != null">volume = #{volume},</if>
            expire_time = #{expireTime},
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMtMeetingRoomById" parameterType="Long">
        delete from mt_meeting_room where id = #{id}
    </delete>

    <delete id="deleteMtMeetingRoomByIds" parameterType="String">
        delete from mt_meeting_room where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>