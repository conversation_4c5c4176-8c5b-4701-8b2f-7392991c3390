<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysCompanyResConfigMapper">
    
    <resultMap type="SysCompanyResConfig" id="SysCompanyResConfigResult">
        <result property="companyId"    column="company_id"    />
        <result property="resourceType"    column="resource_type"    />
        <result property="resourceLimit"    column="resource_limit"    />
        <result property="status"    column="STATUS"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysCompanyResConfigVo">
        select company_id, resource_type, resource_limit, STATUS, del_flag, update_by, update_time from sys_company_res_config
    </sql>

    <select id="selectSysCompanyResConfigList" parameterType="SysCompanyResConfig" resultMap="SysCompanyResConfigResult">
        <include refid="selectSysCompanyResConfigVo"/>
        <where>  
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="resourceType != null  and resourceType != ''"> and resource_type = #{resourceType}</if>
            <if test="resourceLimit != null "> and resource_limit = #{resourceLimit}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysCompanyResConfigByCompanyId" parameterType="Long" resultMap="SysCompanyResConfigResult">
        <include refid="selectSysCompanyResConfigVo"/>
        where company_id = #{companyId}
    </select>
    <select id="selectCompResConfigDataByType" resultType="com.ruoyi.common.entity.domain.system.SysCompanyResConfig">
        <include refid="selectSysCompanyResConfigVo"/>
        where company_id = #{companyId} and resource_type = #{type}
    </select>

    <insert id="insertSysCompanyResConfig" parameterType="SysCompanyResConfig">
        insert into sys_company_res_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="resourceType != null and resourceType != ''">resource_type,</if>
            <if test="resourceLimit != null">resource_limit,</if>
            <if test="status != null">STATUS,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="resourceType != null and resourceType != ''">#{resourceType},</if>
            <if test="resourceLimit != null">#{resourceLimit},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysCompanyResConfig" parameterType="SysCompanyResConfig">
        update sys_company_res_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="resourceType != null and resourceType != ''">resource_type = #{resourceType},</if>
            <if test="resourceLimit != null">resource_limit = #{resourceLimit},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where company_id = #{companyId}
    </update>

    <delete id="deleteSysCompanyResConfigByCompanyId" parameterType="Long">
        delete from sys_company_res_config where company_id = #{companyId}
    </delete>

    <delete id="deleteSysCompanyResConfigByCompanyIds" parameterType="String">
        delete from sys_company_res_config where company_id in 
        <foreach item="companyId" collection="array" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </delete>
</mapper>