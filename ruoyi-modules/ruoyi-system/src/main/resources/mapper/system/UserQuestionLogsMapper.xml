<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserQuestionLogsMapper">
    
    <resultMap type="UserQuestionLogs" id="UserQuestionLogsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="question"    column="question"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserQuestionLogsVo">
        select id, user_id, question, create_by, create_time from user_question_logs
    </sql>

    <select id="selectUserQuestionLogsList" parameterType="UserQuestionLogs" resultMap="UserQuestionLogsResult">
        <include refid="selectUserQuestionLogsVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="question != null  and question != ''"> and question = #{question}</if>
        </where>
    </select>
    
    <select id="selectUserQuestionLogsById" parameterType="Long" resultMap="UserQuestionLogsResult">
        <include refid="selectUserQuestionLogsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserQuestionLogs" parameterType="UserQuestionLogs" useGeneratedKeys="true" keyProperty="id">
        insert into user_question_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="question != null">question,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="question != null">#{question},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateUserQuestionLogs" parameterType="UserQuestionLogs">
        update user_question_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="question != null">question = #{question},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserQuestionLogsById" parameterType="Long">
        delete from user_question_logs where id = #{id}
    </delete>

    <delete id="deleteUserQuestionLogsByIds" parameterType="String">
        delete from user_question_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>