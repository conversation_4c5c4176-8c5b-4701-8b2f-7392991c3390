<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserQuestionStatMapper">
    
    <resultMap type="UserQuestionStat" id="UserQuestionStatResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="userId"    column="user_id"    />
        <result property="count"    column="count"    />
        <result property="consume"    column="consume"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserQuestionStatVo">
        select id, company_id, user_id, count, create_by, create_time from user_question_stat
    </sql>

    <select id="selectUserQuestionStatList" parameterType="UserQuestionStat" resultMap="UserQuestionStatResult">
        <include refid="selectUserQuestionStatVo"/>
        <where>  
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>
    
    <select id="selectUserQuestionStatById" parameterType="Long" resultMap="UserQuestionStatResult">
        <include refid="selectUserQuestionStatVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserQuestionStat" parameterType="UserQuestionStat" useGeneratedKeys="true" keyProperty="id">
        insert into user_question_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="count != null">count,</if>
            <if test="consume != null">consume,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="count != null">#{count},</if>
            <if test="consume != null">#{consume},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_question_stat (company_id, user_id,count,consume,create_by,create_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.companyId}, #{item.userId},#{item.count},#{item.consume},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateUserQuestionStat" parameterType="UserQuestionStat">
        update user_question_stat
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="count != null">count = #{count},</if>
            <if test="consume != null">count = #{consume},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserQuestionStatById" parameterType="Long">
        delete from user_question_stat where id = #{id}
    </delete>

    <delete id="deleteUserQuestionStatByIds" parameterType="String">
        delete from user_question_stat where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>