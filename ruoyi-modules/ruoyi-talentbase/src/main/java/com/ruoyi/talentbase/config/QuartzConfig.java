package com.ruoyi.talentbase.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * Quartz 配置类
 */
@Slf4j
@Configuration
public class QuartzConfig {

    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void checkQuartzTables() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(null, null, "QRTZ_JOB_DETAILS", new String[]{"TABLE"});
            
            if (tables.next()) {
                log.info("Quartz表已存在，数据库存储配置正常");
            } else {
                log.warn("Quartz表不存在，请确保已执行quartz.sql脚本");
            }
        } catch (Exception e) {
            log.error("检查Quartz表时发生错误", e);
        }
    }

    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        
        // 设置数据源
        schedulerFactoryBean.setDataSource(dataSource);
        
        // 设置自动启动
        schedulerFactoryBean.setAutoStartup(true);
        
        // 设置启动延迟
        schedulerFactoryBean.setStartupDelay(5);
        
        // 设置覆盖已存在的任务
        schedulerFactoryBean.setOverwriteExistingJobs(true);
        
        // 设置应用上下文
        schedulerFactoryBean.setApplicationContextSchedulerContextKey("applicationContext");
        
        // 设置Spring上下文到调度器上下文
        Map<String, Object> schedulerContextMap = new HashMap<>();
        schedulerContextMap.put("applicationContext", applicationContext);
        schedulerFactoryBean.setSchedulerContextAsMap(schedulerContextMap);
        
        log.info("Quartz配置完成，使用数据库存储模式");
        
        return schedulerFactoryBean;
    }
} 