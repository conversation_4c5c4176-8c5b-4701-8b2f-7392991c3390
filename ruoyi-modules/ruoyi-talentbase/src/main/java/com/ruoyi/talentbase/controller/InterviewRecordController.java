package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.InterviewTypeEnum;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.vo.InterviewRecordVO;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewConversationVO;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/interviewRecord")
public class InterviewRecordController extends BaseController {

    @Autowired
    private ITbOnlineInterviewService onlineInterviewService;

    @Autowired
    private ITbOfflineInterviewService offlineInterviewService;

    @Autowired
    private ITbInterviewJobPaperService jobPaperService;

    @Autowired
    private ITbInterviewJobPaperQuestionService jobPaperQuestionService;

    @Autowired
    private ITbOfflineInterviewEvaluationService offlineInterviewEvaluationService;

    @Autowired
    private ITbOnlineInterviewEvaluationService onlineInterviewEvaluationService;

    @Autowired
    private IOfflineInterviewAudioFileService offlineInterviewAudioFileService;

    @Autowired
    private IOfflineInterviewConversationService offlineInterviewConversationService;

    @Autowired
    private ITbOnlineInterviewAnswerService onlineInterviewAnswerService;

    @GetMapping("/list")
    public AjaxResult list(@RequestParam Integer sourceType, @RequestParam Long sourceId) {
        List<InterviewRecordVO> result = new ArrayList<>();

        // 1. 查询线上面试记录
        OnlineInterviewQueryDTO onlineQuery = new OnlineInterviewQueryDTO();
        onlineQuery.setOnlineInterviewSource(sourceType);
        onlineQuery.setOnlineInterviewSourceId(sourceId);
        List<TbOnlineInterview> onlineInterviews = onlineInterviewService.selectTbOnlineInterviewList(onlineQuery);

        // 2. 处理线上面试记录
        for (TbOnlineInterview interview : onlineInterviews) {
            InterviewRecordVO vo = new InterviewRecordVO();
            vo.setInterviewType(InterviewTypeEnum.ONLINE.getCode());
            vo.setInterviewId(interview.getId());
            vo.setInterviewLabel(InterviewTypeEnum.ONLINE.getDesc());
            vo.setInterviewJob(interview.getJobIntention());
            vo.setInterviewTime(interview.getInterviewTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()) : null);
            vo.setInterviewEndTime(interview.getInterviewEndTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewEndTime()) : null);
            vo.setPlayUrl(interview.getPlayUrl());
            vo.setInterviewStatus(interview.getInterviewStatus());
            vo.setInviteCode(interview.getInviteCode());
            vo.setOperation(interview.getOperator());
            vo.setCreateTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
            // 设置排序时间：面试完成状态用面试时间，其他状态用创建时间
            if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus() && interview.getInterviewTime() != null) {
                vo.setSortTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()));
            } else {
                vo.setSortTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
            }

            // 计算线上面试时长
            if (interview.getInterviewTime() != null && interview.getInterviewEndTime() != null) {
                long duration = interview.getInterviewEndTime().getTime() - interview.getInterviewTime().getTime();
                long minutes = duration / (60 * 1000);
                long seconds = (duration / 1000) % 60;
                vo.setInterviewDuration(minutes + "分" + seconds + "秒");
            }

            // 获取面试评价
            TbOnlineInterviewEvaluation evaluationQuery = new TbOnlineInterviewEvaluation();
            evaluationQuery.setOnlineInterviewId(interview.getId());
            List<TbOnlineInterviewEvaluation> evaluations = onlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationList(evaluationQuery);
            if(!evaluations.isEmpty()){
                vo.setOnlineEvaluations(evaluations);
            }

            // 获取面试题目
            if (interview.getJobPaperId() != null) {
                TbInterviewJobPaper interviewJobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
                vo.setInterviewJobPaper(interviewJobPaper);

                TbInterviewJobPaperQuestion questionQuery = new TbInterviewJobPaperQuestion();
                questionQuery.setJobPaperId(interview.getJobPaperId());
                List<TbInterviewJobPaperQuestion> questions = jobPaperQuestionService.selectTbInterviewJobPaperQuestionList(questionQuery);

                // 根据面试ID、试卷ID、题目ID查询对应的答题记录，并写入到题目中
                if (questions != null && !questions.isEmpty()) {
                    for (TbInterviewJobPaperQuestion q : questions) {
                        if (q == null) {
                            continue;
                        }
                        TbOnlineInterviewAnswer interviewAnswer = onlineInterviewAnswerService.selectByInterviewAndQuestion(interview.getId(), interview.getJobPaperId(), q.getId());
                        if (interviewAnswer != null) {
                            q.setAnswer(interviewAnswer.getAnswer());
                        }else{
                            q.setAnswer(null);
                        }
                    }
                }
                vo.setQuestions(questions);
            }

            result.add(vo);
        }

        // 3. 查询线下面试记录
        OfflineInterviewQueryDTO offlineQuery = new OfflineInterviewQueryDTO();
        offlineQuery.setOfflineInterviewSource(sourceType);
        offlineQuery.setOfflineInterviewSourceId(sourceId);
        List<TbOfflineInterview> offlineInterviews = offlineInterviewService.selectTbOfflineInterviewList(offlineQuery);

        // 4. 处理线下面试记录
        for (TbOfflineInterview interview : offlineInterviews) {
            InterviewRecordVO vo = new InterviewRecordVO();
            vo.setInterviewType(InterviewTypeEnum.OFFLINE.getCode());
            vo.setInterviewId(interview.getId());
            vo.setInterviewLabel(InterviewTypeEnum.OFFLINE.getDesc());
            vo.setInterviewRound(interview.getInterviewRound());
            vo.setInterviewJob(interview.getJobIntention());
            vo.setInterviewTime(interview.getInterviewTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()) : null);
            vo.setInterviewEndTime(interview.getInterviewEndTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewEndTime()) : null);
            vo.setInterviewStatus(interview.getInterviewStatus());
            vo.setOperation(interview.getOperator());
            vo.setCreateTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
            // 设置排序时间：面试完成状态用面试时间，其他状态用创建时间
            if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus() && interview.getInterviewTime() != null) {
                vo.setSortTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()));
            } else {
                vo.setSortTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
            }

            // 计算面试时长
            if (interview.getInterviewTime() != null && interview.getInterviewEndTime() != null) {
                long duration = interview.getInterviewEndTime().getTime() - interview.getInterviewTime().getTime();
                long minutes = duration / (60 * 1000);
                long seconds = (duration / 1000) % 60;
                vo.setInterviewDuration(minutes + "分" + seconds + "秒");
            }

            // 获取面试评价
            TbOfflineInterviewEvaluation evaluationQuery = new TbOfflineInterviewEvaluation();
            evaluationQuery.setOfflineInterviewId(interview.getId());
            List<TbOfflineInterviewEvaluation> evaluations = offlineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(evaluationQuery);
            if(!evaluations.isEmpty()){
                vo.setEvaluations(evaluations);
            }

            // 获取音频文件
            OfflineInterviewAudioFile audioFileQuery = new OfflineInterviewAudioFile();
            audioFileQuery.setOfflineInterviewId(interview.getId());
            List<OfflineInterviewAudioFile> audioFiles = offlineInterviewAudioFileService.selectOfflineInterviewAudioFileList(audioFileQuery);
            if (audioFiles != null && !audioFiles.isEmpty()) {
                vo.setAudioFile(audioFiles.get(0));
            }

            // 获取会话记录
            OfflineInterviewConversation conversationQuery = new OfflineInterviewConversation();
            conversationQuery.setOfflineInterviewId(interview.getId());
            List<OfflineInterviewConversationVO> conversations = offlineInterviewConversationService.selectOfflineInterviewConversationVOList(conversationQuery);
            vo.setConversations(conversations);

            result.add(vo);
        }

        // 使用Stream API按面试时间倒序排序
        List<InterviewRecordVO> sortedResult = result.stream()
            .sorted(Comparator.comparing(
                InterviewRecordVO::getSortTime,
                Comparator.nullsLast(String::compareTo)
            ).reversed())
            .collect(Collectors.toList());

        return AjaxResult.success(sortedResult);
    }
} 