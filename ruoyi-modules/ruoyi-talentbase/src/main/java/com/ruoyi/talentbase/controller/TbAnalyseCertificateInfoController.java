package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.TbAnalyseCertificateInfo;
import com.ruoyi.talentbase.service.ITbAnalyseCertificateInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analyseCertificateInfo")
public class TbAnalyseCertificateInfoController extends BaseController {

    @Autowired
    private ITbAnalyseCertificateInfoService service;

    @GetMapping("/list")
    public TableDataInfo list(TbAnalyseCertificateInfo info) {
        startPage();
        List<TbAnalyseCertificateInfo> list = service.selectTbAnalyseCertificateInfoList(info);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectTbAnalyseCertificateInfoById(id));
    }

    @Log(title = "证书信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseCertificateInfo info) {
        return toAjax(service.insertTbAnalyseCertificateInfo(info));
    }

    @Log(title = "证书信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseCertificateInfo info) {
        return toAjax(service.updateTbAnalyseCertificateInfo(info));
    }

    @Log(title = "证书信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbAnalyseCertificateInfoByIds(ids));
    }
} 