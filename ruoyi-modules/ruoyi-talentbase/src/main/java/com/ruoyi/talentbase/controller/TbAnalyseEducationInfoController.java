package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import com.ruoyi.talentbase.service.ITbAnalyseEducationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analyseEducationInfo")
public class TbAnalyseEducationInfoController extends BaseController {

    @Autowired
    private ITbAnalyseEducationInfoService service;

    @GetMapping("/list")
    public TableDataInfo list(TbAnalyseEducationInfo info) {
        startPage();
        List<TbAnalyseEducationInfo> list = service.selectTbAnalyseEducationInfoList(info);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectTbAnalyseEducationInfoById(id));
    }

    @Log(title = "教育信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseEducationInfo info) {
        return toAjax(service.insertTbAnalyseEducationInfo(info));
    }

    @Log(title = "教育信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseEducationInfo info) {
        return toAjax(service.updateTbAnalyseEducationInfo(info));
    }

    @Log(title = "教育信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbAnalyseEducationInfoByIds(ids));
    }
} 