package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;
import com.ruoyi.talentbase.service.ITbAnalyseFamilyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analyseFamilyInfo")
public class TbAnalyseFamilyInfoController extends BaseController {

    @Autowired
    private ITbAnalyseFamilyInfoService service;

    @GetMapping("/list")
    public TableDataInfo list(TbAnalyseFamilyInfo info) {
        startPage();
        List<TbAnalyseFamilyInfo> list = service.selectTbAnalyseFamilyInfoList(info);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectTbAnalyseFamilyInfoById(id));
    }

    @Log(title = "家庭信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseFamilyInfo info) {
        return toAjax(service.insertTbAnalyseFamilyInfo(info));
    }

    @Log(title = "家庭信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseFamilyInfo info) {
        return toAjax(service.updateTbAnalyseFamilyInfo(info));
    }

    @Log(title = "家庭信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbAnalyseFamilyInfoByIds(ids));
    }
} 