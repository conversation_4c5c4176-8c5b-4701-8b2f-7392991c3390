package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbAnalysePersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalSelectedDto;
import com.ruoyi.talentbase.domain.vo.TbAnalysePersonalGetInfoVO;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;
import com.ruoyi.talentbase.service.ITbAnalysePersonalInfoService;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.talentbase.domain.enums.FiledFlagEnum;
import com.ruoyi.talentbase.domain.enums.StorageDisplayEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import com.ruoyi.talentbase.service.ITbAnalyseWorkExperienceService;
import com.ruoyi.talentbase.service.ITbAnalyseEducationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.common.core.utils.StringUtils;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/analysePersonalInfo")
public class TbAnalysePersonalInfoController extends BaseController {

    @Autowired
    private ITbAnalysePersonalInfoService service;

    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;

    @Autowired
    private ITbAnalyseWorkExperienceService analyseWorkExperienceService;

    @Autowired
    private ITbAnalyseEducationInfoService analyseEducationInfoService;

    @GetMapping("/list")
    public TableDataInfo list(TbAnalysePersonalInfo info) {
        startPage();
        List<TbAnalysePersonalInfo> list = service.selectTbAnalysePersonalInfoList(info);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        TbAnalysePersonalGetInfoVO vo = service.selectTbAnalysePersonalGetInfoVoById(id);
        if (vo == null) {
            return AjaxResult.error("未找到简历信息");
        }
        return AjaxResult.success(vo);
    }

    @Log(title = "个人简历信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalysePersonalInfo info) {
        return toAjax(service.insertTbAnalysePersonalInfo(info));
    }

    @Log(title = "个人简历信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalysePersonalInfo info) {
        return toAjax(service.updateTbAnalysePersonalInfo(info));
    }

    @Log(title = "个人简历信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbAnalysePersonalInfoByIds(ids));
    }

    /**
     * 批量入库
     */
    @GetMapping("/batchEditTalentPoolStatus/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchEditTalentPoolStatus(@PathVariable Long[] ids) {
        List<TbAnalysePersonalInfo> infoList = service.selectTbAnalysePersonalInfoList(new TbAnalysePersonalInfo());
        // 过滤出指定ID的记录
        List<TbAnalysePersonalInfo> targetList = new ArrayList<>();
        for (TbAnalysePersonalInfo info : infoList) {
            for (Long id : ids) {
                if (info.getId().equals(id)) {
                    targetList.add(info);
                    break;
                }
            }
        }
        
        if (targetList.isEmpty()) {
            return AjaxResult.error("未找到对应的人才信息");
        }
        
        logger.error("入库开始");
        try {
            int count = tbPersonalInfoService.selectInfoByTaskIds(ids, PersonalIdSourceEnum.RESUME_POOL.getCode());
            if (count > 0) {
                return AjaxResult.error("当前选中的人中,存在已入库的人才信息");
            }
            Map<String, String> education = DictUtils.getDictByValueMap(SysDictDataEnum.EDUCATION.getName());
            for (TbAnalysePersonalInfo info : targetList) {
                TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoByTaskPidAndSource(info.getId(), PersonalIdSourceEnum.RESUME_POOL.getCode());
                if (tbPersonalInfo == null) {
                    addTalentPool(info, education);
                } else {
                    // 更新人才库信息
                    TbPersonalInfo updateData = new TbPersonalInfo();
                    updateData.setTaskPersonalId(info.getId());
                    updateData.setPersonalIdSource(PersonalIdSourceEnum.RESUME_POOL.getCode());
                    updateData.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
                    tbPersonalInfoService.updateByTaskPid(updateData);
                }
                info.setTalentPoolStatus(1);
                service.updateTbAnalysePersonalInfo(info);
            }
            logger.error("入库结束");
        } catch (Exception e) {
            logger.error("入库失败", e);
        }
        return AjaxResult.success();
    }

    /**
     * 新增入库
     */
    public Long addTalentPool(TbAnalysePersonalInfo info, Map<String, String> education) {
        TbPersonalInfoDto dto = new TbPersonalInfoDto();
        BeanUtils.copyProperties(info, dto);
        // 基本信息
        dto.setId(null);
        dto.setTaskPersonalId(info.getId());
        dto.setPersonalIdSource(PersonalIdSourceEnum.RESUME_POOL.getCode());
        dto.setEmploymentStatus("0");
        
        // 教育信息
        TbEducationInfo educationInfo = new TbEducationInfo();
        if (info.getEducation() != null && education != null) {
            educationInfo.setEducationLevel(education.get(info.getEducation()));
        }
        educationInfo.setGraduationSchool(info.getSchoolName());
        educationInfo.setMajor(info.getMajor());
        educationInfo.setIntroduction(info.getIntroduction());
        List<TbEducationInfo> educationInfoList = new ArrayList<>();
        educationInfoList.add(educationInfo);
        
        // 工作经历信息
        List<TbAnalyseWorkExperience> analyseExperiences = analyseWorkExperienceService.selectTbAnalyseWorkExperienceList(new TbAnalyseWorkExperience());
        List<TbWorkExperience> experienceList = new ArrayList<>();
        // 过滤出当前个人信息的工作经历
        for (TbAnalyseWorkExperience analyseExp : analyseExperiences) {
            if (analyseExp.getPersonalId().equals(info.getId())) {
                TbWorkExperience workExperience = new TbWorkExperience();
                BeanUtils.copyProperties(analyseExp, workExperience);
                experienceList.add(workExperience);
            }
        }
        
        dto.setEducationInfoList(educationInfoList);
        dto.setWorkExperienceList(experienceList);
        try {
            tbPersonalInfoService.insertTbPersonalInfoDto(dto);
            return dto.getId();
        } catch (Exception e) {
            String message = e.getMessage();
            logger.error("id信息：" + info.getId() + "入库失败", e);
            info.setErrorMsg(message);
        }
        return null;
    }

    /**
     * 入职
     */
    @GetMapping("/entry/{id}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult entry(@PathVariable Long id) {
        TbAnalysePersonalInfo info = service.selectTbAnalysePersonalInfoById(id);
        if (info == null) {
            return AjaxResult.error("未找到对应的人才信息");
        }
        TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoByTaskPidAndSource(id, PersonalIdSourceEnum.RESUME_POOL.getCode());
        // 如果没有人才库,就先入库
        Map<String, Long> map = new HashMap<>();
        if (tbPersonalInfo == null) {
            Map<String, String> education = DictUtils.getDictByValueMap(SysDictDataEnum.EDUCATION.getName());
            info.setStorageDisplay(1);
            Long pid = addTalentPool(info, education);
            map.put("id", pid);
        } else {
            if (tbPersonalInfo.getFiledFlag().equals(FiledFlagEnum.FILED_FLAG.getCode())) {
                return AjaxResult.error("该人才已经建档,请前往人才档案操作");
            }
            map.put("id", tbPersonalInfo.getId());
        }
        return AjaxResult.success(map);
    }

    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TbAnalysePersonalInfoDto dto) {
        startPage();
        // 查询解析简历信息列表
        TbAnalysePersonalInfo query = new TbAnalysePersonalInfo();
        // 只映射常用字段，如需更多字段可补充
        query.setUserName(dto.getUserName());
        query.setSex(dto.getSex());
        query.setEducation(dto.getEducation());
        query.setFiledFlag(0);
        query.setJobId(dto.getJobId());

        List<TbAnalysePersonalInfo> analyseList = service.selectTbAnalysePersonalInfoList(query);
        List<TbPersonalInfoVo> voList = new ArrayList<>();
        for (TbAnalysePersonalInfo info : analyseList) {
            TbPersonalInfoVo vo = new TbPersonalInfoVo();
            vo.setId(info.getId());
            vo.setTaskPersonalId(info.getId());
            vo.setUserName(info.getUserName());
            // 设置拼音字段，如果没有则生成
            if (StringUtils.isNotEmpty(info.getPinyin())) {
                vo.setPinyin(info.getPinyin());
            } else if (StringUtils.isNotEmpty(info.getUserName())) {
                vo.setPinyin(PinyinUtils.getPinyin(info.getUserName()));
            }
            vo.setSex(info.getSex());
            Map<String, String> sex = DictUtils.getDictByValueMap(SysDictDataEnum.SYS_USER_SEX.getName());
            if (sex != null && info.getSex() != null) {
                vo.setSexName(sex.get(info.getSex()));
            }
            vo.setAge(info.getAge() != null ? info.getAge().toString() : null);
            vo.setFiledFlag(info.getFiledFlag());
            vo.setStorageDisplay(info.getStorageDisplay());
            vo.setYearsOfExperience(info.getYearsOfExperience() != null ? info.getYearsOfExperience().toString() : null);
            vo.setTotalScore(info.getTotalScore());
            vo.setEmploymentStatus("0"); // 解析库默认未入职
            vo.setEducation(info.getEducation());
            Map<String, String> education = DictUtils.getDictByValueMap(SysDictDataEnum.EDUCATION.getName());
            if (education != null && info.getEducation() != null) {
                vo.setEducationName(education.get(info.getEducation()));
            }
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setJobIntent(info.getJobIntent());
            vo.setPersonalIdSource(2); // 2-简历库
            vo.setRecruitmentChannel(info.getRecruitmentChannel());
            // 其它字段可根据需要补充
            voList.add(vo);
        }
        // 处理选中逻辑
        List<TbPersonalSelectedDto> selectedList = dto.getSelectedList();
        if (selectedList != null && selectedList.size() > 0) {
            List<Long> tpInfoIds = selectedList.stream().filter(s -> s.getType() == 0).map(TbPersonalSelectedDto :: getId).collect(Collectors.toList());
            List<Long> ids = new ArrayList<>();
            if (tpInfoIds.size() > 0) {
                Long[] infoIds = tpInfoIds.toArray(new Long[0]);
                List<TbPersonalInfo> infos = tbPersonalInfoService.selectByIds(infoIds);
                ids.addAll(infos.stream().map(TbPersonalInfo :: getTaskPersonalId).collect(Collectors.toList()));
            }
            // 判断是否存在Ids中
            voList.forEach(item -> {
                if (ids.size() > 0 && ids.contains(item.getId())) {
                    item.setSelected(1);
                } else {
                    item.setSelected(0);
                }
            });
        } else {
            voList.forEach(item -> item.setSelected(0));
        }
        return getDataTable(voList);
    }

    /**
     * 批量更新拼音数据
     */
    @PostMapping("/updatePinyin")
    public AjaxResult updatePinyin() {
        try {
            // 查询所有没有拼音的数据
            TbAnalysePersonalInfo query = new TbAnalysePersonalInfo();
            List<TbAnalysePersonalInfo> list = service.selectTbAnalysePersonalInfoList(query);
            
            int updateCount = 0;
            for (TbAnalysePersonalInfo info : list) {
                if (StringUtils.isNotEmpty(info.getUserName()) && StringUtils.isEmpty(info.getPinyin())) {
                    String pinyin = PinyinUtils.getPinyin(info.getUserName());
                    info.setPinyin(pinyin);
                    service.updateTbAnalysePersonalInfo(info);
                    updateCount++;
                }
            }
            
            return AjaxResult.success("成功更新 " + updateCount + " 条记录的拼音数据");
        } catch (Exception e) {
            logger.error("批量更新拼音失败", e);
            return AjaxResult.error("批量更新拼音失败：" + e.getMessage());
        }
    }
} 