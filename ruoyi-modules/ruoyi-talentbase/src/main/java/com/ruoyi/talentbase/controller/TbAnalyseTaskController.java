package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbAnalyseTask;
import com.ruoyi.talentbase.service.ITbAnalyseTaskService;
import com.ruoyi.talentbase.domain.enums.AnalyseTaskStatusEnum;
import com.ruoyi.talentbase.domain.vo.TbAnalyseTaskVO;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 简历分析任务Controller
 */
@RestController
@RequestMapping("/analyseTask")
public class TbAnalyseTaskController extends BaseController {

    @Autowired
    private ITbAnalyseTaskService taskService;

    /**
     * 查询任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AnalyseTaskQueryDTO queryDTO) {
        startPage();
        List<TbAnalyseTask> list = taskService.selectTbAnalyseTaskList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(taskService.selectTbAnalyseTaskById(id));
    }

    /**
     * 新增任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseTask task) {
        task.setUserId(SecurityUtils.getUserId());
        task.setCreateBy(SecurityUtils.getNickname());
        task.setStatus(AnalyseTaskStatusEnum.COMPLETED.getCode());
        int row = taskService.insertTbAnalyseTask(task);
        if (row > 0) {
            // task 对象已回填主键 id
            return AjaxResult.success(task);
        }
        return AjaxResult.error();
    }

    /**
     * 修改任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseTask task) {
        task.setUpdateBy(SecurityUtils.getNickname());
        return toAjax(taskService.updateTbAnalyseTask(task));
    }

    /**
     * 删除任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskService.deleteTbAnalyseTaskByIds(ids));
    }

    /**
     * 根据任务ID复制基础信息（VO）
     */
    @GetMapping("/copyInfo/{id}")
    public AjaxResult copyInfo(@PathVariable Long id) {
        TbAnalyseTask task = taskService.selectTbAnalyseTaskById(id);
        if (task == null) {
            return AjaxResult.error("任务不存在");
        }
        TbAnalyseTaskVO vo = new TbAnalyseTaskVO();
        BeanUtils.copyProperties(task, vo);
        return AjaxResult.success(vo);
    }

    /**
     * 查询简历分析任务下的简历列表（统一接口）
     */
    @GetMapping("/resumeList")
    public TableDataInfo resumeList(ResumeListQueryDTO queryDTO) {
        startPage();
        List<ResumeListVO> list = taskService.selectResumeList(queryDTO);
        return getDataTable(list);
    }
} 