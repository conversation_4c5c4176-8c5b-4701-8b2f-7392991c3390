package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.talentbase.domain.TbContractInfo;
import com.ruoyi.talentbase.service.ITbContractInfoService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "合同信息")
@RestController
@RequestMapping("/contractInfo")
public class TbContractInfoController extends BaseController {

    @Autowired
    private ITbContractInfoService tbContractInfoService;

    @GetMapping("/list")
    public TableDataInfo list(TbContractInfo contractInfo) {
        startPage();
        List<TbContractInfo> list = tbContractInfoService.selectList(contractInfo);
        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tbContractInfoService.selectById(id));
    }

    @PostMapping
    public AjaxResult add(@RequestBody TbContractInfo userInfo) {
        return toAjax(tbContractInfoService.insert(userInfo));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbContractInfo userInfo) {
        return toAjax(tbContractInfoService.update(userInfo));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbContractInfoService.delete(ids));
    }

}
