package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/interviewJobPaperQuestion")
public class TbInterviewJobPaperQuestionController extends BaseController {

    @Autowired
    private ITbInterviewJobPaperQuestionService service;

    @GetMapping("/list")
    public TableDataInfo list(TbInterviewJobPaperQuestion query) {
        startPage();
        List<TbInterviewJobPaperQuestion> list = service.selectTbInterviewJobPaperQuestionList(query);
        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(service.selectTbInterviewJobPaperQuestionById(id));
    }

    @PostMapping
    public AjaxResult add(@RequestBody TbInterviewJobPaperQuestion question) {
        return toAjax(service.insertTbInterviewJobPaperQuestion(question));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbInterviewJobPaperQuestion question) {
        return toAjax(service.updateTbInterviewJobPaperQuestion(question));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbInterviewJobPaperQuestionByIds(ids));
    }
} 