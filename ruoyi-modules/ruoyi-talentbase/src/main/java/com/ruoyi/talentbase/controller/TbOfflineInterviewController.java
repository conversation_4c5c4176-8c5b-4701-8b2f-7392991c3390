package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.agent.AgentApiUtil;
import com.ruoyi.common.core.utils.agent.InterviewEvaluationResponse;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.EvaluationTypeEnum;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewPersonDetailVO;
import com.ruoyi.talentbase.service.ITbOfflineInterviewEvaluationService;
import com.ruoyi.talentbase.service.ITbOfflineInterviewService;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import com.ruoyi.talentbase.service.ITbTaskPersonalInfoService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationSettingService;
import com.ruoyi.talentbase.service.ITbAnalysePersonalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/offlineInterview")
public class TbOfflineInterviewController extends BaseController {

    @Autowired
    private ITbOfflineInterviewService service;

    @Autowired
    private ITbTaskPersonalInfoService taskPersonalInfoService;

    @Autowired
    private ITbPersonalInfoService personalInfoService;

    @Autowired
    private AgentApiUtil agentApiUtil;

    @Autowired
    private ITbOfflineInterviewEvaluationService evaluationService;

    @Autowired
    private IOfflineInterviewConversationService conversationService;

    @Autowired
    private IOfflineInterviewConversationSettingService conversationSettingService;

    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;

    @GetMapping("/list")
    public TableDataInfo list(OfflineInterviewQueryDTO queryDTO) {
        startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TbOfflineInterview> list = service.selectTbOfflineInterviewList(queryDTO);
        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(service.selectTbOfflineInterviewById(id));
    }

    @PostMapping
    public AjaxResult add(@RequestBody TbOfflineInterview interview) {
        if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            // 来源于人才表
            TbPersonalInfo info = personalInfoService.selectTbPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的人才信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
            // 来源于寻才表
            TbTaskPersonalInfo info = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的寻才信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
            // 来源于简历库
            TbAnalysePersonalInfo info = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的简历库信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else {
            return AjaxResult.error("线下面试来源类型不正确");
        }
        
        int rows = service.insertTbOfflineInterview(interview);
        if (rows > 0) {
            return AjaxResult.success("新增成功", interview.getId());
        }
        return AjaxResult.error("新增失败");
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbOfflineInterview interview) {
        return toAjax(service.updateTbOfflineInterview(interview));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbOfflineInterviewByIds(ids));
    }

    @PostMapping("/start/{id}")
    public AjaxResult startInterview(@PathVariable Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }
        
        // 校验面试状态
        if (InterviewStatusEnum.IN_PROGRESS.getCode() == interview.getInterviewStatus()) {
            return AjaxResult.error("面试正在进行中，不能重复开始");
        }
        if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus()) {
            return AjaxResult.error("面试已结束，不能重新开始");
        }
        
        interview.setInterviewTime(DateUtils.getNowDate());
        interview.setInterviewStatus(InterviewStatusEnum.IN_PROGRESS.getCode());
        interview.setOperator(SecurityUtils.getNickname());
        interview.setUpdateBy(SecurityUtils.getUsername());
        interview.setUpdateTime(DateUtils.getNowDate());
        int result = service.updateTbOfflineInterview(interview);
        return toAjax(result);
    }

    @PostMapping("/finish/{id}")
    public AjaxResult finishInterview(@PathVariable Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }
        
        // 先更新面试状态和结束时间
        interview.setInterviewStatus(InterviewStatusEnum.COMPLETED.getCode());
        interview.setInterviewEndTime(DateUtils.getNowDate());
        int result = service.updateTbOfflineInterview(interview);
        
        if (result > 0) {
            // 更新成功后再异步处理AI评价生成
            generateAIEvaluation(interview);
        }
        
        return toAjax(result);
    }

    /**
     * 异步生成AI评价
     */
    @Async
    protected void generateAIEvaluation(TbOfflineInterview interview) {
        try {
            // 获取面试会话内容
            String conversationContent = getConversationContent(interview.getId());
            if (conversationContent == null || conversationContent.trim().isEmpty()) {
                log.warn("面试ID：{} 没有会话内容，无法生成AI评价", interview.getId());
                return;
            }

            InterviewEvaluationResponse evaluation = agentApiUtil.generateEvaluation(conversationContent);
            if (evaluation != null) {
                // 创建评价记录
                TbOfflineInterviewEvaluation evaluationRecord = new TbOfflineInterviewEvaluation();
                evaluationRecord.setOfflineInterviewId(interview.getId());
                evaluationRecord.setEvaluationContent(evaluation.getSummary());
                evaluationRecord.setScore(evaluation.getScore().doubleValue());
                evaluationRecord.setEvaluationType(EvaluationTypeEnum.AI.getCode());
                evaluationRecord.setCreateBy(SecurityUtils.getUsername());
                evaluationRecord.setCreateTime(DateUtils.getNowDate());
                
                // 保存评价记录
                evaluationService.insertTbOfflineInterviewEvaluation(evaluationRecord);
            }
        } catch (Exception e) {
            log.error("生成AI评价失败，面试ID：{}", interview.getId(), e);
        }
    }

    /**
     * 获取合并后的会话内容
     */
    private String getConversationContent(Long interviewId) {
        try {
            // 获取会话内容
            List<OfflineInterviewConversation> conversations = conversationService.selectByOfflineInterviewId(interviewId);
            if (conversations == null || conversations.isEmpty()) {
                return null;
            }

            // 获取会话配置
            List<OfflineInterviewConversationSetting> settings = conversationSettingService.selectByOfflineInterviewId(interviewId);
            if (settings == null || settings.isEmpty()) {
                return null;
            }

            // 将配置转换为Map，方便查找
            Map<Integer, String> roleMap = settings.stream()
                    .collect(Collectors.toMap(
                            OfflineInterviewConversationSetting::getSpeakNum,
                            OfflineInterviewConversationSetting::getSpeakRole
                    ));

            // 合并会话内容
            return conversations.stream()
                    .map(conv -> {
                        String role = roleMap.getOrDefault(conv.getSpeakNum(), "未知");
                        return role + ":" + conv.getSpeakTxt();
                    })
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            log.error("获取会话内容失败，面试ID：{}", interviewId, e);
            return null;
        }
    }

    @GetMapping("/personDetail/{id}")
    public AjaxResult getPersonDetail(@PathVariable("id") Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 更新红点状态为已读
        interview.setAvatarRedDot(0);
        interview.setUpdateBy(SecurityUtils.getUsername());
        interview.setUpdateTime(DateUtils.getNowDate());
        service.updateTbOfflineInterview(interview);

        OfflineInterviewPersonDetailVO vo = new OfflineInterviewPersonDetailVO();
        if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            TbPersonalInfo info = personalInfoService.selectTbPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的人才信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducationName());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
            TbTaskPersonalInfo info = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的寻才信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducationName());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
            // 来源于简历库
            TbAnalysePersonalInfo info = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的简历库信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducation());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else {
            return AjaxResult.error("线下面试来源类型不正确");
        }
        return AjaxResult.success(vo);
    }
} 