package com.ruoyi.talentbase.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbOfflineInterviewEvaluation;
import com.ruoyi.talentbase.service.ITbOfflineInterviewEvaluationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 线下面试评价Controller
 */
@RestController
@RequestMapping("/offlineInterviewEvaluation")
public class TbOfflineInterviewEvaluationController extends BaseController {
    @Autowired
    private ITbOfflineInterviewEvaluationService tbOfflineInterviewEvaluationService;

    /**
     * 查询线下面试评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        startPage();
        List<TbOfflineInterviewEvaluation> list = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出线下面试评价列表
     */
    @Log(title = "线下面试评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        List<TbOfflineInterviewEvaluation> list = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
        ExcelUtil<TbOfflineInterviewEvaluation> util = new ExcelUtil<TbOfflineInterviewEvaluation>(TbOfflineInterviewEvaluation.class);
        util.exportExcel(response, list, "线下面试评价数据");
    }

    /**
     * 获取线下面试评价详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(id));
    }

    /**
     * 新增线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        tbOfflineInterviewEvaluation.setEvaluationType("1");
        tbOfflineInterviewEvaluation.setInterviewerName(SecurityUtils.getNickname());
        tbOfflineInterviewEvaluation.setInterviewerUserName(SecurityUtils.getUsername());
        tbOfflineInterviewEvaluation.setInterviewerUserId(SecurityUtils.getUserId());
        tbOfflineInterviewEvaluation.setCreateTime(DateUtils.getNowDate());
        tbOfflineInterviewEvaluation.setCreateBy(SecurityUtils.getUsername());
        int rows = tbOfflineInterviewEvaluationService.insertTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
        if (rows > 0) {
            return success(tbOfflineInterviewEvaluation);
        }
        return error("新增评价失败");
    }

    /**
     * 修改线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        tbOfflineInterviewEvaluation.setCreateTime(DateUtils.getNowDate());
        tbOfflineInterviewEvaluation.setCreateBy(SecurityUtils.getUsername());
        // 获取当前评价信息
        TbOfflineInterviewEvaluation existingEvaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(tbOfflineInterviewEvaluation.getId());
        if (existingEvaluation == null) {
            return error("评价不存在");
        }

        // 校验是否是当前用户创建的评价
        if (existingEvaluation.getInterviewerUserId() == null ||
                !existingEvaluation.getInterviewerUserId().equals(SecurityUtils.getUserId())) {
            return error("只能修改自己创建的评价");
        }
        existingEvaluation.setOfflineInterviewId(null);

        int rows = tbOfflineInterviewEvaluationService.updateTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
        if (rows > 0) {
            // 查询更新后的数据
            TbOfflineInterviewEvaluation updatedEvaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(tbOfflineInterviewEvaluation.getId());
            return success(updatedEvaluation);
        }
        return error("修改评价失败");
    }

    /**
     * 删除线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbOfflineInterviewEvaluationService.deleteTbOfflineInterviewEvaluationByIds(ids));
    }
} 