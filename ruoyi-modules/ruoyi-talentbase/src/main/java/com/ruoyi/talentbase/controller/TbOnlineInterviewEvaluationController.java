package com.ruoyi.talentbase.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbOnlineInterviewEvaluation;
import com.ruoyi.talentbase.service.ITbOnlineInterviewEvaluationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 线上面试评价Controller
 */
@RestController
@RequestMapping("/onlineInterviewEvaluation")
public class TbOnlineInterviewEvaluationController extends BaseController {
    @Autowired
    private ITbOnlineInterviewEvaluationService tbOnlineInterviewEvaluationService;

    /**
     * 查询线上面试评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        startPage();
        List<TbOnlineInterviewEvaluation> list = tbOnlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationList(tbOnlineInterviewEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出线上面试评价列表
     */
    @Log(title = "线上面试评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        List<TbOnlineInterviewEvaluation> list = tbOnlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationList(tbOnlineInterviewEvaluation);
        ExcelUtil<TbOnlineInterviewEvaluation> util = new ExcelUtil<TbOnlineInterviewEvaluation>(TbOnlineInterviewEvaluation.class);
        util.exportExcel(response, list, "线上面试评价数据");
    }

    /**
     * 获取线上面试评价详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbOnlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationById(id));
    }

    /**
     * 新增线上面试评价
     */
    @Log(title = "线上面试评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        tbOnlineInterviewEvaluation.setEvaluationType("1");
        tbOnlineInterviewEvaluation.setInterviewerName(SecurityUtils.getNickname());
        tbOnlineInterviewEvaluation.setInterviewerUserName(SecurityUtils.getUsername());
        tbOnlineInterviewEvaluation.setInterviewerUserId(SecurityUtils.getUserId());
        tbOnlineInterviewEvaluation.setCreateTime(DateUtils.getNowDate());
        tbOnlineInterviewEvaluation.setCreateBy(SecurityUtils.getUsername());
        int rows = tbOnlineInterviewEvaluationService.insertTbOnlineInterviewEvaluation(tbOnlineInterviewEvaluation);
        if (rows > 0) {
            return success(tbOnlineInterviewEvaluation);
        }
        return error("新增评价失败");
    }

    /**
     * 修改线上面试评价
     */
    @Log(title = "线上面试评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        tbOnlineInterviewEvaluation.setCreateTime(DateUtils.getNowDate());
        tbOnlineInterviewEvaluation.setCreateBy(SecurityUtils.getUsername());
        // 获取当前评价信息
        TbOnlineInterviewEvaluation existingEvaluation = tbOnlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationById(tbOnlineInterviewEvaluation.getId());
        if (existingEvaluation == null) {
            return error("评价不存在");
        }

        // 校验是否是当前用户创建的评价
        if (existingEvaluation.getInterviewerUserId() == null ||
                !existingEvaluation.getInterviewerUserId().equals(SecurityUtils.getUserId())) {
            return error("只能修改自己创建的评价");
        }
        existingEvaluation.setOnlineInterviewId(null);

        int rows = tbOnlineInterviewEvaluationService.updateTbOnlineInterviewEvaluation(tbOnlineInterviewEvaluation);
        if (rows > 0) {
            // 查询更新后的数据
            TbOnlineInterviewEvaluation updatedEvaluation = tbOnlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationById(tbOnlineInterviewEvaluation.getId());
            return success(updatedEvaluation);
        }
        return error("修改评价失败");
    }

    /**
     * 删除线上面试评价
     */
    @Log(title = "线上面试评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbOnlineInterviewEvaluationService.deleteTbOnlineInterviewEvaluationByIds(ids));
    }
} 