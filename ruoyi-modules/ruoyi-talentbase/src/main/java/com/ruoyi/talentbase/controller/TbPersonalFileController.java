package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.file.SysPackZIPFile;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbPersonalFile;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.service.ITbPersonalFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人档案Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/personalFile")
public class TbPersonalFileController extends BaseController {
    @Autowired
    private ITbPersonalFileService tbPersonalFileService;

    /**
     * 查询个人档案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbPersonalFile tbPersonalFile) {
        startPage();
        List<TbPersonalFile> list = tbPersonalFileService.selectTbPersonalFileList(tbPersonalFile);
        return getDataTable(list);
    }

    /**
     * 新增个人档案
     */
    @Log(title = "个人档案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody List<TbPersonalFile> list) {
        String nickname = SecurityUtils.getNickname();
        list.forEach(item -> {
            if (item.getPersonalId() == null) {
                throw new RuntimeException("请求参数异常");
            }
            item.setUploadTime(DateUtils.getNowDate());
            item.setCreateTime(DateUtils.getNowDate());
            item.setFileSource(FileSourceEnum.ADD.getCode());
            item.setCreateBy(nickname);
        });
        boolean rows = tbPersonalFileService.saveBatch(list);
        return toAjax(rows);
    }

    /**
     * 修改个人档案
     */
    @Log(title = "个人档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbPersonalFile tbPersonalFile) {
        return toAjax(tbPersonalFileService.updateTbPersonalFile(tbPersonalFile));
    }

    /**
     * 删除个人档案
     */
    @Log(title = "个人档案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbPersonalFileService.deleteTbPersonalFileByIds(ids));
    }

    /**
     * 获取指定格式文件列表
     */
    @GetMapping("/download/{ids}")
    public AjaxResult download(@PathVariable Long[] ids) {
        List<TbPersonalFile> personalFiles = tbPersonalFileService.selectTbPersonalFileByIds(ids);
        if (personalFiles.isEmpty()) {
            return AjaxResult.error("文件不存在");
        }
        List<SysPackZIPFile> list = personalFiles.stream().filter(s -> s.getFileUrl() != null && !s.getFileUrl().isEmpty()).map(s -> {
            SysPackZIPFile file = new SysPackZIPFile();
            file.setOriginalName(s.getFileUrl());
            file.setNewName(s.getFileName());
            return file;
        }).collect(Collectors.toList());
        return AjaxResult.success(list);
    }
}
