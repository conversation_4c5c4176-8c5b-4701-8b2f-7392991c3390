package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.enums.DelFlagStatus;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.*;
import com.ruoyi.talentbase.domain.enums.FiledFlagEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.enums.StorageDisplayEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;
import com.ruoyi.talentbase.service.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人简历信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/personalInfo")
public class TbPersonalInfoController extends BaseController {
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbOnlineInterviewService tbOnlineInterviewService;
    @Autowired
    private ITbOfflineInterviewService tbOfflineInterviewService;
    @Autowired
    private ITbTaskPersonalInfoService tbTaskPersonalInfoService;
    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;
    @Autowired
    private ITbUserInfoService tbUserInfoService;
    @Autowired
    private ITbUserUndergoService tbUserUndergoService;

    // 手动录入
    private static final String MANUAL_ENTRY = "手动录入";

    /**
     * 查询个人简历信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbPersonalInfo tbPersonalInfo) {
        startPage();
        tbPersonalInfo.setDelFlag(Integer.valueOf(DelFlagStatus.OK.getCode()));
        tbPersonalInfo.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
        List<TbPersonalInfo> list = tbPersonalInfoService.selectTbPersonalInfoList(tbPersonalInfo);
        for (TbPersonalInfo personalInfo : list) {
            int count = 0;
            Long infoId = personalInfo.getId();
            // 查询线上面试次数
            OnlineInterviewQueryDTO queryDTO = new OnlineInterviewQueryDTO();
            queryDTO.setOnlineInterviewSourceId(infoId);
            List<TbOnlineInterview> onlineInterviewList = tbOnlineInterviewService.selectTbOnlineInterviewList(queryDTO);
            // 查询线下面试次数
            OfflineInterviewQueryDTO offlineInterview = new OfflineInterviewQueryDTO();
            offlineInterview.setOfflineInterviewSourceId(infoId);
            List<TbOfflineInterview> offlineInterviewList = tbOfflineInterviewService.selectTbOfflineInterviewList(offlineInterview);
            if (onlineInterviewList != null) {
                count += onlineInterviewList.size();
            }
            if (offlineInterviewList != null) {
                count += offlineInterviewList.size();
            }
            // 写入面试次数
            personalInfo.setInterviewCount(count);
        }
        return getDataTable(list);
    }

    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TbPersonalInfoParamDto infoDto) {
        startPage();
        List<TbPersonalInfoVo> list = tbPersonalInfoService.selectTbPersonalInfoVoList(infoDto);
        List<TbPersonalSelectedDto> selectedList = infoDto.getSelectedList();
        if (selectedList != null && selectedList.size() > 0) {
            List<TbPersonalSelectedDto> dtoList = selectedList.stream().filter(s -> s.getType() != 0).collect(Collectors.toList());
            // 拼接id和type
            List<String> ids = dtoList.stream().map(s -> s.getId() + "_" + s.getType()).collect(Collectors.toList());
            // 判断是否存在dtoList中
            list.forEach(item -> {
                if (ids.contains(item.getTaskPersonalId() + "_" + item.getPersonalIdSource())) {
                    item.setSelected(1);
                } else {
                    item.setSelected(0);
                }
            });
        } else {
            list.forEach(item -> item.setSelected(0));
        }
        return getDataTable(list);
    }

    /**
     * 导出个人简历信息列表
     */
    @Log(title = "个人简历信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbPersonalInfo tbPersonalInfo) {
        List<TbPersonalInfo> list = tbPersonalInfoService.selectTbPersonalInfoList(tbPersonalInfo);
        ExcelUtil<TbPersonalInfo> util = new ExcelUtil<TbPersonalInfo>(TbPersonalInfo.class);
        util.exportExcel(response, list, "个人简历信息数据");
    }

    /**
     * 获取个人简历信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbPersonalGetInfoVo infoVo = tbPersonalInfoService.selectTbPersonalInfoVoById(id);
        return success(infoVo);
    }

    /**
     * 新增个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbPersonalInfoDto infoDto) {
        infoDto.setRecruitmentChannel(MANUAL_ENTRY);
        int row = tbPersonalInfoService.insertTbPersonalInfoDto(infoDto);
        if (row > 0) {
            TbPersonalGetInfoVo getInfoVo = tbPersonalInfoService.selectTbPersonalInfoVoById(infoDto.getId());
            return success(getInfoVo);
        }
        return AjaxResult.error("添加信息失败");
    }

    // 基础信息更新
    @ApiOperation(tags = "个人简历信息", value = "基础信息更新")
    @PostMapping("/editBase")
    public AjaxResult editBase(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editBase(infoDto));
    }

    // 教育信息更新
    @ApiOperation(tags = "个人简历信息", value = "教育信息更新")
    @PostMapping("/editEducation")
    public AjaxResult editEducation(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editEducation(infoDto));
    }

    // 工作信息更新
    @ApiOperation(tags = "个人简历信息", value = "工作信息更新")
    @PostMapping("/editWork")
    public AjaxResult editWork(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editWork(infoDto));
    }

    /**
     * 修改个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbPersonalInfo tbPersonalInfo) {
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(tbPersonalInfo.getId());
        if (personalInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }
        return toAjax(tbPersonalInfoService.updateTbPersonalInfo(tbPersonalInfo));
    }

    /**
     * 删除个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult remove(@PathVariable Long[] ids) {
        for (Long id : ids) {
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
            if (personalInfo == null) {
                throw new RuntimeException("查询失败,未能获取到相关信息");
            }

            // 根据来源类型处理不同的逻辑
            if (personalInfo.getTaskPersonalId() != null && personalInfo.getPersonalIdSource() != null) {
                if (personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.TASK_POOL.getCode())) {
                    // 寻才库来源
                    TbTaskPersonalInfo taskPersonalInfo = new TbTaskPersonalInfo();
                    taskPersonalInfo.setId(personalInfo.getTaskPersonalId());
                    taskPersonalInfo.setTalentPoolStatus(0);
                    tbTaskPersonalInfoService.updateById(taskPersonalInfo);
                } else if (personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.RESUME_POOL.getCode())) {
                    // 简历库来源
                    TbAnalysePersonalInfo analysePersonalInfo = new TbAnalysePersonalInfo();
                    analysePersonalInfo.setId(personalInfo.getTaskPersonalId());
                    analysePersonalInfo.setTalentPoolStatus(0);
                    analysePersonalInfoService.updateTbAnalysePersonalInfo(analysePersonalInfo);
                }
            }
        }
        TbPersonalInfo personalInfo = new TbPersonalInfo();
        personalInfo.setStorageDisplay(1);
        return toAjax(tbPersonalInfoService.updateFlagByIds(personalInfo, ids));
    }

    /**
     * 档案入库
     *
     * @param id
     * @return
     */
    @GetMapping("/addTalentPool/{id}")
    public AjaxResult addTalentPool(@PathVariable Long id) {
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
        if (personalInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }
        personalInfo.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
        personalInfo.setCreateTime(DateUtils.getNowDate());
        tbPersonalInfoService.updateFlagById(personalInfo);
        return AjaxResult.success();
    }

    // 每年年初所有人年岁+1
    @Scheduled(cron = "0 0 0 1 1 ?")
    public void updateAge() {
        // 所有人年龄+1岁
        tbPersonalInfoService.updateAge();
    }


    /**
     * 删除人才库未入库未建档的数据
     * @param isDelInterview 是否删除线下面试和线上面试数据
     * TODO: 待确认在什么时候调用这个方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteData(boolean isDelInterview) {
        try {
            // 未入库未建档的数据
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setStorageDisplay(StorageDisplayEnum.NOT_STORAGE_DISPLAY.getCode());
            personalInfo.setFiledFlag(FiledFlagEnum.NOT_FILED_FLAG.getCode());
            List<TbPersonalInfo> list = tbPersonalInfoService.selectList(personalInfo);
            for (TbPersonalInfo info : list) {
                // 删除人才库相关数据
                tbPersonalInfoService.removeAll(info.getId());
                // 删除档案相关数据
                tbUserInfoService.deleteTbUserInfoByPersonalIds(new Long[]{info.getId()});
                // 删除履历相关数据
                tbUserUndergoService.deleteByPInfoId(info.getId());
                if (isDelInterview) {
                    // 删除线下面试相关数据
                    tbOfflineInterviewService.deleteByPInfoId(info.getId(), TalentSourceEnum.TALENT_POOL);
                    // 删除线上面试相关数据
                    tbOnlineInterviewService.deleteByPInfoId(info.getId(), TalentSourceEnum.TALENT_POOL);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("删除失败");
        }
    }

}
