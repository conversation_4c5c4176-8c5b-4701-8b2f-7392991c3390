package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.talentbase.domain.TbTaskPosting;
import com.ruoyi.talentbase.service.ITbTaskPostingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 查找人才任务Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/posting")
public class TbTaskPostingController extends BaseController {
    @Autowired
    private ITbTaskPostingService tbJobPostingService;

    /**
     * 查询查找人才任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbTaskPosting tbJobPosting) {
        startPage();
        List<TbTaskPosting> list = tbJobPostingService.selectTbTaskPostingList(tbJobPosting);
        return getDataTable(list);
    }

    /**
     * 获取查找人才任务详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        if (tbJobPosting == null) {
            return AjaxResult.error("任务不存在");
        }
        tbJobPosting.makeSearchCondition();
        return AjaxResult.success(tbJobPosting);
    }

    /**
     * 新增查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbTaskPosting tbJobPosting) {
        if (tbJobPosting == null || tbJobPosting.getStatus() == null) {
            return AjaxResult.error("新增失败，请检查参数");
        }
        Integer status = tbJobPosting.getStatus();
        if (status.equals(JobPostingStatus.PROCESSING.getCode())) {
            tbJobPosting.setStartTime(new Date());
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        tbJobPosting.setCreateBy(loginUser.getUser().getNickName());
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        tbJobPosting.setUserId(loginUser.getUserId());
        int row = tbJobPostingService.insertTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }

    /**
     * 修改查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbTaskPosting tbJobPosting) {
        if (!Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.DRAFT.getCode())
                && !Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.PAUSED.getCode())) {
            return AjaxResult.error("任务状态不是草稿和暂停不允许更新");
        }
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        int row = tbJobPostingService.updateTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }

    /**
     * 删除查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbJobPostingService.deleteTbTaskPostingByIds(ids));
    }

    /**
     * 修改查找人才任务状态
     */
    @GetMapping("/editStatus/{id}/{status}")
    public AjaxResult editStatus(@PathVariable Long id, @PathVariable Integer status) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        if (tbJobPosting == null) {
            return AjaxResult.error("当前任务不存在");
        }
        TbTaskPosting jobPosting = new TbTaskPosting();
        jobPosting.setId(id);
        jobPosting.setUpdateTime(new Date());
        jobPosting.setUpdateBy(SecurityUtils.getNickname());
        jobPosting.setStatus(status);
        if (Objects.equals(status, JobPostingStatus.PROCESSING.getCode())) {
            jobPosting.setStartTime(new Date());
        }
        return toAjax(tbJobPostingService.updateTbTaskPosting(jobPosting));
    }

    /**
     * 修改查找人才任务状态
     */
    @GetMapping("/checkJobStatus")
    public AjaxResult checkJobStatus() {
        Integer count = tbJobPostingService.selectTbTaskPostingByUserId(SecurityUtils.getUserId());
        if (count > 0) {
            return AjaxResult.error("当前存有正在进行中的任务");
        }
        return AjaxResult.success();
    }

    /**
     * 重新开始任务
     */
    @GetMapping("/reStartJob")
    public AjaxResult reStartJob(@RequestParam Long id, @RequestParam String taskName) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        if (tbJobPosting == null) {
            return AjaxResult.error("任务不存在");
        }
        tbJobPosting.setTaskName(taskName);
        tbJobPosting.setStatus(1);
        tbJobPosting.setFinishCount(0L);
        tbJobPosting.setId(null);
        tbJobPosting.setCreateBy(SecurityUtils.getNickname());
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        int row = tbJobPostingService.insertTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }


}
