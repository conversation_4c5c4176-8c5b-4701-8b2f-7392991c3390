package com.ruoyi.talentbase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.talentbase.domain.TbPersonalFile;
import com.ruoyi.talentbase.domain.TbUserUndergo;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.service.ITbPersonalFileService;
import com.ruoyi.talentbase.service.ITbUserUndergoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工经历信息Controller
 *
 * <AUTHOR>
 */
@Api(tags = "员工经历信息")
@RestController
@RequestMapping("/undergo")
public class TbUserUndergoController extends BaseController {
    @Autowired
    private ITbUserUndergoService tbUserUndergoService;
    @Autowired
    private ITbPersonalFileService tbPersonalFileService;

    /**
     * 查询员工经历信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbUserUndergo tbUserUndergo) {
        startPage();
        List<TbUserUndergo> list = tbUserUndergoService.selectTbUserUndergoList(tbUserUndergo);
        for (TbUserUndergo undergo : list) {
            Integer fileCode = undergo.getFileCode();
            if (fileCode == null) {
                continue;
            }
            TbPersonalFile personalFile = new TbPersonalFile();
            personalFile.setFileSource(fileCode);
            personalFile.setPersonalId(undergo.getPersonalId());
            personalFile.setUndergoId(undergo.getId());
            List<TbPersonalFile> files = tbPersonalFileService.selectTbPersonalFileList(personalFile);
            if (files != null && !files.isEmpty()) {
                undergo.setPersonalFileList(files);
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取员工经历信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbUserUndergo undergo = tbUserUndergoService.selectTbUserUndergoById(id);
        if (undergo != null && undergo.getFileCode() != null) {
            TbPersonalFile personalFile = new TbPersonalFile();
            personalFile.setFileSource(undergo.getFileCode());
            personalFile.setPersonalId(undergo.getPersonalId());
            personalFile.setUndergoId(undergo.getId());
            List<TbPersonalFile> files = tbPersonalFileService.selectTbPersonalFileList(personalFile);
            if (files != null && !files.isEmpty()) {
                undergo.setPersonalFileList(files);
            }
        }
        return success(undergo);
    }

    /**
     * 新增员工经历信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody TbUserUndergo tbUserUndergo) {
        tbUserUndergo.setFileCode(FileSourceEnum.EXPERIENCE_INFO.getCode());
        int row = tbUserUndergoService.insertTbUserUndergo(tbUserUndergo);
        if (row > 0) {
            List<TbPersonalFile> fileList = tbUserUndergo.getPersonalFileList();
            tbPersonalFileService.insertBatch(fileList, tbUserUndergo.getPersonalId(), tbUserUndergo.getId(), FileSourceEnum.EXPERIENCE_INFO);
        }
        return toAjax(row);
    }

    /**
     * 修改员工经历信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody TbUserUndergo tbUserUndergo) {
        int row = tbUserUndergoService.updateTbUserUndergo(tbUserUndergo);
        if (row > 0) {
            List<TbPersonalFile> fileList = tbUserUndergo.getPersonalFileList();
            tbPersonalFileService.deleteAndInsert(fileList, tbUserUndergo.getPersonalId(), tbUserUndergo.getId(), FileSourceEnum.EXPERIENCE_INFO);
        }
        return toAjax(row);
    }

    /**
     * 删除员工经历信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbUserUndergoService.deleteTbUserUndergoByIds(ids));
    }
}