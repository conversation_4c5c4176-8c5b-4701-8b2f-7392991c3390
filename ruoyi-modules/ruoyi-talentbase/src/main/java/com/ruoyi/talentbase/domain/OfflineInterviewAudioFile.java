package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线下面试会话录音文件对象 tb_offline_interview_audio_file
 */
public class OfflineInterviewAudioFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 线下面试ID */
    @Excel(name = "线下面试ID")
    private Long offlineInterviewId;

    /** 线下面试录音文件地址 */
    @Excel(name = "线下面试录音文件地址")
    private String audioFile;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOfflineInterviewId(Long offlineInterviewId) {
        this.offlineInterviewId = offlineInterviewId;
    }

    public Long getOfflineInterviewId() {
        return offlineInterviewId;
    }

    public void setAudioFile(String audioFile) {
        this.audioFile = audioFile;
    }

    public String getAudioFile() {
        return audioFile;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("offlineInterviewId", getOfflineInterviewId())
                .append("audioFile", getAudioFile())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
} 