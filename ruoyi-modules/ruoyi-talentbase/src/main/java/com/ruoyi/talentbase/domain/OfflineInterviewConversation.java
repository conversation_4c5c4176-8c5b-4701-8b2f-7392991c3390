package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线下面试会话对象 tb_offline_interview_conversation
 */
public class OfflineInterviewConversation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 线下面试ID */
    @Excel(name = "线下面试ID")
    private Long offlineInterviewId;

    /** 发言序号 */
    @Excel(name = "发言序号")
    private Integer speakNum;

    /** 发言内容 */
    @Excel(name = "发言内容")
    private String speakTxt;

    /** 发言时间 */
    @Excel(name = "发言时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String speakTime;

    /** 时间戳 */
    @Excel(name = "时间戳")
    private Long timestamp;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOfflineInterviewId(Long offlineInterviewId) {
        this.offlineInterviewId = offlineInterviewId;
    }

    public Long getOfflineInterviewId() {
        return offlineInterviewId;
    }

    public void setSpeakNum(Integer speakNum) {
        this.speakNum = speakNum;
    }

    public Integer getSpeakNum() {
        return speakNum;
    }

    public void setSpeakTxt(String speakTxt) {
        this.speakTxt = speakTxt;
    }

    public String getSpeakTxt() {
        return speakTxt;
    }

    public void setSpeakTime(String speakTime) {
        this.speakTime = speakTime;
    }

    public String getSpeakTime() {
        return speakTime;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("offlineInterviewId", getOfflineInterviewId())
                .append("speakNum", getSpeakNum())
                .append("speakTxt", getSpeakTxt())
                .append("speakTime", getSpeakTime())
                .append("timestamp", getTimestamp())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
} 