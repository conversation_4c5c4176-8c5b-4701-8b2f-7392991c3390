package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线下面试会话AI分析记录对象 tb_offline_interview_conversation_ai
 */
@Data
public class OfflineInterviewConversationAi extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    private Long offlineInterviewId;

    /** 线下面试会话时间戳 */
    @Excel(name = "线下面试会话时间戳")
    private Long timestamp;

    /** AI分析内容 */
    @Excel(name = "AI分析内容")
    private String aiTxt;
} 