package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线下面试会话配置对象 tb_offline_interview_conversation_setting
 */
public class OfflineInterviewConversationSetting extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 线下面试ID */
    @Excel(name = "线下面试ID")
    private Long offlineInterviewId;

    /** 发言序号 */
    @Excel(name = "发言序号")
    private Integer speakNum;

    /** 发言角色 */
    @Excel(name = "发言角色")
    private String speakRole;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOfflineInterviewId(Long offlineInterviewId) {
        this.offlineInterviewId = offlineInterviewId;
    }

    public Long getOfflineInterviewId() {
        return offlineInterviewId;
    }

    public void setSpeakNum(Integer speakNum) {
        this.speakNum = speakNum;
    }

    public Integer getSpeakNum() {
        return speakNum;
    }

    public void setSpeakRole(String speakRole) {
        this.speakRole = speakRole;
    }

    public String getSpeakRole() {
        return speakRole;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("offlineInterviewId", getOfflineInterviewId())
                .append("speakNum", getSpeakNum())
                .append("speakRole", getSpeakRole())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
} 