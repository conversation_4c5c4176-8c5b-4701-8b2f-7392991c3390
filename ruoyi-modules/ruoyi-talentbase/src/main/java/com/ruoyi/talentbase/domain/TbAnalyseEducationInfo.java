package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 教育信息表 tb_analyse_education_info
 */
@Data
public class TbAnalyseEducationInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long personalId;
    private String educationLevel;
    private String graduationSchool;
    private String introduction;
    private String major;
    private String skills;

    @JsonFormat(pattern = "yyyy-MM")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM")
    private Date endTime;
} 