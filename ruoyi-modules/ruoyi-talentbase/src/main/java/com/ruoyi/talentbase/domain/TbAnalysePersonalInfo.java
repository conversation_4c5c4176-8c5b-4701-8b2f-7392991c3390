package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 个人简历信息表 tb_analyse_personal_info
 */
@Data
public class TbAnalysePersonalInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /** 任务ID */
    private Long jobId;

    private String userName;
    private String pinyin;
    private String sex;
    private Integer age;
    private String idCard;
    private String ethnicity;
    private String marriageStatus;
    private String education;
    private String schoolName;
    private String major;
    private String position;
    private String phone;
    private String skills;
    private String certificate;
    private String email;
    private String avatar;
    private String currentAddress;
    private String politicalStatus;
    private Integer yearsOfExperience;
    private String workStatus;
    private String introduction;
    private String foreignProficiency;
    private String professionalLevel;
    private String jobIntent;
    private String salaryExpectation;
    private String recruitmentChannel;

    private Double minimumEducationScore;
    private Double workExperienceScore;
    private Double jobHoppingRateScore;
    private Double salaryRangeScore;
    private Double totalScore;

    private Integer talentPoolStatus;
    private String errorMsg;
    private Integer filedFlag;

    // 查询时间字段
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 入库展示(入库展示 0:展示 1:不展示)
     */
    @TableField(exist = false)
    private Integer storageDisplay;
} 