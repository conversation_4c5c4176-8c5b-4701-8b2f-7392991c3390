package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 简历分析任务对象 tb_analyse_task
 */
@Data
public class TbAnalyseTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 创建人ID */
    private Long userId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 职位名称 */
    @Excel(name = "职位名称")
    private String positionName;

    /** 职位要求 */
    private String positionRequirement;

    /** 及格分 */
    private Integer passScore = 80;

    /** 学历评分权重 */
    private Integer educationWeight;

    /** 工作经验评分权重 */
    private Integer workExperienceWeight;

    /** 跳槽频率评分权重 */
    private Integer jobHoppingRateWeight;

    /** 薪资范围评分权重 */
    private Integer salaryRangeWeight;

    /** 任务状态：1进行中 2已完成 */
    private Integer status;

    /** 简历总数 */
    private Integer resumeCount;

    /** 简历总数-分析成功 */
    private Integer resumeSuccessCount;

    /** 简历总数-分析失败 */
    private Integer resumeFailCount;

    /** 简历数-优 */
    private Integer resumeExcellentCount;

    /** 简历数-良 */
    private Integer resumeGoodCount;

    /** 备注 */
    private String remark;
} 