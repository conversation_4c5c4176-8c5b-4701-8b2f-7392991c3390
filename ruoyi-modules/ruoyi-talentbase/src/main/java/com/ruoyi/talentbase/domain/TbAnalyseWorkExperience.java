package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 工作经历分析表 tb_analyse_work_experience
 */
@Data
public class TbAnalyseWorkExperience extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long personalId;
    
    private String companyName;
    
    private String position;
    
    private String resignationReason;
    
    private String workIntroduction;
    
    @JsonFormat(pattern = "yyyy-MM")
    private Date startTime;
    
    @JsonFormat(pattern = "yyyy-MM")
    private Date endTime;
} 