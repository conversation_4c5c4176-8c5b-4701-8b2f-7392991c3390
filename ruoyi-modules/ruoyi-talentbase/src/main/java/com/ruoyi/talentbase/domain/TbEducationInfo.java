package com.ruoyi.talentbase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 教育信息对象 tb_education_info
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbEducationInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 学历 */
    @Excel(name = "学历")
    private String educationLevel;

    /** 毕业院校 */
    @Excel(name = "毕业院校")
    private String graduationSchool;

    /** 简介 */
    @Excel(name = "简介")
    private String introduction;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 起止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "起止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;
}
