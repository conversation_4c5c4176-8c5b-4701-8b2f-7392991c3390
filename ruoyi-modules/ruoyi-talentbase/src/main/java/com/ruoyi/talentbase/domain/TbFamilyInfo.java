package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 家庭信息对象 tb_family_info
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbFamilyInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 基本信息Id
     */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String memberName;

    /**
     * 关系
     */
    @Excel(name = "关系")
    private String relationship;

    /**
     * 工作单位
     */
    @Excel(name = "工作单位")
    private String workplace;

    /**
     * 职务
     */
    @Excel(name = "职务")
    private String position;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String memberPhone;
}
