package com.ruoyi.talentbase.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbInterviewJobPaper extends BaseEntity {
    private Long id;
    private String jobName;         // 岗位名称
    private Integer validDays;      // 有效天数
    private String openingRemark;   // 开场话术
    private String remark;          // 备注
} 