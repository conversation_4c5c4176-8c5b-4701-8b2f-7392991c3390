package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonInclude;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbOfflineInterview extends BaseEntity {
    private Long id;
    private String avatarUrl;
    private Integer avatarRedDot;
    private String name;
    private String gender;
    private String education;
    private Integer age;
    private String jobIntention;
    private String talentSource;
    private String interviewRound;
    private String operator;
    private Integer interviewStatus;
    private Date interviewTime;
    private Date interviewEndTime;
    private String remark;
    private Integer offlineInterviewSource; // 线下面试来源（0-人才表，1-寻才表）
    private Long offlineInterviewSourceId; // 线下面试来源ID
} 