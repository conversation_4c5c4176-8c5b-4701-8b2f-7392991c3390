package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线下面试评价对象 tb_offline_interview_evaluation
 */
public class TbOfflineInterviewEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long id;

    /** 线下面试ID */
    private Long offlineInterviewId;

    /** 评价内容 */
    private String evaluationContent;

    /** 评价类型（0-AI 1-面试官） */
    private String evaluationType;

    /** 评分 */
    private Double score;

    /** 面试官姓名 */
    private String interviewerName;

    /** 面试官用户名 */
    private String interviewerUserName;

    /** 面试官ID */
    private Long interviewerUserId;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOfflineInterviewId(Long offlineInterviewId) {
        this.offlineInterviewId = offlineInterviewId;
    }

    public Long getOfflineInterviewId() {
        return offlineInterviewId;
    }

    public void setEvaluationContent(String evaluationContent) {
        this.evaluationContent = evaluationContent;
    }

    public String getEvaluationContent() {
        return evaluationContent;
    }

    public void setEvaluationType(String evaluationType) {
        this.evaluationType = evaluationType;
    }

    public String getEvaluationType() {
        return evaluationType;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Double getScore() {
        return score;
    }

    public void setInterviewerName(String interviewerName) {
        this.interviewerName = interviewerName;
    }

    public String getInterviewerName() {
        return interviewerName;
    }

    public void setInterviewerUserName(String interviewerUserName) {
        this.interviewerUserName = interviewerUserName;
    }

    public String getInterviewerUserName() {
        return interviewerUserName;
    }

    public void setInterviewerUserId(Long interviewerUserId) {
        this.interviewerUserId = interviewerUserId;
    }

    public Long getInterviewerUserId() {
        return interviewerUserId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("offlineInterviewId", getOfflineInterviewId())
                .append("evaluationContent", getEvaluationContent())
                .append("evaluationType", getEvaluationType())
                .append("score", getScore())
                .append("interviewerName", getInterviewerName())
                .append("interviewerUserName", getInterviewerUserName())
                .append("interviewerUserId", getInterviewerUserId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
} 