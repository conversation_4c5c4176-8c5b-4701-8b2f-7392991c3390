package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;
import com.ruoyi.common.core.annotation.Excel;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbOnlineInterview extends BaseEntity {
    private Long id;
    private String avatarUrl;
    private Integer avatarRedDot;
    private String name;
    private String gender;

    /**
     * 性别名称
     */
    private String genderName;

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) {
            return "未知";
        }
        if ("0".equals(gender)) {
            return "男";
        } else if ("1".equals(gender)) {
            return "女";
        } else {
            return "未知";
        }
    }
    private String education;
    private String educationName;
    private Integer age;
    private String jobInterview;
    private String jobIntention;
    private String talentSource;
    private String operator;
    private Integer interviewStatus;
    private Date inviteTime;
    private String inviteUrl;
    private String inviteCode;
    private Long jobPaperId;
    @TableField(exist = false)
    private TbInterviewJobPaper interviewJobPaper;
    private Date interviewTime;
    private Date interviewEndTime; // 面试结束时间
    private String playUrl; // 线上面试记录视频
    private Integer onlineInterviewSource;
    private Long onlineInterviewSourceId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private String inviteExpireTime; // 邀请链接过期时间点
} 