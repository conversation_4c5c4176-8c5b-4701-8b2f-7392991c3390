package com.ruoyi.talentbase.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 线上面试答题记录
 */
@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbOnlineInterviewAnswer extends BaseEntity {
    private Long id;           // 主键ID
    private Long interviewId;  // 线上面试ID
    private Long jobPaperId;   // 岗位试卷ID
    private Long questionId;   // 试题ID
    private String answer;     // 面试者答案
    private String remark;     // 备注
} 