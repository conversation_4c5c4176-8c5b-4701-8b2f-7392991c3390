package com.ruoyi.talentbase.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 线上面试评价对象 tb_online_interview_evaluation
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbOnlineInterviewEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long id;

    /** 线上面试ID */
    @Excel(name = "线上面试ID")
    private Long onlineInterviewId;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String evaluationContent;

    /** 评价类型（0-AI 1-面试官） */
    @Excel(name = "评价类型", readConverterExp = "0=AI,1=面试官")
    private String evaluationType;

    /** 评分 */
    @Excel(name = "评分")
    private Double score;

    /** 面试官姓名 */
    @Excel(name = "面试官姓名")
    private String interviewerName;

    /** 面试官用户名 */
    @Excel(name = "面试官用户名")
    private String interviewerUserName;

    /** 面试官ID */
    @Excel(name = "面试官ID")
    private Long interviewerUserId;
} 