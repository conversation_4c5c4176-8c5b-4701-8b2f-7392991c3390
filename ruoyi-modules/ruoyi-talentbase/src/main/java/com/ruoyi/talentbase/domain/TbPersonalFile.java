package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 个人档案对象 tb_personal_file
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbPersonalFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件大小（单位：字节） */
    @Excel(name = "文件大小", readConverterExp = "单=位：字节")
    private Long fileSize;

    /** 文件地址 */
    @Excel(name = "文件地址")
    private String fileUrl;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    /** 删除标识（0正常，1删除） */
    private Long delFlag;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件来源( 0：基本信息，1：入职信息, 2.离职信息 3:经历信息 4:新增文件)
     */
    private Integer fileSource;

    /**
     * 关联事件
     */
    private Long undergoId;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}
