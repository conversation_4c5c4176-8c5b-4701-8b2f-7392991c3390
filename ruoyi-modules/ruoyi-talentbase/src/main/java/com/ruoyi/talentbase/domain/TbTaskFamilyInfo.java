package com.ruoyi.talentbase.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 tb_task_family_info
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public class TbTaskFamilyInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String memberName;

    /** 关系 */
    @Excel(name = "关系")
    private String relationship;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String workplace;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String memberPhone;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPersonalId(Long personalId) 
    {
        this.personalId = personalId;
    }

    public Long getPersonalId() 
    {
        return personalId;
    }
    public void setMemberName(String memberName) 
    {
        this.memberName = memberName;
    }

    public String getMemberName() 
    {
        return memberName;
    }
    public void setRelationship(String relationship) 
    {
        this.relationship = relationship;
    }

    public String getRelationship() 
    {
        return relationship;
    }
    public void setWorkplace(String workplace) 
    {
        this.workplace = workplace;
    }

    public String getWorkplace() 
    {
        return workplace;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setMemberPhone(String memberPhone) 
    {
        this.memberPhone = memberPhone;
    }

    public String getMemberPhone() 
    {
        return memberPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("personalId", getPersonalId())
            .append("memberName", getMemberName())
            .append("relationship", getRelationship())
            .append("workplace", getWorkplace())
            .append("position", getPosition())
            .append("memberPhone", getMemberPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
