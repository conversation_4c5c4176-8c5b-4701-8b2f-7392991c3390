package com.ruoyi.talentbase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 tb_task_personal_file
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class TbTaskPersonalFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件大小（单位：字节） */
    @Excel(name = "文件大小", readConverterExp = "单=位：字节")
    private Long fileSize;

    /** 文件地址 */
    @Excel(name = "文件地址")
    private String fileUrl;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    /** 删除标识（0正常，1删除） */
    private Long delFlag;
}
