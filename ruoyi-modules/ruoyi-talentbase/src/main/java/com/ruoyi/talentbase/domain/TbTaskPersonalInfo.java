package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

import java.util.List;

/**
 * 【基本信息】对象 tb_task_personal_info
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class TbTaskPersonalInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @Excel(name = "用户性别")
    private String sex;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 民族
     */
    @Excel(name = "民族")
    private String ethnicity;

    /**
     * 婚姻状态
     */
    @Excel(name = "婚姻状态")
    private String marriageStatus;

    /**
     * 职位
     */
    @Excel(name = "职位")
    private String position;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phone;

    /**
     * 用户邮箱
     */
    @Excel(name = "用户邮箱")
    private String email;

    /**
     * 照片地址
     */
    @Excel(name = "照片地址")
    private String avatar;

    /**
     * 当前地址
     */
    @Excel(name = "当前地址")
    private String currentAddress;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /**
     * 个人简介
     */
    @Excel(name = "个人简介")
    private String introduction;

    /**
     * 技能列表
     */
    private String skills;

    /**
     * 外语水平
     */
    @Excel(name = "外语水平")
    private String foreignProficiency;

    /**
     * 专业水平
     */
    @Excel(name = "专业水平")
    private String professionalLevel;

    /**
     * 毕业学历
     */
    private String education;

    /**
     * 求职意向
     */
    @Excel(name = "求职意向")
    private String jobIntent;

    /**
     * 期望薪资
     */
    @Excel(name = "期望薪资")
    private String salaryExpectation;


    /**
     * 招聘渠道
     */
    @Excel(name = "招聘渠道")
    private String recruitmentChannel;

    /**
     * 是否纳入人才库（0未入库 1待入库 2 已入库）
     */
    @Excel(name = "是否纳入人才库")
    private Integer talentPoolStatus;

    /**
     * 相关证书
     */
    private String certificate;

    /**
     * 工作经验
     */
    private Integer yearsOfExperience;
    /**
     * 最低学历分
     */
    private Double minimumEducationScore;

    /**
     * 工作经历分
     */
    private Double workExperienceScore;

    /**
     * 跳槽频率分
     */
    private Double jobHoppingRateScore;

    /**
     * 薪资范围分
     */
    private Double salaryRangeScore;

    /**
     * 总分
     */
    private Double totalScore;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 毕业院校
     */
    private String schoolName;

    /**
     * 所学专业
     */
    private String major;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;

    /**
     * 入库展示(入库展示 0:展示 1:不展示)
     */
    @TableField(exist = false)
    private Integer storageDisplay;
    /**
     * 工作经验
     */
    @TableField(exist = false)
    private List<TbTaskWorkExperience> workExperienceList;

    /**
     * 教育经历
     */
    @TableField(exist = false)
    private List<TbTaskEducationInfo> educationInfoList;

    /**
     * 学历名称
     */
    @TableField(exist = false)
    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String educationName;
    /**
     * 男女
     */
    @TableField(exist = false)
    @Dict(type = SysDictDataEnum.SYS_USER_SEX, field = "sex")
    private String sexName;
    /**
     * 面试次数
     */
    @TableField(exist = false)
    private Integer interviewCount;

}
