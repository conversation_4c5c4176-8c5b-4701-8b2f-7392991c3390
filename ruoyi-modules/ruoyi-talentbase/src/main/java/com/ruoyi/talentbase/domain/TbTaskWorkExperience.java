package com.ruoyi.talentbase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 【工作经历】对象 tb_task_work_experience
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class TbTaskWorkExperience extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String companyName;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 简介 */
    @Excel(name = "简介")
    private String workIntroduction;

    /** 离职原因 */
    @Excel(name = "离职原因")
    private String resignationReason;

    /** 起止时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "起止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;
}
