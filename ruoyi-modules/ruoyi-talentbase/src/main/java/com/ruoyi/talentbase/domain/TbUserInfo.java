package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("tb_user_info")
public class TbUserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id; // 用户ID
    private Long basePersonalId; // 人才库信息ID
    private Long deptId; // 部门ID
    private Long companyId; // 公司ID
    private String depts; // 公司部门路径
    private String position; // 职位
    private String positionLevel; // 职级
    private String insuranceBase; // 五险一金基数
    private String insuranceRatio; // 五险一金比例
    private String salary; // 薪资
    private Integer delFlag; // 删除标志（0代表存在 1代表删除）
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryTime; // 入职时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date turnoverTime; // 预计转正时间
    // 申请离职原因
    private String leaveReason;
    // 申请离职日期
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date leaveTime;
    // 正式离职日期
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date formalLeaveTime;
    // 经历Id
    private Long undergoId;

    // 工作状态
    @TableField(exist = false)
    private String employmentStatus;
    // 合同信息
    @TableField(exist = false)
    private List<TbContractInfo> tbContractInfoList;
    // 相关文件
    @TableField(exist = false)
    private List<TbPersonalFile> personalFileList;
    // 部门信息
    @TableField(exist = false)
    private String deptName;
    // 工作状态
    @TableField(exist = false)
    @Dict(type = SysDictDataEnum.EMPLOYMENT_STATUS, field = "employmentStatus")
    private String employmentStatusName;
    // 入职年限
    @TableField(exist = false)
    private Integer entryYear;

}