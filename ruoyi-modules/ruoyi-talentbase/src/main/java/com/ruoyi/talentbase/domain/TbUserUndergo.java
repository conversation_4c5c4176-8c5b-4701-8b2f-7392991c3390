package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 用户档案信息对象 tb_user_undergo
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_user_undergo")
public class TbUserUndergo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 基本信息Id
     */
    private Long personalId;

    /**
     * 关联文件来源编码
     */
    private Integer fileCode;

    /**
     * 事件名称
     */
    private String affairsName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date operateTime;

    /**
     * 事件说明
     */
    private String remark;
    /**
     * 关联文件
     */
    @TableField(exist = false)
    private List<TbPersonalFile> personalFileList;
}