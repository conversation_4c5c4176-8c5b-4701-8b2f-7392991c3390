package com.ruoyi.talentbase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 工作经历对象 tb_work_experience
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbWorkExperience extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String companyName;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 离职原因 */
    @Excel(name = "离职原因")
    private String resignationReason;

    /** 工作介绍 */
    @Excel(name = "工作介绍")
    private String workIntroduction;

    /** 起止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "起止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;
}
