package com.ruoyi.talentbase.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 简历分析任务列表查询参数 DTO
 */
@Data
public class AnalyseTaskQueryDTO {
    private String taskName;

    private String positionName;

    /** 创建时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createByStart;

    /** 创建时间结束 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createByEnd;
} 