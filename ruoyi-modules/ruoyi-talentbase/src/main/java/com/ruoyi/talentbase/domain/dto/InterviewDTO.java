package com.ruoyi.talentbase.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

import java.util.Date;

/**
 * 线下面试记录
 */
@Data
public class InterviewDTO {
    private Long id;
    private String avatarUrl;
    private String name;
    private String gender;
    
    @JSONField(serialize = false)  // 不序列化此字段
    private String genderName;
    
    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String education;
    private Integer age;
    private String jobInterview;
    private String jobIntention;
    private String talentSource;
    
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")  // 指定日期格式
    private Date inviteTime;
    
    private String inviteUrl;
    private String inviteCode;
    private Integer interviewStatus;

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) {
            return "未知";
        }
        if ("0".equals(gender)) {
            return "男";
        } else if ("1".equals(gender)) {
            return "女";
        } else {
            return "未知";
        }
    }
} 