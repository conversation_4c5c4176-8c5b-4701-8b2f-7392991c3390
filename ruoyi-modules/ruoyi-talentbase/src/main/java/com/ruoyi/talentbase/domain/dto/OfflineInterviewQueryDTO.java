package com.ruoyi.talentbase.domain.dto;

import lombok.Data;

@Data
public class OfflineInterviewQueryDTO {
    private String name;           // 姓名
    private String jobIntention;   // 求职意向
    private Integer interviewStatus; // 面试状态
    private String gender;         // 性别
    private String education;      // 学历
    private Integer ageStart;      // 年龄段-起始
    private Integer ageEnd;        // 年龄段-结束
    private String interviewTimeStart; // 面试时间-起始（建议格式yyyy-MM-dd HH:mm:ss）
    private String interviewTimeEnd;   // 面试时间-结束
    private Integer pageNum;       // 分页-页码
    private Integer pageSize;      // 分页-每页数量
    private Integer offlineInterviewSource; // 线下面试来源（0-人才表，1-寻才表）
    private Long offlineInterviewSourceId; // 线下面试来源ID
} 