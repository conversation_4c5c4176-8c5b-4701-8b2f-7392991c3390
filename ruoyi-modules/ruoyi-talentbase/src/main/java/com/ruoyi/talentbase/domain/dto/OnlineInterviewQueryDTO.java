package com.ruoyi.talentbase.domain.dto;

import lombok.Data;

@Data
public class OnlineInterviewQueryDTO {
    private String name;           // 姓名
    private String jobInterview;   // 求职意向
    private String jobIntention;   // 面试职位
    private Integer interviewStatus; // 面试状态
    private String gender;         // 性别
    private String education;      // 学历
    private Integer ageStart;      // 年龄段-起始
    private Integer ageEnd;        // 年龄段-结束
    private String inviteTimeStart; // 邀请时间-起始
    private String inviteTimeEnd;   // 邀请时间-结束
    private String interviewTimeStart; // 面试时间-起始
    private String interviewTimeEnd;   // 面试时间-结束
    private Integer onlineInterviewSource; // 来源
    private Long onlineInterviewSourceId;  // 来源ID
    private Long jobPaperId; // 线上面试岗位试卷ID
    
    private Integer pageNum;       // 分页-页码
    private Integer pageSize;      // 分页-每页数量
} 