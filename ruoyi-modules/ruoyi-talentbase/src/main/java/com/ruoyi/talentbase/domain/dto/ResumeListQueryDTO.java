package com.ruoyi.talentbase.domain.dto;

import lombok.Data;

/**
 * 简历列表查询参数 DTO
 */
@Data
public class ResumeListQueryDTO {
    
    /** 任务ID */
    private Long taskId;
    
    /** 姓名/文件名 */
    private String nameOrFileName;
    
    /** 性别（0男 1女 2未知） */
    private String sex;
    
    /** 年龄最小值 */
    private Integer ageMin;
    
    /** 年龄最大值 */
    private Integer ageMax;
    
    /** 学历 */
    private String education;
    
    /** 经验要求字典值 */
    private String experienceCode;
    
    /** 求职意向 */
    private String jobIntent;
    
    /** 简历分析状态（0成功，1解析中，2失败） */
    private Integer parseStatus;
    
    /** 排序字段（userName-姓名，yearsOfExperience-工作经验，totalScore-评分，age-年龄） */
    private String sortField;
    
    /** 排序方向（asc-正序，desc-倒序） */
    private String sortOrder;
} 