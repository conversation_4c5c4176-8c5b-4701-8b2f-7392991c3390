package com.ruoyi.talentbase.domain.dto;

import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;
import com.ruoyi.talentbase.domain.TbEducationInfo;
import com.ruoyi.talentbase.domain.TbWorkExperience;
import lombok.Data;

import java.util.List;

/**
 * 个人简历信息（解析任务） DTO
 */
@Data
public class TbAnalysePersonalInfoDto {
    /** 简历文件Id */
    private Long fileId;
    /** 任务Id */
    private Long jobId;

    private String userName;
    private String pinyin;
    private Integer age;
    private String sex;
    private String idCard;
    private String ethnicity;
    private String marriageStatus;
    private String position;
    private String phone;
    private String email;
    private String avatar;
    private String currentAddress;
    private String politicalStatus;
    private String introduction;
    private String foreignProficiency;
    private String professionalLevel;
    private String jobIntent;
    private String salaryExpectation;
    private String education;
    private String schoolName;
    private String major;
    private Integer yearsOfExperience;
    private String workStatus;
    private List<String> skillList;
    private String certificate;

    private List<TbAnalyseWorkExperience> tbWorkExperienceList;
    private List<TbAnalyseEducationInfo> tbEducationInfoList;

    private Double minimumEducationScore;
    private Double workExperienceScore;
    private Double jobHoppingRateScore;
    private Double salaryRangeScore;
    private Double totalScore;

    // 解析失败信息
    private String errorMsg;
    private Integer filedFlag;

    /**
     * 选中列表记录Id
     */
    private List<TbPersonalSelectedDto> selectedList;
} 