package com.ruoyi.talentbase.domain.dto;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TbTaskPersonalInfoParamDto {
    /**
     * 任务Id
     */
    private Long jobId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 年龄下限
     */
    private Integer ageLowerBound;

    /**
     * 年龄上限
     */
    private Integer ageUpperBound;
    /**
     * 用户性别
     */
    private String sex;

    /**
     * 是否纳入人才库（0否 1是）
     */
    private Integer talentPoolStatus;

    /**
     * 学历
     */
    private Integer education;

    /**
     * 排序字段 0评分 1经验 2年龄
     */
    private Integer orderField;

    /**
     * 归档字段
     */
    private Integer filedFlag;

    /**
     * 排序 0升序 1降序
     */
    private Integer sort;

    /**
     * 选中列表记录Id
     */
    private List<TbPersonalSelectedDto> selectedList;

}
