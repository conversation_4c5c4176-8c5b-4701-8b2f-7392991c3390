package com.ruoyi.talentbase.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TbUserInfoDto extends BaseEntity {

    private Long id;
    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String userName;
    /**
     * 工作状态
     */
    @Excel(name = "工作状态")
    private String employmentStatus;
    /**
     * 职位
     */
    @Excel(name = "职位")
    private String position;
    /**
     * 职级
     */
    @Excel(name = "职级")
    private String positionLevel;
    /**
     * 部门Id
     */
    private Long deptId;

    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer delFlag;

    /**
     * 排序 (默认0:合同到期升序，1:合同到期降序)
     */
    private Integer sort;

}
