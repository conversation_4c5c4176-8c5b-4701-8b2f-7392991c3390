package com.ruoyi.talentbase.domain.enums;

/**
 * 简历分析任务状态枚举
 * 1 进行中 2 已完成
 */
public enum AnalyseTaskStatusEnum {
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成");

    private final int code;
    private final String desc;

    AnalyseTaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AnalyseTaskStatusEnum fromCode(int code) {
        for (AnalyseTaskStatusEnum value : AnalyseTaskStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
} 