package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum EmploymentEnum {
    // 入职状态(0:未入职 1:兼职 2:试用 3:实习 4:正式员工 5:离职 6:劳务派遣)
    EMPLOYMENT_STATUS("0", "未入职"),
    PART_TIME("1", "兼职"),
    TRIAL("2", "试用"),
    INTRN("3", "实习"),
    FULL_TIME("4", "正式员工"),
    RESIGN("5", "离职"),
    DISPATCH("6", "劳务派遣");

    private final String code;
    private final String desc;

    public static EmploymentEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (EmploymentEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

}
