package com.ruoyi.talentbase.domain.enums;

/**
 * 评价类型枚举
 */
public enum EvaluationTypeEnum {
    AI("0", "AI评价"),
    INTERVIEWER("1", "面试官评价");

    private final String code;
    private final String info;

    EvaluationTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
} 