package com.ruoyi.talentbase.domain.enums;

/**
 * 工作年限（经验要求）枚举
 */
public enum ExperienceEnum {
    ALL("all", "全部"),
    LT_1("lt_1", "1年以内"),
    ONE_TO_THREE("1_3", "1-3年"),
    THREE_TO_FIVE("3_5", "3-5年"),
    FIVE_TO_TEN("5_10", "5-10年"),
    GT_10("gt_10", "10年以上");

    private final String code;
    private final String desc;

    ExperienceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ExperienceEnum fromCode(String code) {
        for (ExperienceEnum value : ExperienceEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 