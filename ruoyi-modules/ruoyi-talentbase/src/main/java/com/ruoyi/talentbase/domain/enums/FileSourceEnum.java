package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum FileSourceEnum {

    BASIC_INFO(0, "基本信息"),
    ENTRY_INFO(1, "入职信息"),
    LEAVE_INFO(2, "离职信息"),
    EXPERIENCE_INFO(3, "经历信息"),
    ADD(4, "新增文件");

    private final Integer code;
    private final String desc;

    public static FileSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FileSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
