package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FiledFlagEnum {

    // 建档
    FILED_FLAG(1, "已建档"),
    NOT_FILED_FLAG(0, "未建档");


    private final Integer code;
    private final String desc;

    public static FiledFlagEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FiledFlagEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
