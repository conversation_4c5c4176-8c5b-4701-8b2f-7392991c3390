package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 面试类型枚举
 */
@Getter
@AllArgsConstructor
public enum InterviewTypeEnum {
    
    ONLINE(0, "线上面试"),
    OFFLINE(1, "线下面试");

    private final Integer code;
    private final String desc;

    public static InterviewTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InterviewTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
} 