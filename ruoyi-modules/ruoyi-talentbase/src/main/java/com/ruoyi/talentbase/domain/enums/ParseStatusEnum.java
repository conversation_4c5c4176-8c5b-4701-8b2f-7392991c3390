package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 简历文件解析状态枚举
 * 0 成功 1 解析中 2 失败
 */
@Getter
@AllArgsConstructor
public enum ParseStatusEnum {
    SUCCESS(0, "成功"),
    PARSING(1, "解析中"),
    FAIL(2, "失败");

    private final Integer code;
    private final String desc;

    public static ParseStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ParseStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
} 