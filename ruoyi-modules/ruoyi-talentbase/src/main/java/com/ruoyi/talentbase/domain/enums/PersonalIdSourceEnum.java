package com.ruoyi.talentbase.domain.enums;

/**
 * 个人ID来源枚举
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public enum PersonalIdSourceEnum {
    
    /**
     * 寻才库
     */
    TASK_POOL(1, "寻才库"),
    
    /**
     * 简历库
     */
    RESUME_POOL(2, "简历库");
    
    private final Integer code;
    private final String info;
    
    PersonalIdSourceEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PersonalIdSourceEnum getByCode(Integer code) {
        for (PersonalIdSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 