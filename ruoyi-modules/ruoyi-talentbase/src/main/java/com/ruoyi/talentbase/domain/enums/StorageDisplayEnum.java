package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StorageDisplayEnum {

    STORAGE_DISPLAY(0, "入库展示"),
    NOT_STORAGE_DISPLAY(1, "不展示");

    private final Integer code;
    private final String desc;

    public static StorageDisplayEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StorageDisplayEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
