package com.ruoyi.talentbase.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人才来源枚举
 */
@Getter
@AllArgsConstructor
public enum TalentSourceEnum {
    
    TALENT_POOL(0, "人才库"),
    TASK_POOL(1, "寻才库"),
    RESUME_POOL(2, "简历库");

    private final Integer code;
    private final String desc;

    public static TalentSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TalentSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
} 