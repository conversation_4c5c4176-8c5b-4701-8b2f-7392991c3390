package com.ruoyi.talentbase.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.talentbase.domain.*;
import lombok.Data;
import java.util.List;

@Data
public class InterviewRecordVO {
    private Integer interviewType; // 0：线上面试，1：线下面试
    private Long interviewId; // 面试记录id
    private String interviewLabel; // 线上面试 或 线下面试
    private String interviewRound;
    private String interviewJob; // 面试岗位
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String interviewTime; // 面试时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String interviewEndTime; // 面试结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String sortTime; // 排序时间（面试完成状态用面试时间，其他状态用创建时间）
    private Integer interviewStatus; // 面试状态
    private Integer interviewStatusLabel; // 面试状态标签
    private String inviteCode; // 邀请码
    private String operation; // 操作人
    private String interviewDuration; // 面试时长
    private Double interviewScore; // 面试评分
    private String aiEvaluation; // AI面试评价

    // 线上面试
    private List<TbInterviewJobPaperQuestion> questions; // 面试题目
    private String playUrl;
    private TbInterviewJobPaper interviewJobPaper;
    private List<TbOnlineInterviewEvaluation> onlineEvaluations; // 评价记录

    // 线下面试
    private OfflineInterviewAudioFile audioFile; // 音频文件
    private List<TbOfflineInterviewEvaluation> evaluations; // 评价记录
    private List<OfflineInterviewConversationVO> conversations; // 对话记录
}
