package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 线下面试会话视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OfflineInterviewConversationVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 发言角色名称 */
    private String roleName;

    /** 发言内容 */
    private String speakTxt;

    /** 时间戳 */
    private Long timestamp;
} 