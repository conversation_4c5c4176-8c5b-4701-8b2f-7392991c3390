package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;
import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 简历分析-个人信息详情VO
 */
@Data
public class TbAnalysePersonalGetInfoVO {
    /** id */
    private Long id;
    /** 姓名 */
    private String userName;
    /** 性别（0男 1女 2未知） */
    private String sex;
    @Dict(field = "sex", type = SysDictDataEnum.SYS_USER_SEX)
    private String sexName;
    /** 年龄 */
    private Integer age;
    /** 学历 */
    private String education;
    /** 学历 */
    @Dict(field = "education", type = SysDictDataEnum.EDUCATION)
    private String educationName;
    /** 工作经验 */
    private Integer yearsOfExperience;
    /** 职位 */
    private String position;
    /** 手机号码 */
    private String phone;
    /** 邮箱 */
    private String email;
    /** 期望薪资 */
    private String salaryExpectation;
    /** 总分 */
    private BigDecimal totalScore;
    /** 个人简介 */
    private String introduction;

    private Integer filedFlag;

    private Integer talentPoolStatus;
    /** 教育信息 */
    private List<TbAnalyseEducationInfo> educationInfoList;
    /** 家庭信息 */
    private List<TbAnalyseFamilyInfo> familyInfoList;
    /** 工作经历 */
    private List<TbAnalyseWorkExperience> workExperienceList;
} 