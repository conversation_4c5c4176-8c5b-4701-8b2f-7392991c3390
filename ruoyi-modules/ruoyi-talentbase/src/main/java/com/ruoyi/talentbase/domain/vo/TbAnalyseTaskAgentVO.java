package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.util.Map;

/**
 * 查找人才任务对象 tb_task_posting
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
public class TbAnalyseTaskAgentVO {

    /**
     * 学历评分侧重
     */
    private Integer educationWeight;

    /**
     * 工作经验评分侧重
     */
    private Integer workExperienceWeight;

    /**
     * 跳槽频率评分侧重
     */
    private Integer jobHoppingRateWeight;

    /**
     * 薪资范围评分侧重
     */
    private Integer salaryRangeWeight;

    /**
     * 及格分
     */
    private Integer passScore;

    /** 最低学历 */
    private Integer minimumEducation;

    /** 工作经验下限 */
    private Integer experienceLowerBound;

    /** 工作经验上限 */
    private Integer experienceUpperBound;
    /**
     * 跳槽频率年限范围
     */
    private Integer jobHoppingYears;
    /**
     * 跳槽频率次数限制
     */
    private Integer jobHoppingCount;
    /**
     * 薪资范围下限
     */
    private Integer salaryRangeLowerBound;
    /**
     * 薪资范围上限
     */
    private Integer salaryRangeUpperBound;

    /**
     * 附加条件
     */
    private String screeningConditions;

    /**
     * 学历字典
     */
    private Map<String, String> educationMap;

}
