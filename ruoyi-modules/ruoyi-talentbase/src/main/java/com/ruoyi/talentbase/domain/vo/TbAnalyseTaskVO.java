package com.ruoyi.talentbase.domain.vo;

import lombok.Data;

import java.util.Map;

/**
 * 简历分析任务 VO
 */
@Data
public class TbAnalyseTaskVO {
    /** 任务名称 */
    private String taskName;

    /** 职位名称 */
    private String positionName;

    /** 职位要求 */
    private String positionRequirement;

    /** 学历评分权重 */
    private Integer educationWeight;

    /** 工作经验评分权重 */
    private Integer workExperienceWeight;

    /** 跳槽频率评分权重 */
    private Integer jobHoppingRateWeight;

    /** 薪资范围评分权重 */
    private Integer salaryRangeWeight;

    /**
     * 学历字典
     */
    private Map<String, String> educationMap;
} 