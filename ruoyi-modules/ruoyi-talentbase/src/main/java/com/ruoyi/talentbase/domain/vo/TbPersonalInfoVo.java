package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TbPersonalInfoVo {
    /**
     * id
     */
    private Long id;
    /**
     * 任务Id
     */
    private Long taskPersonalId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 姓名拼音
     */
    private String pinyin;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;
    /**
     * 入库展示(入库展示 0:展示 1:不展示)
     */
    private Integer storageDisplay;

    /**
     * 工作经验
     */
    private String yearsOfExperience;

    /**
     * 总分
     */
    private Double totalScore;

    /**
     * 入职状态(0:未入职 1:兼职 2:试用 3:实习 4:正式员工 5:离职 6:劳务派遣)
     */
    private String employmentStatus;

    /**
     * 学历
     */
    private String education;

    /**
     * 期望薪资
     */
    private String salaryExpectation;

    /**
     * 性别
     */
    @Dict(type = SysDictDataEnum.SYS_USER_SEX, field = "sex")
    private String sexName;

    /**
     * 学历
     */
    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String educationName;

    /**
     * 渠道
     */
    private String recruitmentChannel;

    /**
     * 求职意向
     */
    private String jobIntent;

    /**
     * 是否已经选中 0：未选中 1：选中
     */
    private Integer selected;

    /**
     * 类型 1-寻才库，2-简历库
     */
    private Integer personalIdSource;
}
