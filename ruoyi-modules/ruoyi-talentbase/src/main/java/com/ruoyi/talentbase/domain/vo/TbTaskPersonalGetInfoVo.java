package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.talentbase.domain.TbTaskWorkExperience;
import lombok.Data;

import java.util.List;

@Data
public class TbTaskPersonalGetInfoVo {
    /**
     * id
     */
    private Long id;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 是否纳入人才库（0否 1是）
     */
    private Integer talentPoolStatus;
    /**
     * 工作经验
     */
    private String yearsOfExperience;

    /**
     * 总分
     */
    private Double totalScore;
    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 学历
     */
    private String education;

    /**
     * 期望薪资
     */
    private String salaryExpectation;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 求职经历
     */
    private List<TbTaskWorkExperience> workExperience;
}
