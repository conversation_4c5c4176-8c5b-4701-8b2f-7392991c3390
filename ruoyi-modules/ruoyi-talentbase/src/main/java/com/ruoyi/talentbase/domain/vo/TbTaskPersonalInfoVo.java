package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

@Data
public class TbTaskPersonalInfoVo {
    /**
     * id
     */
    private Long id;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 是否纳入人才库（0否 1是）
     */
    private Integer talentPoolStatus;
    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;
    /**
     * 工作经验
     */
    private String yearsOfExperience;

    /**
     * 总分
     */
    private Double totalScore;
    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 学历
     */
    private String education;

    /**
     * 期望薪资
     */
    private String salaryExpectation;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 性别
     */
    @Dict(type = SysDictDataEnum.SYS_USER_SEX, field = "sex")
    private String sexName;

    /**
     * 学历
     */
    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String educationName;

    /**
     * 渠道
     */
    private String recruitmentChannel;

    /**
     *  求职意向
     */
    private String jobIntention;

    /**
     *  是否已经选中 0：未选中 1：选中
     */
    private Integer selected;
}
