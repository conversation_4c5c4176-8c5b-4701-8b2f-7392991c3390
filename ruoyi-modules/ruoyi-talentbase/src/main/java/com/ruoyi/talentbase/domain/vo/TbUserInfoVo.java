package com.ruoyi.talentbase.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TbUserInfoVo {

    private Long id;

    /**
     * 寻才简历Id
     */
    @Excel(name = "寻才简历Id")
    private Long basePersonalId;
    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String userName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 部门Id
     */
    private Long deptId;
    /**
     * 工作状态
     */
    @Excel(name = "工作状态")
    private String employmentStatus;
    /**
     * 工作状态
     */
    @Excel(name = "工作状态")
    @Dict(type = SysDictDataEnum.EMPLOYMENT_STATUS, field = "employmentStatus")
    private String employmentStatusName;
    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    private String deptName;
    /**
     * 职位
     */
    @Excel(name = "职位")
    private String position;
    /**
     * 职级
     */
    @Excel(name = "职级")
    private String positionLevel;
    /**
     * 倒计时
     */
    @Excel(name = "倒计时")
    private Integer countdown;
    /**
     * 修改时间
     */
    @Excel(name = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *  创建档案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createArchivesTime;

}
