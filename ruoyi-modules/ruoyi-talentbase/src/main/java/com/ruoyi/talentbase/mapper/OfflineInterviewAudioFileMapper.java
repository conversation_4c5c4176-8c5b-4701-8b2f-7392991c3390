package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewAudioFile;

/**
 * 线下面试会话录音文件Mapper接口
 */
public interface OfflineInterviewAudioFileMapper {
    /**
     * 查询线下面试会话录音文件列表
     * 
     * @param audioFile 查询条件
     * @return 录音文件列表
     */
    public List<OfflineInterviewAudioFile> selectOfflineInterviewAudioFileList(OfflineInterviewAudioFile audioFile);

    /**
     * 新增线下面试会话录音文件
     * 
     * @param audioFile 线下面试会话录音文件
     * @return 结果
     */
    public int insert(OfflineInterviewAudioFile audioFile);

    /**
     * 根据线下面试ID删除音频文件
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    int deleteByOfflineInterviewId(Long offlineInterviewId);
} 