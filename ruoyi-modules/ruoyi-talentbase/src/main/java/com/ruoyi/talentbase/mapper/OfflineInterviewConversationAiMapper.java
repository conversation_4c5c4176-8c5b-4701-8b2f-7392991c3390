package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationAi;

/**
 * 线下面试会话AI分析记录Mapper接口
 */
public interface OfflineInterviewConversationAiMapper {
    /**
     * 批量新增线下面试会话AI分析记录
     * 
     * @param aiList 线下面试会话AI分析记录列表
     * @return 结果
     */
    public int batchInsert(List<OfflineInterviewConversationAi> aiList);

    /**
     * 根据线下面试ID删除会话AI
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    int deleteByOfflineInterviewId(Long offlineInterviewId);
} 