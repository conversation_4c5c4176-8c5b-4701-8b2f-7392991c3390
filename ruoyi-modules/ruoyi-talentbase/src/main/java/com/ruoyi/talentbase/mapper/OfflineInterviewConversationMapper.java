package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewConversation;

/**
 * 线下面试会话Mapper接口
 */
public interface OfflineInterviewConversationMapper {
    /**
     * 根据面试ID查询会话列表
     * 
     * @param offlineInterviewId 面试ID
     * @return 会话列表
     */
    List<OfflineInterviewConversation> selectByOfflineInterviewId(Long offlineInterviewId);

    /**
     * 批量新增线下面试会话
     * 
     * @param conversationList 会话列表
     * @return 结果
     */
    int batchInsert(List<OfflineInterviewConversation> conversationList);

    /**
     * 根据线下面试ID删除会话
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    int deleteByOfflineInterviewId(Long offlineInterviewId);
} 