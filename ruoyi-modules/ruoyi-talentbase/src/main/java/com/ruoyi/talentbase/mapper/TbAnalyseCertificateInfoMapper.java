package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalyseCertificateInfo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TbAnalyseCertificateInfoMapper {
    TbAnalyseCertificateInfo selectTbAnalyseCertificateInfoById(Long id);

    List<TbAnalyseCertificateInfo> selectTbAnalyseCertificateInfoList(TbAnalyseCertificateInfo info);

    int insertTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info);

    int updateTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info);

    int deleteTbAnalyseCertificateInfoById(Long id);

    int deleteTbAnalyseCertificateInfoByIds(Long[] ids);
} 