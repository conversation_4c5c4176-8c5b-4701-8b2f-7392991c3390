package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TbAnalyseEducationInfoMapper {
    TbAnalyseEducationInfo selectTbAnalyseEducationInfoById(Long id);

    List<TbAnalyseEducationInfo> selectTbAnalyseEducationInfoList(TbAnalyseEducationInfo info);

    int insertTbAnalyseEducationInfo(TbAnalyseEducationInfo info);

    int updateTbAnalyseEducationInfo(TbAnalyseEducationInfo info);

    int deleteTbAnalyseEducationInfoById(Long id);

    int deleteTbAnalyseEducationInfoByIds(Long[] ids);
} 