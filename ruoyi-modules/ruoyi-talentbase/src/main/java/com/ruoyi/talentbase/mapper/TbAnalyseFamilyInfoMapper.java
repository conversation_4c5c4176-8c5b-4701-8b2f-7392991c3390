package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TbAnalyseFamilyInfoMapper {
    TbAnalyseFamilyInfo selectTbAnalyseFamilyInfoById(Long id);

    List<TbAnalyseFamilyInfo> selectTbAnalyseFamilyInfoList(TbAnalyseFamilyInfo info);

    int insertTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info);

    int updateTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info);

    int deleteTbAnalyseFamilyInfoById(Long id);

    int deleteTbAnalyseFamilyInfoByIds(Long[] ids);
} 