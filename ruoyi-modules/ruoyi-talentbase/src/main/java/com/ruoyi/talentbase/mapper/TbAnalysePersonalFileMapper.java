package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalysePersonalFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbAnalysePersonalFileMapper {
    TbAnalysePersonalFile selectTbAnalysePersonalFileById(Long id);

    List<TbAnalysePersonalFile> selectTbAnalysePersonalFileList(TbAnalysePersonalFile file);

    int insertTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int updateTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int deleteTbAnalysePersonalFileById(Long id);

    int deleteTbAnalysePersonalFileByIds(Long[] ids);
    
    /**
     * 批量更新文件状态
     * @param fileIds 文件ID列表
     * @param status 状态
     * @return 更新结果
     */
    int batchUpdateStatus(@Param("fileIds") List<Long> fileIds, @Param("status") Integer status);
} 