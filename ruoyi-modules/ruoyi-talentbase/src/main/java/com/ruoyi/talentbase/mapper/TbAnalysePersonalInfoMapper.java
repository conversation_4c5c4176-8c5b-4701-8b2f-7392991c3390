package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalysePersonalInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbAnalysePersonalInfoMapper {
    TbAnalysePersonalInfo selectTbAnalysePersonalInfoById(Long id);

    List<TbAnalysePersonalInfo> selectTbAnalysePersonalInfoList(TbAnalysePersonalInfo info);

    int insertTbAnalysePersonalInfo(TbAnalysePersonalInfo info);

    int updateTbAnalysePersonalInfo(TbAnalysePersonalInfo info);

    int deleteTbAnalysePersonalInfoById(Long id);

    int deleteTbAnalysePersonalInfoByIds(Long[] ids);
} 