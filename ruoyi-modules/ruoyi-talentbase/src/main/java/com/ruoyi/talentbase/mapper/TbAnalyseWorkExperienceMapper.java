package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbAnalyseWorkExperienceMapper {
    TbAnalyseWorkExperience selectTbAnalyseWorkExperienceById(Long id);

    List<TbAnalyseWorkExperience> selectTbAnalyseWorkExperienceList(TbAnalyseWorkExperience workExperience);

    int insertTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience);

    int insertBatch(List<TbAnalyseWorkExperience> workExperienceList);

    int updateTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience);

    int deleteTbAnalyseWorkExperienceById(Long id);

    int deleteTbAnalyseWorkExperienceByIds(Long[] ids);

    int deleteTbAnalyseWorkExperienceByPersonalId(Long personalId);
} 