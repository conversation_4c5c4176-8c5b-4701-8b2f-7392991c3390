package com.ruoyi.talentbase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.talentbase.domain.TbContractInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface TbContractInfoMapper extends BaseMapper<TbContractInfo> {


    // 查询当前时间和合同到期时间相差多少天
    String SELECT_DAYS_DIFFERENCE = "SELECT DATEDIFF(MAX(end_time), NOW()) AS days_remaining FROM tb_contract_info WHERE user_id = #{userId} and end_time IS NOT NULL";
    @Select(SELECT_DAYS_DIFFERENCE)
    Integer selectEndTimeByUserId(@Param("userId") Long userId);
}
