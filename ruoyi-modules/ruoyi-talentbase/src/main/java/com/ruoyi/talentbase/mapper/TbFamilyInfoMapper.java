package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbFamilyInfo;

/**
 * 家庭信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface TbFamilyInfoMapper 
{
    /**
     * 查询家庭信息
     * 
     * @param id 家庭信息主键
     * @return 家庭信息
     */
    public TbFamilyInfo selectTbFamilyInfoById(Long id);

    /**
     * 查询家庭信息列表
     * 
     * @param tbFamilyInfo 家庭信息
     * @return 家庭信息集合
     */
    public List<TbFamilyInfo> selectTbFamilyInfoList(TbFamilyInfo tbFamilyInfo);

    /**
     * 新增家庭信息
     * 
     * @param tbFamilyInfo 家庭信息
     * @return 结果
     */
    public int insertTbFamilyInfo(TbFamilyInfo tbFamilyInfo);

    /**
     * 修改家庭信息
     * 
     * @param tbFamilyInfo 家庭信息
     * @return 结果
     */
    public int updateTbFamilyInfo(TbFamilyInfo tbFamilyInfo);

    /**
     * 删除家庭信息
     * 
     * @param id 家庭信息主键
     * @return 结果
     */
    public int deleteTbFamilyInfoById(Long id);

    /**
     * 批量删除家庭信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbFamilyInfoByIds(Long[] ids);

    /**
     * 根据个人信息ID删除家庭信息
     *
     * @param personalInfoId 个人信息ID
     * @return 结果
     */
    int deleteTbFamilyInfoByPid(Long personalInfoId);

    int insertBatch(List<TbFamilyInfo> familyInfoList);
}
