package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbInterviewJobPaper;
import java.util.List;

public interface TbInterviewJobPaperMapper {
    TbInterviewJobPaper selectTbInterviewJobPaperById(Long id);
    List<TbInterviewJobPaper> selectTbInterviewJobPaperList(TbInterviewJobPaper paper);
    int insertTbInterviewJobPaper(TbInterviewJobPaper paper);
    int updateTbInterviewJobPaper(TbInterviewJobPaper paper);
    int deleteTbInterviewJobPaperById(Long id);
    int deleteTbInterviewJobPaperByIds(Long[] ids);
} 