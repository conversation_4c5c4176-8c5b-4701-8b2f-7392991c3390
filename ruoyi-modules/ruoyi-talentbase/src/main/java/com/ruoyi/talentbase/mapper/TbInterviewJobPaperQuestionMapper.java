package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface TbInterviewJobPaperQuestionMapper {
    TbInterviewJobPaperQuestion selectTbInterviewJobPaperQuestionById(Long id);
    List<TbInterviewJobPaperQuestion> selectTbInterviewJobPaperQuestionList(TbInterviewJobPaperQuestion question);
    int insertTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question);
    int updateTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question);
    int deleteTbInterviewJobPaperQuestionById(Long id);
    int deleteTbInterviewJobPaperQuestionByIds(Long[] ids);

    int deleteTbInterviewJobPaperQuestionByJobPaperId(Long jobPaperId);

    /**
     * 根据ID列表批量查询问题
     */
    List<TbInterviewJobPaperQuestion> selectByIds(@Param("ids") List<Long> ids);
} 