package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbOfflineInterview;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbOfflineInterviewMapper {
    TbOfflineInterview selectTbOfflineInterviewById(Long id);
    List<TbOfflineInterview> selectTbOfflineInterviewList(OfflineInterviewQueryDTO queryDTO);
    int insertTbOfflineInterview(TbOfflineInterview interview);
    int updateTbOfflineInterview(TbOfflineInterview interview);
    int deleteTbOfflineInterviewById(Long id);
    int deleteTbOfflineInterviewByIds(Long[] ids);

    /**
     * 根据来源信息将面试批量更新为已过期
     * @param param 包含来源类型、来源ID、过期状态码和更新人的对象
     * @return 受影响行数
     */
    int expireOfflineInterviewsBySource(TbOfflineInterview param);

    /**
     * 根据 infoId 和 code 删除面试记录
     * @param infoId 人才信息ID
     * @param code 状态码
     * @return 受影响行数
     */
    int deleteByPInfoId(@Param("infoId") Long infoId, @Param("code") Integer code);
}