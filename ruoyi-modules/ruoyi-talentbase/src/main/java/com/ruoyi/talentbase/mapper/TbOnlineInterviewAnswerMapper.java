package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbOnlineInterviewAnswer;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface TbOnlineInterviewAnswerMapper {

    /**
     * 插入单条答题记录
     */
    @Insert("INSERT INTO tb_online_interview_answer (interview_id, job_paper_id, question_id, answer, create_time) " +
            "VALUES (#{answer.interviewId}, #{answer.jobPaperId}, #{answer.questionId}, #{answer.answer}, NOW())")
    int insertTbOnlineInterviewAnswer(@Param("answer") TbOnlineInterviewAnswer answer);

    /**
     * 根据面试记录获取答题记录
     */
    @Select("SELECT id, interview_id AS interviewId, job_paper_id AS jobPaperId, question_id AS questionId, answer, remark, create_time AS createTime FROM tb_online_interview_answer WHERE interview_id = #{interviewId} AND job_paper_id = #{jobPaperId} AND question_id = #{questionId} LIMIT 1")
    TbOnlineInterviewAnswer selectByInterviewAndQuestion(@Param("interviewId") Long interviewId,
                                                        @Param("jobPaperId") Long jobPaperId,
                                                        @Param("questionId") Long questionId);

    /**
     * 根据面试ID查询所有答案
     */
    @Select("SELECT id, interview_id AS interviewId, job_paper_id AS jobPaperId, question_id AS questionId, answer, remark, create_time AS createTime FROM tb_online_interview_answer WHERE interview_id = #{interviewId}")
    List<TbOnlineInterviewAnswer> selectByInterviewId(@Param("interviewId") Long interviewId);

    // 可根据需要扩展批量插入方法
} 