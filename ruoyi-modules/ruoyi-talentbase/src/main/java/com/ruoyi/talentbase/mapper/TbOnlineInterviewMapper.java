package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface TbOnlineInterviewMapper {
    TbOnlineInterview selectTbOnlineInterviewById(Long id);
    List<TbOnlineInterview> selectTbOnlineInterviewList(OnlineInterviewQueryDTO queryDTO);
    int insertTbOnlineInterview(TbOnlineInterview interview);
    int updateTbOnlineInterview(TbOnlineInterview interview);
    int deleteTbOnlineInterviewById(Long id);
    int deleteTbOnlineInterviewByIds(Long[] ids);

    /**
     * 根据 inviteUrl 列表批量查询面试记录
     */
    List<TbOnlineInterview> selectByInviteUrls(@Param("urls") Collection<String> urls);

    /**
     * 根据来源类型和来源ID批量过期面试
     */
    int expireOnlineInterviewsBySource(TbOnlineInterview interview);

    int deleteByPInfoId(@Param("infoId") Long infoId, @Param("sourceType") Integer sourceType);
}