package com.ruoyi.talentbase.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.talentbase.domain.TbPersonalFile;

/**
 * 个人档案Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface TbPersonalFileMapper extends BaseMapper<TbPersonalFile>
{
    /**
     * 查询个人档案
     * 
     * @param id 个人档案主键
     * @return 个人档案
     */
    public TbPersonalFile selectTbPersonalFileById(Long id);

    /**
     * 查询个人档案列表
     * 
     * @param tbPersonalFile 个人档案
     * @return 个人档案集合
     */
    public List<TbPersonalFile> selectTbPersonalFileList(TbPersonalFile tbPersonalFile);

    /**
     * 批量新增个人档案
     *
     * @return 结果
     */
    int insertBatch(List<TbPersonalFile> personalFileList);
}
