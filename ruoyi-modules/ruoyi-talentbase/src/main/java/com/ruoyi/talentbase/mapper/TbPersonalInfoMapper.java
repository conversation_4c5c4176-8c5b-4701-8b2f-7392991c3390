package com.ruoyi.talentbase.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;

/**
 * 个人简历信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface TbPersonalInfoMapper extends BaseMapper<TbPersonalInfo>
{

    /**
     * 查询个人简历信息列表
     * 
     * @param tbPersonalInfo 个人简历信息
     * @return 个人简历信息集合
     */
    public List<TbPersonalInfo> selectTbPersonalInfoList(TbPersonalInfo tbPersonalInfo);

    /**
     * 删除个人简历信息
     * 
     * @param id 个人简历信息主键
     * @return 结果
     */
    public int deleteTbPersonalInfoById(Long id);

    /**
     * 批量删除个人简历信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbPersonalInfoByIds(Long[] ids);

    /**
     * 根据信息查询个人简历信息
     */
    List<TbPersonalInfoVo> selectTbPersonalInfoVoList(TbPersonalInfoParamDto infoDto);
}
