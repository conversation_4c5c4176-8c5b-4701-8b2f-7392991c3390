package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbTaskEducationInfo;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface TbTaskEducationInfoMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskEducationInfo selectTbTaskEducationInfoById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskEducationInfo> selectTbTaskEducationInfoList(TbTaskEducationInfo tbEducationInfo);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskEducationInfo(TbTaskEducationInfo tbEducationInfo);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskEducationInfo(TbTaskEducationInfo tbEducationInfo);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbTaskEducationInfoById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTaskEducationInfoByIds(Long[] ids);
}
