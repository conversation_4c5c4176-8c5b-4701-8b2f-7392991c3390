package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbTaskPersonalFile;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface TbTaskPersonalFileMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskPersonalFile selectTbTaskPersonalFileById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskPersonalFile> selectTbTaskPersonalFileList(TbTaskPersonalFile tbPersonalFile);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskPersonalFile(TbTaskPersonalFile tbPersonalFile);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskPersonalFile(TbTaskPersonalFile tbPersonalFile);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbTaskPersonalFileById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTaskPersonalFileByIds(Long[] ids);
}
