package com.ruoyi.talentbase.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.talentbase.domain.TbTaskPersonalInfo;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.vo.TbTaskPersonalInfoVo;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface TbTaskPersonalInfoMapper extends BaseMapper<TbTaskPersonalInfo>
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskPersonalInfo selectTbTaskPersonalInfoById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskPersonalInfo> selectTbTaskPersonalInfoList(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbTaskPersonalInfoById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTaskPersonalInfoByIds(Long[] ids);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    List<TbTaskPersonalInfoVo> selectTbTaskPersonalInfoVoList(TbTaskPersonalInfoParamDto tbPersonalInfo);

    /**
     * 修改简历入人才库状态
     * @param ids
     * @return
     */
    int batchEditTalentPoolStatus(Long[] ids);
}
