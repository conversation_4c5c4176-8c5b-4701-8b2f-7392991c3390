package com.ruoyi.talentbase.mapper;

import com.ruoyi.talentbase.domain.TbTaskPosting;

import java.util.List;

/**
 * 查找人才任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface TbTaskPostingMapper {
    /**
     * 查询查找人才任务
     *
     * @param id 查找人才任务主键
     * @return 查找人才任务
     */
    public TbTaskPosting selectTbTaskPostingById(Long id);

    /**
     * 查询查找人才任务列表
     *
     * @param tbJobPosting 查找人才任务
     * @return 查找人才任务集合
     */
    public List<TbTaskPosting> selectTbTaskPostingList(TbTaskPosting tbJobPosting);

    /**
     * 新增查找人才任务
     *
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    public int insertTbTaskPosting(TbTaskPosting tbJobPosting);

    /**
     * 修改查找人才任务
     *
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    public int updateTbTaskPosting(TbTaskPosting tbJobPosting);

    /**
     * 删除查找人才任务
     *
     * @param id 查找人才任务主键
     * @return 结果
     */
    public int deleteTbTaskPostingById(Long id);

    /**
     * 批量删除查找人才任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTaskPostingByIds(Long[] ids);

    /**
     * 查询人才任务数量
     *
     * @param userId
     * @return
     */
    Integer selectTbTaskPostingByUserId(Long userId);
}
