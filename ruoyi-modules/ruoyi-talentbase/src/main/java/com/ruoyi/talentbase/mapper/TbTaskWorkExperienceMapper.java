package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbTaskWorkExperience;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface TbTaskWorkExperienceMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskWorkExperience selectTbTaskWorkExperienceById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskWorkExperience> selectTbTaskWorkExperienceList(TbTaskWorkExperience tbWorkExperience);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskWorkExperience(TbTaskWorkExperience tbWorkExperience);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskWorkExperience(TbTaskWorkExperience tbWorkExperience);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbTaskWorkExperienceById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTaskWorkExperienceByIds(Long[] ids);

    /**
     * 根据个人id查询
     * @param pId
     * @return
     */
    List<TbTaskWorkExperience> selectTbTaskWorkExperienceByPersonalId(Long pId);
}
