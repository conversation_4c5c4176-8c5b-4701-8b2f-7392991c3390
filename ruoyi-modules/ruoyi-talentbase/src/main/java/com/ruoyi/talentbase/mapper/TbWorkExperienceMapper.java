package com.ruoyi.talentbase.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.talentbase.domain.TbWorkExperience;

/**
 * 工作经历Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface TbWorkExperienceMapper extends BaseMapper<TbWorkExperience>
{
    /**
     * 查询工作经历
     * 
     * @param id 工作经历主键
     * @return 工作经历
     */
    public TbWorkExperience selectTbWorkExperienceById(Long id);

    /**
     * 查询工作经历列表
     * 
     * @param tbWorkExperience 工作经历
     * @return 工作经历集合
     */
    public List<TbWorkExperience> selectTbWorkExperienceList(TbWorkExperience tbWorkExperience);

    /**
     * 新增工作经历
     * 
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    public int insertTbWorkExperience(TbWorkExperience tbWorkExperience);

    /**
     * 修改工作经历
     * 
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    public int updateTbWorkExperience(TbWorkExperience tbWorkExperience);

    /**
     * 删除工作经历
     * 
     * @param id 工作经历主键
     * @return 结果
     */
    public int deleteTbWorkExperienceById(Long id);

    /**
     * 批量删除工作经历
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbWorkExperienceByIds(Long[] ids);

    int insertBatch(List<TbWorkExperience> taskPersonalInfoList);

}
