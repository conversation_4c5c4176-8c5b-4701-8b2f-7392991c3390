package com.ruoyi.talentbase.python;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * /interview/getInterviews 接口 item
 */
@Data
public class InterviewInfoItem {
    private Long id;

    @JSONField(name = "interview_id")
    private String interviewId;

    private String text;

    private Integer status;

    @JSONField(name = "start_time")
    private Date startTime;

    @JSONField(name = "end_time")
    private Date endTime;

    private String playUrl;
} 