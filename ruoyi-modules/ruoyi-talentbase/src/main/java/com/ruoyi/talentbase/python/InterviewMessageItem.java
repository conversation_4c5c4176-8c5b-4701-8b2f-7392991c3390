package com.ruoyi.talentbase.python;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 接收 Python 服务 /interview/getInterviewMessages 返回的单条面试答题记录
 */
@Data
public class InterviewMessageItem {

    private Long id;

    /** 线上面试唯一标识（inviteUrl） */
    @JSONField(name = "interview_id")
    private String interviewId;

    /** 提问序号 */
    @JSONField(name = "ask_num")
    private Integer askNum;

    /** 问题内容 */
    @JSONField(name = "ask_content")
    private String askContent;

    /** 面试者回答内容 */
    @JSONField(name = "answer_content")
    private String answerContent;

    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    @JSONField(name = "update_time")
    private LocalDateTime updateTime;
} 