package com.ruoyi.talentbase.python;

import com.alibaba.fastjson.JSON;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewSyncDTO;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Python服务API工具类
 * 用于与外网Python服务进行交互
 */
@Component
public class PythonApiUtil {
    private static final Logger log = LoggerFactory.getLogger(PythonApiUtil.class);

    @Value("${python.base-url}")
    private String baseUrl;

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .build();

    /**
     * 提交OnlineInterviewSyncDTO列表到外网服务
     * @param syncList 需要同步的面试数据列表
     * @return 外网服务返回内容
     */
    public String submitOnlineInterviewSyncList(List<OnlineInterviewSyncDTO> syncList) {
        String url = baseUrl + "/interview/addInterviews";
        try {
            InterviewSyncRequest requestBody = new InterviewSyncRequest();
            List<InterviewSyncItem> items = new java.util.ArrayList<>();
            for (OnlineInterviewSyncDTO dto : syncList) {
                InterviewSyncItem item = new InterviewSyncItem();
                String inviteUrl = dto.getInterview().getInviteUrl();
                item.setInterview_id(StringUtils.isBlank(inviteUrl) ? null : inviteUrl);
                item.setText(JSON.toJSONString(dto));
                items.add(item);
            }
            requestBody.setInterviews(items);
            String json = JSON.toJSONString(requestBody);
            log.info("提交面试同步数据到外网: {}，参数: {}", url, json);
            Request request = new Request.Builder()
                    .url(url)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(MediaType.parse("application/json"), json))
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return response.body().string();
                } else {
                    log.error("同步面试数据失败，响应码: {}", response.code());
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("同步面试数据到外网异常", e);
            return null;
        }
    }

    /**
     * 根据线上面试ID(支持多个以逗号分隔)获取面试结果信息
     * 接口地址：/interview/getInterviewMessages/{interview_ids}
     *
     * @param interviewIds 线上面试记录ID数组
     * @return 面试结果信息（json字符串）
     */
    public List<InterviewMessageItem> getOnlineInterviewResult(String[] interviewIds) {
        if (interviewIds == null || interviewIds.length == 0) {
            return null;
        }

        String idsStr = String.join(",", Arrays.stream(interviewIds)
                .map(String::valueOf)
                .toArray(String[]::new));

        String url = baseUrl + "/interview/getInterviewMessages/" + idsStr;
        try {
            log.info("查询线上面试结果: {}", url);
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .header("Content-Type", "application/json")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String bodyStr = response.body().string();
                    InterviewMessageResponse resp = JSON.parseObject(bodyStr, InterviewMessageResponse.class);
                    if (resp != null && resp.getCode() != null && (resp.getCode() == 0 || resp.getCode() == 200)) {
                        return resp.getData();
                    }
                } else {
                    log.error("查询面试结果失败，响应码: {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("查询线上面试结果异常", e);
        }
        return null;
    }

    /**
     * 删除外网面试记录
     * @param interviewIds 面试记录ID数组
     * @return 删除结果
     */
    public boolean deleteOnlineInterviews(String[] interviewIds) {
        if (interviewIds == null || interviewIds.length == 0) {
            return false;
        }
        
        // 将ID数组转换为逗号分隔的字符串
        String idsStr = String.join(",", Arrays.stream(interviewIds)
                .map(String::valueOf)
                .toArray(String[]::new));
                
        String url = baseUrl + "/interview/deleteInterviews/" + idsStr;
        try {
            log.info("删除外网面试记录: {}", url);
            Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(MediaType.parse("application/json"), ""))  // 使用POST方法，空请求体
                    .header("Content-Type", "application/json")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.info("删除外网面试记录成功");
                    return true;
                } else {
                    log.error("删除外网面试记录失败，响应码: {}", response.code());
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("删除外网面试记录异常", e);
            return false;
        }
    }

    /**
     * 根据线上面试 inviteUrl(支持多个以逗号分隔)获取面试基础信息
     * 接口地址：/interview/getInterviews/{interview_ids}
     * 目前暂返回原始 JSON 字符串，调用方可自行解析。
     *
     * @param interviewIds inviteUrl 数组
     * @return JSON 字符串，失败返回 null
     */
    public List<InterviewInfoItem> getOnlineInterviews(String[] interviewIds) {
        if (interviewIds == null || interviewIds.length == 0) {
            return null;
        }

        String idsStr = String.join(",", Arrays.stream(interviewIds)
                .map(String::valueOf)
                .toArray(String[]::new));

        String url = baseUrl + "/interview/getInterviewsByIds/" + idsStr;
        try {
            log.info("查询线上面试基础信息: {}", url);
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .header("Content-Type", "application/json")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String bodyStr = response.body().string();
                    InterviewInfoResponse resp = JSON.parseObject(bodyStr, InterviewInfoResponse.class);
                    if (resp != null && resp.getCode() != null && (resp.getCode() == 0 || resp.getCode() == 200)) {
                        return resp.getData();
                    }
                } else {
                    log.error("查询线上面试基础信息失败，响应码: {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("查询线上面试基础信息异常", e);
        }
        return null;
    }

    /**
     * 根据面试状态查询线上面试列表
     * 接口：/interview/getInterviews/{status}
     * 0-未开始 1-进行中 2-已结束
     *
     * @param status 状态码
     * @return 面试列表，失败返回 null
     */
    public List<InterviewInfoItem> getOnlineInterviewsByStatus(int status) {
        String url = baseUrl + "/interview/getInterviewsByStatus/" + status;
        try {
            log.info("根据状态查询线上面试列表: {}", url);
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .header("Content-Type", "application/json")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String bodyStr = response.body().string();
                    InterviewInfoResponse resp = JSON.parseObject(bodyStr, InterviewInfoResponse.class);
                    if (resp != null && resp.getCode() != null && (resp.getCode() == 0 || resp.getCode() == 200)) {
                        return resp.getData();
                    }
                } else {
                    log.error("根据状态查询线上面试失败，响应码: {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("根据状态查询线上面试异常", e);
        }
        return null;
    }
} 