package com.ruoyi.talentbase.schedule;

import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 面试过期检查任务
 */
@Slf4j
@Component
public class InterviewExpirationJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 从任务数据中获取面试ID
            Long interviewId = context.getJobDetail().getJobDataMap().getLong("interviewId");
            
            log.info("开始执行面试过期检查任务，面试ID: {}", interviewId);
            
            // 从Spring上下文获取服务
            ApplicationContext applicationContext = getApplicationContext(context);
            ITbOnlineInterviewService interviewService = applicationContext.getBean(ITbOnlineInterviewService.class);
            
            // 查询面试记录
            TbOnlineInterview interview = interviewService.selectTbOnlineInterviewById(interviewId);
            if (interview == null) {
                log.warn("未找到面试记录，ID: {}", interviewId);
                return;
            }
            
            // 检查面试状态，只有未开始或进行中的面试才需要检查过期
            if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus() ||
                InterviewStatusEnum.EXPIRED.getCode() == interview.getInterviewStatus()) {
                log.info("面试已结束或已过期，无需处理，面试ID: {}", interviewId);
                return;
            }
            
            // 更新面试状态为已过期
            interview.setInterviewStatus(InterviewStatusEnum.EXPIRED.getCode());
            interview.setUpdateBy("system");
            interview.setUpdateTime(new java.util.Date());
            
            interviewService.updateTbOnlineInterview(interview);
            
            log.info("面试过期状态更新成功，面试ID: {}", interviewId);
            
        } catch (Exception e) {
            log.error("执行面试过期检查任务失败", e);
            throw new JobExecutionException(e);
        }
    }
    
    /**
     * 从JobExecutionContext获取Spring ApplicationContext
     */
    private ApplicationContext getApplicationContext(JobExecutionContext context) throws JobExecutionException {
        try {
            return (ApplicationContext) context.getScheduler().getContext().get("applicationContext");
        } catch (Exception e) {
            log.error("获取Spring上下文失败", e);
            throw new JobExecutionException("无法获取Spring上下文", e);
        }
    }
} 