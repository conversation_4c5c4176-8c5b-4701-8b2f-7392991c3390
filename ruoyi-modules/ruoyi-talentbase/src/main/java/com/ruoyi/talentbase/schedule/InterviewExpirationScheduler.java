package com.ruoyi.talentbase.schedule;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbInterviewJobPaper;
import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 面试过期任务调度管理器
 */
@Slf4j
@Component
public class InterviewExpirationScheduler {

    @Autowired
    private Scheduler scheduler;
    
    @Autowired
    private ITbInterviewJobPaperService jobPaperService;

    /**
     * 为面试创建过期检查任务
     * @param interviewId 面试ID
     * @param inviteTime 邀请时间
     * @param jobPaperId 岗位试卷ID
     */
    public void scheduleInterviewExpiration(Long interviewId, Date inviteTime, Long jobPaperId) {
        try {
            if (interviewId == null || inviteTime == null || jobPaperId == null) {
                log.warn("面试记录信息不完整，无法创建过期检查任务");
                return;
            }

            // 获取岗位试卷信息
            TbInterviewJobPaper jobPaper = jobPaperService.selectTbInterviewJobPaperById(jobPaperId);
            if (jobPaper == null || jobPaper.getValidDays() == null) {
                log.warn("未找到岗位试卷或有效天数为空，面试ID: {}", interviewId);
                return;
            }

            // 计算过期时间
            Date expireTime = new Date(inviteTime.getTime() + jobPaper.getValidDays() * 24 * 60 * 60 * 1000L);
            
            // 如果已经过期，直接返回
            if (expireTime.before(new Date())) {
                log.info("面试已过期，无需创建过期检查任务，面试ID: {}", interviewId);
                return;
            }

            // 创建任务标识
            String jobKey = "interview_expire_" + interviewId;
            String triggerKey = "trigger_expire_" + interviewId;

            log.info("开始创建面试过期检查任务，面试ID: {}, 过期时间: {}", interviewId, expireTime);

            // 创建任务详情
            JobDetail jobDetail = JobBuilder.newJob(InterviewExpirationJob.class)
                    .withIdentity(jobKey)
                    .usingJobData("interviewId", interviewId)
                    .build();

            // 创建触发器，在过期时间执行
            log.info("设置触发器时间: {}, 面试ID: {}", expireTime, interviewId);
            
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(expireTime)
                    .build();

            // 保存任务到数据库
            scheduler.scheduleJob(jobDetail, trigger);
            
            // 验证任务是否保存成功
            JobKey jobKeyObj = JobKey.jobKey(jobKey);
            if (scheduler.checkExists(jobKeyObj)) {
                log.info("任务保存成功，面试ID: {}, 任务名称: {}", interviewId, jobKey);
            } else {
                log.error("任务保存失败，面试ID: {}, 任务名称: {}", interviewId, jobKey);
            }
            
            log.info("面试过期检查任务已调度，面试ID: {}, 执行时间: {}", interviewId, expireTime);
            
        } catch (SchedulerException e) {
            log.error("调度面试过期检查任务失败，面试ID: {}", interviewId, e);
        }
    }

    /**
     * 取消面试的过期检查任务
     * @param interviewId 面试ID
     */
    public void cancelExpirationCheck(Long interviewId) {
        try {
            if (interviewId == null) {
                return;
            }

            String jobKey = "interview_expire_" + interviewId;
            String triggerKey = "trigger_expire_" + interviewId;

            // 删除触发器
            scheduler.unscheduleJob(TriggerKey.triggerKey(triggerKey));
            
            // 删除任务
            scheduler.deleteJob(JobKey.jobKey(jobKey));
            
            log.info("面试过期检查任务取消成功，面试ID: {}", interviewId);
            
        } catch (SchedulerException e) {
            log.error("取消面试过期检查任务失败，面试ID: {}", interviewId, e);
        }
    }

    /**
     * 更新面试的过期检查任务（先取消再创建）
     * @param interview 面试记录
     */
    public void updateExpirationCheck(TbOnlineInterview interview) {
        if (interview == null || interview.getId() == null) {
            return;
        }
        
        // 先取消原有任务
        cancelExpirationCheck(interview.getId());
        
        // 再创建新任务
        scheduleInterviewExpiration(interview.getId(), interview.getInviteTime(), interview.getJobPaperId());
    }

    /**
     * 查询数据库中的面试过期任务
     * @param interviewId 面试ID
     */
    public void checkTaskInDatabase(Long interviewId) {
        try {
            String jobKey = "interview_expire_" + interviewId;
            String triggerKey = "trigger_expire_" + interviewId;
            
            // 检查任务是否存在
            JobKey jobKeyObj = JobKey.jobKey(jobKey);
            TriggerKey triggerKeyObj = TriggerKey.triggerKey(triggerKey);
            
            boolean jobExists = scheduler.checkExists(jobKeyObj);
            boolean triggerExists = scheduler.checkExists(triggerKeyObj);
            
            log.info("数据库任务检查结果 - 面试ID: {}, 任务存在: {}, 触发器存在: {}", 
                    interviewId, jobExists, triggerExists);
            
            if (jobExists) {
                JobDetail jobDetail = scheduler.getJobDetail(jobKeyObj);
                log.info("任务详情 - 面试ID: {}, 任务类: {}", interviewId, jobDetail.getJobClass().getName());
            }
            
            if (triggerExists) {
                Trigger trigger = scheduler.getTrigger(triggerKeyObj);
                log.info("触发器详情 - 面试ID: {}, 下次执行时间: {}", interviewId, trigger.getNextFireTime());
            }
            
        } catch (SchedulerException e) {
            log.error("查询数据库任务失败，面试ID: {}", interviewId, e);
        }
    }

    /**
     * 测试Quartz是否正常工作
     */
    public void testQuartz() {
        try {
            log.info("开始测试Quartz功能...");
            
            // 创建测试任务
            JobDetail jobDetail = JobBuilder.newJob(InterviewExpirationJob.class)
                    .withIdentity("test-job", "test-group")
                    .usingJobData("interviewId", 999L)
                    .build();
            
            // 创建触发器，1分钟后执行
            Date triggerTime = DateUtils.addMinutes(new Date(), 1);
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity("test-trigger", "test-group")
                    .startAt(triggerTime)
                    .build();
            
            // 调度任务
            scheduler.scheduleJob(jobDetail, trigger);
            
            log.info("测试任务创建成功，执行时间: {}", triggerTime);
            
        } catch (SchedulerException e) {
            log.error("测试Quartz功能失败", e);
        }
    }
} 