package com.ruoyi.talentbase.schedule;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.agent.AgentApiUtil;
import com.ruoyi.common.core.utils.agent.InterviewEvaluationResponse;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.TbOnlineInterviewAnswer;
import com.ruoyi.talentbase.domain.TbOnlineInterviewEvaluation;
import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.EvaluationTypeEnum;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.python.InterviewInfoItem;
import com.ruoyi.talentbase.python.InterviewMessageItem;
import com.ruoyi.talentbase.python.PythonApiUtil;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperQuestionService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewAnswerService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewEvaluationService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时同步线上面试状态及结果
 */
@Slf4j
@Component
public class OnlineInterviewScheduler {

    @Autowired
    private PythonApiUtil pythonApiUtil;
    @Autowired
    private ITbOnlineInterviewService interviewService;
    @Autowired
    private ITbOnlineInterviewAnswerService answerService;
    @Autowired
    private ITbInterviewJobPaperQuestionService questionService;
    @Autowired
    private ITbOnlineInterviewEvaluationService evaluationService;
    @Autowired
    private AgentApiUtil agentApiUtil;

    /**
     * 每 10 秒执行一次
     */
    @Scheduled(fixedDelay = 10000)
    public void syncInterviewStatus() {
        try {
            handleStatus(InterviewStatusEnum.IN_PROGRESS.getCode());
        } catch (Exception e) {
            log.error("定时同步线上面试异常", e);
        }
    }

    /**
     * 每 60 秒执行一次
     */
    @Scheduled(fixedDelay = 10000)
    public void syncInterviewStatusAndResult() {
        try {
            handleStatus(InterviewStatusEnum.COMPLETED.getCode());
        } catch (Exception e) {
            log.error("定时同步线上面试异常", e);
        }
    }

    private void handleStatus(int status) {
        List<InterviewInfoItem> infoItems = pythonApiUtil.getOnlineInterviewsByStatus(status);
        if (infoItems == null || infoItems.isEmpty()) {
            return;
        }

        // 先取接口返回的 inviteUrl 集合，仅查询本地需要的记录
        java.util.Set<String> inviteSet = infoItems.stream()
                .map(InterviewInfoItem::getInterviewId)
                .filter(StringUtils::isNotBlank)
                .collect(java.util.stream.Collectors.toSet());

        if (inviteSet.isEmpty()) {
            return;
        }

        List<TbOnlineInterview> localList = interviewService.selectByInviteUrls(inviteSet);
        Map<String, TbOnlineInterview> localMap = localList.stream()
                .collect(Collectors.toMap(TbOnlineInterview::getInviteUrl, i -> i));

        List<String> finishedInviteUrls = new ArrayList<>();
        List<TbOnlineInterview> needUpdate = new ArrayList<>();
        List<TbOnlineInterviewAnswer> answersToSave = new ArrayList<>();

        for (InterviewInfoItem item : infoItems) {
            if (item == null || StringUtils.isBlank(item.getInterviewId())) {
                continue;
            }
            TbOnlineInterview local = localMap.get(item.getInterviewId());
            if (local == null) {
                continue;
            }
            if (status == InterviewStatusEnum.IN_PROGRESS.getCode()) {
                // 更新为进行中
                if (!Objects.equals(local.getInterviewStatus(), InterviewStatusEnum.IN_PROGRESS.getCode())) {
                    local.setInterviewTime(item.getStartTime());
                    local.setInterviewStatus(InterviewStatusEnum.IN_PROGRESS.getCode());
                    needUpdate.add(local);
                }
            } else if (status == InterviewStatusEnum.COMPLETED.getCode()) {
                // 更新为已完成并保存结果
                if (!Objects.equals(local.getInterviewStatus(), InterviewStatusEnum.COMPLETED.getCode())) {
                    local.setInterviewStatus(InterviewStatusEnum.COMPLETED.getCode());
                    local.setInterviewEndTime(item.getEndTime());
                    local.setPlayUrl(item.getPlayUrl());
                    needUpdate.add(local);
                }
                finishedInviteUrls.add(item.getInterviewId());
            }
        }

        // 批量更新状态
        for (TbOnlineInterview upd : needUpdate) {
            interviewService.updateTbOnlineInterview(upd);
        }

        // 对已结束的面试，拉取并保存结果，然后删除外网记录
        if (!finishedInviteUrls.isEmpty()) {
            List<InterviewMessageItem> resultItems = pythonApiUtil.getOnlineInterviewResult(finishedInviteUrls.toArray(new String[0]));
            if (resultItems != null && !resultItems.isEmpty()) {
                Map<String, TbOnlineInterview> finishedMap = localMap;
                for (InterviewMessageItem msg : resultItems) {
                    TbOnlineInterview interview = finishedMap.get(msg.getInterviewId());
                    if (interview == null) continue;
                    TbOnlineInterviewAnswer ans = new TbOnlineInterviewAnswer();
                    ans.setInterviewId(interview.getId());
                    ans.setJobPaperId(interview.getJobPaperId());
                    ans.setQuestionId(Long.valueOf(msg.getAskNum()));
                    ans.setAnswer(msg.getAnswerContent());
                    ans.setRemark(msg.getAskContent());
                    answersToSave.add(ans);
                }
                if (!answersToSave.isEmpty()) {
                    answerService.batchInsert(answersToSave);
                }
            }
            
            // 对已完成的面试生成AI评价
            for (TbOnlineInterview interview : needUpdate) {
                if (interview.getInterviewStatus().equals(InterviewStatusEnum.COMPLETED.getCode())) {
                    generateAIEvaluation(interview);
                }
            }
            
            // 删除外网记录
            pythonApiUtil.deleteOnlineInterviews(finishedInviteUrls.toArray(new String[0]));
        }
    }

    /**
     * 异步生成AI评价
     */
    @Async
    protected void generateAIEvaluation(TbOnlineInterview interview) {
        try {
            // 获取面试问答内容
            String evaluationContent = getEvaluationContent(interview.getId());
            if (evaluationContent == null || evaluationContent.trim().isEmpty()) {
                log.warn("面试ID：{} 没有问答内容，无法生成AI评价", interview.getId());
                return;
            }

            InterviewEvaluationResponse evaluation = agentApiUtil.generateEvaluation(evaluationContent);
            if (evaluation != null) {
                // 创建评价记录
                TbOnlineInterviewEvaluation evaluationRecord = new TbOnlineInterviewEvaluation();
                evaluationRecord.setOnlineInterviewId(interview.getId());
                evaluationRecord.setEvaluationContent(evaluation.getSummary());
                evaluationRecord.setScore(evaluation.getScore().doubleValue());
                evaluationRecord.setEvaluationType(EvaluationTypeEnum.AI.getCode());
                evaluationRecord.setCreateBy("system");
                evaluationRecord.setCreateTime(DateUtils.getNowDate());
                
                // 保存评价记录
                evaluationService.insertTbOnlineInterviewEvaluation(evaluationRecord);
                
                log.info("线上面试AI评价生成成功，面试ID：{}", interview.getId());
            }
        } catch (Exception e) {
            log.error("生成线上面试AI评价失败，面试ID：{}", interview.getId(), e);
        }
    }

    /**
     * 获取面试问答内容用于AI评价
     * 格式：面试官：问题
     *      面试官：问题的标准答案
     *      面试者：答案
     */
    private String getEvaluationContent(Long interviewId) {
        try {
            // 获取面试答案记录
            List<TbOnlineInterviewAnswer> answers = answerService.selectByInterviewId(interviewId);
            if (answers == null || answers.isEmpty()) {
                return null;
            }

            // 获取问题信息
            List<Long> questionIds = answers.stream()
                    .map(TbOnlineInterviewAnswer::getQuestionId)
                    .collect(Collectors.toList());
            
            List<TbInterviewJobPaperQuestion> questions = questionService.selectByIds(questionIds);
            Map<Long, TbInterviewJobPaperQuestion> questionMap = questions.stream()
                    .collect(Collectors.toMap(TbInterviewJobPaperQuestion::getId, q -> q));

            // 构建评价内容，参考离线面试的合并逻辑
            return answers.stream()
                    .map(answer -> {
                        TbInterviewJobPaperQuestion question = questionMap.get(answer.getQuestionId());
                        if (question != null) {
                            return "面试官:" + question.getQuestion() + 
                                   ",标准答案:" + question.getAnswer() + 
                                   ",面试者:" + answer.getAnswer();
                        }
                        return null;
                    })
                    .filter(content -> content != null)
                    .collect(Collectors.joining(","));
            
        } catch (Exception e) {
            log.error("获取面试问答内容失败，面试ID：{}", interviewId, e);
            return null;
        }
    }
} 