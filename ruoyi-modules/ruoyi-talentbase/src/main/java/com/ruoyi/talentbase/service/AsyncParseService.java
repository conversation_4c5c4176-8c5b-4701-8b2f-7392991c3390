package com.ruoyi.talentbase.service;

import com.ruoyi.common.core.utils.agent.AgentApiUtil;
import com.ruoyi.talentbase.domain.TbAnalysePersonalFile;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;

/**
 * 异步解析服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncParseService {
    
    private static final Logger log = LoggerFactory.getLogger(AsyncParseService.class);
    
    @Autowired
    private AgentApiUtil agentApiUtil;
    
    @Autowired
    private ITbAnalysePersonalFileService analysePersonalFileService;
    
    /**
     * 异步调用简历解析接口
     */
    @Async
    public void asyncParseTalent(String fileUrl, Long taskId, Long fileId) {
        try {
            log.info("开始异步解析简历 - fileId: {}, taskId: {}", fileId, taskId);
            agentApiUtil.parseTalent(fileUrl, taskId, fileId);
            log.info("异步解析简历完成 - fileId: {}, taskId: {}", fileId, taskId);
        } catch (Exception ex) {
            log.error("异步解析简历失败 - fileId: {}, taskId: {}, error: {}", fileId, taskId, ex.getMessage(), ex);
        }
    }
    
    /**
     * 异步批量重试解析
     */
    @Async
    public void asyncRetryByTask(Long taskId, List<TbAnalysePersonalFile> failedFiles) {
        log.info("异步方法开始执行 - taskId: {}, 文件数量: {}", taskId, failedFiles.size());
        int success = 0;
        int failed = 0;
        
        // 提取文件ID列表
        List<Long> fileIds = new ArrayList<>();
        for (TbAnalysePersonalFile file : failedFiles) {
            fileIds.add(file.getId());
        }
        log.info("文件ID列表提取完成 - taskId: {}, ID数量: {}", taskId, fileIds.size());
        
        // 批量更新文件状态为解析中
        try {
            log.info("开始批量更新文件状态 - taskId: {}, 状态: {}", taskId, ParseStatusEnum.PARSING.getCode());
            int updateResult = analysePersonalFileService.batchUpdateStatus(fileIds, ParseStatusEnum.PARSING.getCode());
            log.info("批量更新文件状态完成 - taskId: {}, 更新数量: {}", taskId, updateResult);
        } catch (Exception ex) {
            log.error("批量更新文件状态失败 - taskId: {}, error: {}", taskId, ex.getMessage(), ex);
            return;
        }
        
        // 异步调用解析接口
        log.info("开始异步调用解析接口 - taskId: {}, 文件数量: {}", taskId, failedFiles.size());
        for (TbAnalysePersonalFile file : failedFiles) {
            try {
                log.debug("调用解析接口 - fileId: {}, taskId: {}, fileUrl: {}", file.getId(), taskId, file.getFileUrl());
                asyncParseTalent(file.getFileUrl(), file.getTaskId(), file.getId());
                success++;
            } catch (Exception ex) {
                failed++;
                log.error("重试解析失败 - fileId: {}, taskId: {}, error: {}", file.getId(), taskId, ex.getMessage(), ex);
            }
        }
        
        log.info("任务重试解析完成 - taskId: {}, 成功: {}, 失败: {}", taskId, success, failed);
    }
} 