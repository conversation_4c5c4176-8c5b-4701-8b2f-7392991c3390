package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewAudioFile;

/**
 * 线下面试音频文件Service接口
 */
public interface IOfflineInterviewAudioFileService {
    /**
     * 查询线下面试音频文件列表
     * 
     * @param audioFile 查询条件
     * @return 音频文件列表
     */
    List<OfflineInterviewAudioFile> selectOfflineInterviewAudioFileList(OfflineInterviewAudioFile audioFile);

    /**
     * 新增线下面试音频文件
     * 
     * @param audioFile 音频文件信息
     * @return 结果
     */
    int insert(OfflineInterviewAudioFile audioFile);

    /**
     * 根据线下面试ID删除音频文件
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    int deleteByOfflineInterviewId(Long offlineInterviewId);
} 