package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewConversation;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewConversationVO;

/**
 * 线下面试会话Service接口
 */
public interface IOfflineInterviewConversationService {
    /**
     * 查询线下面试会话VO列表
     * 
     * @param conversation 查询条件
     * @return 线下面试会话VO列表
     */
    List<OfflineInterviewConversationVO> selectOfflineInterviewConversationVOList(OfflineInterviewConversation conversation);

    /**
     * 根据面试ID查询会话列表
     * 
     * @param offlineInterviewId 面试ID
     * @return 会话列表
     */
    List<OfflineInterviewConversation> selectByOfflineInterviewId(Long offlineInterviewId);

    /**
     * 批量新增线下面试会话
     * 
     * @param conversationList 会话列表
     * @return 结果
     */
    int batchInsert(List<OfflineInterviewConversation> conversationList);

    /**
     * 根据线下面试ID删除会话
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    int deleteByOfflineInterviewId(Long offlineInterviewId);
} 