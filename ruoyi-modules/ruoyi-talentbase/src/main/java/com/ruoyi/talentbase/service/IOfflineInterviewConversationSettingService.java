package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationSetting;

/**
 * 线下面试会话配置Service接口
 */
public interface IOfflineInterviewConversationSettingService {
    /**
     * 批量新增线下面试会话配置
     * 
     * @param settingList 线下面试会话配置列表
     * @return 结果
     */
    public int batchInsert(List<OfflineInterviewConversationSetting> settingList);

    /**
     * 批量更新线下面试会话配置
     * 
     * @param settingList 线下面试会话配置列表
     * @return 结果
     */
    public int batchUpdate(List<OfflineInterviewConversationSetting> settingList);

    /**
     * 根据线下面试ID查询会话配置
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 会话配置列表
     */
    public List<OfflineInterviewConversationSetting> selectByOfflineInterviewId(Long offlineInterviewId);

    /**
     * 根据线下面试ID删除会话配置
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    public int deleteByOfflineInterviewId(Long offlineInterviewId);
} 