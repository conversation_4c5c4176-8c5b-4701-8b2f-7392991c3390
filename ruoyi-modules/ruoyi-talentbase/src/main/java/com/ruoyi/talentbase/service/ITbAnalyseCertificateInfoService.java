package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalyseCertificateInfo;
import java.util.List;

public interface ITbAnalyseCertificateInfoService {
    TbAnalyseCertificateInfo selectTbAnalyseCertificateInfoById(Long id);

    List<TbAnalyseCertificateInfo> selectTbAnalyseCertificateInfoList(TbAnalyseCertificateInfo info);

    int insertTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info);

    int updateTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info);

    int deleteTbAnalyseCertificateInfoById(Long id);

    int deleteTbAnalyseCertificateInfoByIds(Long[] ids);
} 