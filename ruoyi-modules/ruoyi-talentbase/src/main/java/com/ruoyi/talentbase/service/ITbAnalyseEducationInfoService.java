package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import java.util.List;

public interface ITbAnalyseEducationInfoService {
    TbAnalyseEducationInfo selectTbAnalyseEducationInfoById(Long id);

    List<TbAnalyseEducationInfo> selectTbAnalyseEducationInfoList(TbAnalyseEducationInfo info);

    int insertTbAnalyseEducationInfo(TbAnalyseEducationInfo info);

    int updateTbAnalyseEducationInfo(TbAnalyseEducationInfo info);

    int deleteTbAnalyseEducationInfoById(Long id);

    int deleteTbAnalyseEducationInfoByIds(Long[] ids);
} 