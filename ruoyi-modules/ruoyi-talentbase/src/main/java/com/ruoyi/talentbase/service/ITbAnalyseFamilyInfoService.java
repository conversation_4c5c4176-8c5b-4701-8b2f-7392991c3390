package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;

import java.util.List;

/**
 * 家庭信息Service接口
 */
public interface ITbAnalyseFamilyInfoService {
    TbAnalyseFamilyInfo selectTbAnalyseFamilyInfoById(Long id);

    List<TbAnalyseFamilyInfo> selectTbAnalyseFamilyInfoList(TbAnalyseFamilyInfo info);

    int insertTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info);

    int updateTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info);

    int deleteTbAnalyseFamilyInfoById(Long id);

    int deleteTbAnalyseFamilyInfoByIds(Long[] ids);
} 