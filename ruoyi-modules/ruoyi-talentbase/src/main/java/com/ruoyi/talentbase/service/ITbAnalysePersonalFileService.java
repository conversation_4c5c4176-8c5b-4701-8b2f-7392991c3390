package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalysePersonalFile;

import java.util.List;

/**
 * 个人档案Service接口
 */
public interface ITbAnalysePersonalFileService {
    TbAnalysePersonalFile selectTbAnalysePersonalFileById(Long id);

    List<TbAnalysePersonalFile> selectTbAnalysePersonalFileList(TbAnalysePersonalFile file);

    int insertTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int updateTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int deleteTbAnalysePersonalFileById(Long id);

    int deleteTbAnalysePersonalFileByIds(Long[] ids);
    
    /**
     * 批量更新文件状态
     * @param fileIds 文件ID列表
     * @param status 状态
     * @return 更新结果
     */
    int batchUpdateStatus(List<Long> fileIds, Integer status);
} 