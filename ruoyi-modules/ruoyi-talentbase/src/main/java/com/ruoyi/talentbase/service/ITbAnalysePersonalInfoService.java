package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalysePersonalInfo;
import com.ruoyi.talentbase.domain.vo.TbAnalysePersonalGetInfoVO;

import java.util.List;

/**
 * 个人简历信息Service接口
 */
public interface ITbAnalysePersonalInfoService {
    TbAnalysePersonalInfo selectTbAnalysePersonalInfoById(Long id);

    List<TbAnalysePersonalInfo> selectTbAnalysePersonalInfoList(TbAnalysePersonalInfo info);

    int insertTbAnalysePersonalInfo(TbAnalysePersonalInfo info);

    int updateTbAnalysePersonalInfo(TbAnalysePersonalInfo info);

    int deleteTbAnalysePersonalInfoById(Long id);

    int deleteTbAnalysePersonalInfoByIds(Long[] ids);

    TbAnalysePersonalGetInfoVO selectTbAnalysePersonalGetInfoVoById(Long id);
} 