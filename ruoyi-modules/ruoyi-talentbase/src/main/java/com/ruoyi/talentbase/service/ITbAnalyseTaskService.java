package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalyseTask;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;

import java.util.List;

/**
 * 简历分析任务Service接口
 */
public interface ITbAnalyseTaskService {

    /**
     * 查询任务
     */
    TbAnalyseTask selectTbAnalyseTaskById(Long id);

    /**
     * 查询任务列表
     */
    List<TbAnalyseTask> selectTbAnalyseTaskList(AnalyseTaskQueryDTO query);

    /**
     * 新增任务
     */
    int insertTbAnalyseTask(TbAnalyseTask task);

    /**
     * 修改任务
     */
    int updateTbAnalyseTask(TbAnalyseTask task);

    /**
     * 批量删除
     */
    int deleteTbAnalyseTaskByIds(Long[] ids);

    /**
     * 删除任务
     */
    int deleteTbAnalyseTaskById(Long id);

    /**
     * 查询简历列表（统一接口，包含文件信息和简历信息）
     */
    List<ResumeListVO> selectResumeList(ResumeListQueryDTO queryDTO);
} 