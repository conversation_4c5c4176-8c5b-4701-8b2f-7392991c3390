package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;

import java.util.List;

/**
 * 工作经历分析Service接口
 */
public interface ITbAnalyseWorkExperienceService {
    TbAnalyseWorkExperience selectTbAnalyseWorkExperienceById(Long id);

    List<TbAnalyseWorkExperience> selectTbAnalyseWorkExperienceList(TbAnalyseWorkExperience workExperience);

    int insertTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience);

    int insertBatch(List<TbAnalyseWorkExperience> workExperienceList);

    int updateTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience);

    int deleteTbAnalyseWorkExperienceById(Long id);

    int deleteTbAnalyseWorkExperienceByIds(Long[] ids);

    int deleteTbAnalyseWorkExperienceByPersonalId(Long personalId);
} 