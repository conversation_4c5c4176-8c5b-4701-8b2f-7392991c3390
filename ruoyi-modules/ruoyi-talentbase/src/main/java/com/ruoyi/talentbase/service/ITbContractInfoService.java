package com.ruoyi.talentbase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbContractInfo;

import java.util.List;

/**
 * 合同信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbContractInfoService extends IService<TbContractInfo> {
    List<TbContractInfo> selectList(TbContractInfo contractInfo);

    TbContractInfo selectById(Long id);

    int insert(TbContractInfo userInfo);

    int update(TbContractInfo userInfo);

    int delete(Long[] ids);

    Integer selectEndTimeByUserId(Long userId);

    int deleteByUserInfoId(Long userId);
}
