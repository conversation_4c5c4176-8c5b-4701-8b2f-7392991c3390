package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.TbEducationInfo;
import com.ruoyi.talentbase.domain.TbWorkExperience;

/**
 * 教育信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbEducationInfoService 
{
    /**
     * 查询教育信息
     * 
     * @param id 教育信息主键
     * @return 教育信息
     */
    public TbEducationInfo selectTbEducationInfoById(Long id);

    /**
     * 查询教育信息列表
     * 
     * @param tbEducationInfo 教育信息
     * @return 教育信息集合
     */
    public List<TbEducationInfo> selectTbEducationInfoList(TbEducationInfo tbEducationInfo);

    /**
     * 新增教育信息
     * 
     * @param tbEducationInfo 教育信息
     * @return 结果
     */
    public int insertTbEducationInfo(TbEducationInfo tbEducationInfo);

    /**
     * 修改教育信息
     * 
     * @param tbEducationInfo 教育信息
     * @return 结果
     */
    public int updateTbEducationInfo(TbEducationInfo tbEducationInfo);

    /**
     * 批量删除教育信息
     * 
     * @param ids 需要删除的教育信息主键集合
     * @return 结果
     */
    public int deleteTbEducationInfoByIds(Long[] ids);

    /**
     * 删除教育信息信息
     * 
     * @param id 教育信息主键
     * @return 结果
     */
    public int deleteTbEducationInfoById(Long id);

    // 批量删除插入教育信息
    int deleteAndInsert(List<TbEducationInfo> workExperienceList, Long personalId);

    int deleteByPersonalId(Long infoId);
}
