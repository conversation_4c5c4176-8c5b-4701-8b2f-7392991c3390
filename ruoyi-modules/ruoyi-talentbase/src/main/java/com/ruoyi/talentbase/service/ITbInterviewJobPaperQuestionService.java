package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import java.util.List;

public interface ITbInterviewJobPaperQuestionService {
    TbInterviewJobPaperQuestion selectTbInterviewJobPaperQuestionById(Long id);
    List<TbInterviewJobPaperQuestion> selectTbInterviewJobPaperQuestionList(TbInterviewJobPaperQuestion question);
    int insertTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question);
    int updateTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question);
    int deleteTbInterviewJobPaperQuestionById(Long id);
    int deleteTbInterviewJobPaperQuestionByIds(Long[] ids);

    /**
     * 根据试卷ID删除所有问题
     * 
     * @param jobPaperId 试卷ID
     * @return 删除的记录数
     */
    int deleteTbInterviewJobPaperQuestionByJobPaperId(Long jobPaperId);

    /**
     * 根据ID列表批量查询问题
     * 
     * @param ids 问题ID列表
     * @return 问题列表
     */
    List<TbInterviewJobPaperQuestion> selectByIds(List<Long> ids);
} 