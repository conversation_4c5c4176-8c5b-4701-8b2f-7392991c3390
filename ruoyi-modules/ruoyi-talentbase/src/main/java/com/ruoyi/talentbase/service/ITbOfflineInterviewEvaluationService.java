package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.TbOfflineInterviewEvaluation;

/**
 * 线下面试评价Service接口
 */
public interface ITbOfflineInterviewEvaluationService {
    /**
     * 查询线下面试评价
     */
    public TbOfflineInterviewEvaluation selectTbOfflineInterviewEvaluationById(Long id);

    /**
     * 查询线下面试评价列表
     */
    public List<TbOfflineInterviewEvaluation> selectTbOfflineInterviewEvaluationList(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation);

    /**
     * 新增线下面试评价
     */
    public int insertTbOfflineInterviewEvaluation(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation);

    /**
     * 修改线下面试评价
     */
    public int updateTbOfflineInterviewEvaluation(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation);

    /**
     * 批量删除线下面试评价
     */
    public int deleteTbOfflineInterviewEvaluationByIds(Long[] ids);

    /**
     * 删除线下面试评价信息
     */
    public int deleteTbOfflineInterviewEvaluationById(Long id);

    /**
     * 根据线下面试ID删除评价
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    public int deleteTbOfflineInterviewEvaluationByOfflineInterviewId(Long offlineInterviewId);
} 