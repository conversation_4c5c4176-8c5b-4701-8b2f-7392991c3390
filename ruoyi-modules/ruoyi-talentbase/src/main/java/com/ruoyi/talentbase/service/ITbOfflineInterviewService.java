package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbOfflineInterview;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;

import java.util.List;

public interface ITbOfflineInterviewService {
    TbOfflineInterview selectTbOfflineInterviewById(Long id);
    List<TbOfflineInterview> selectTbOfflineInterviewList(OfflineInterviewQueryDTO queryDTO);
    int insertTbOfflineInterview(TbOfflineInterview interview);
    int updateTbOfflineInterview(TbOfflineInterview interview);
    int deleteTbOfflineInterviewById(Long id);
    int deleteTbOfflineInterviewByIds(Long[] ids);

    /**
     * 根据人才来源类型和来源ID，将相关面试状态批量更新为"已过期"
     * @param sourceType 人才来源类型，参考 TalentSourceEnum
     * @param sourceId   来源ID
     * @return 受影响行数
     */
    int expireOfflineInterviewsBySource(Integer sourceType, Long sourceId);

    int deleteByPInfoId(Long infoId, TalentSourceEnum sourceEnum);
}