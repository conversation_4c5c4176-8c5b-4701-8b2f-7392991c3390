package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbOnlineInterviewAnswer;
import java.util.List;

public interface ITbOnlineInterviewAnswerService {

    int insertTbOnlineInterviewAnswer(TbOnlineInterviewAnswer answer);

    int batchInsert(List<TbOnlineInterviewAnswer> list);

    TbOnlineInterviewAnswer selectByInterviewAndQuestion(Long interviewId, Long jobPaperId, Long questionId);

    /**
     * 根据面试ID查询所有答案
     * 
     * @param interviewId 面试ID
     * @return 答案列表
     */
    List<TbOnlineInterviewAnswer> selectByInterviewId(Long interviewId);
} 