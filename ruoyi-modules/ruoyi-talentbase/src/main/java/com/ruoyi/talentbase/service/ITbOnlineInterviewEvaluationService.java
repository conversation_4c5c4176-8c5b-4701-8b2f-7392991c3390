package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbOnlineInterviewEvaluation;

import java.util.List;

/**
 * 线上面试评价Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITbOnlineInterviewEvaluationService {
    /**
     * 查询线上面试评价
     * 
     * @param id 线上面试评价主键
     * @return 线上面试评价
     */
    public TbOnlineInterviewEvaluation selectTbOnlineInterviewEvaluationById(Long id);

    /**
     * 查询线上面试评价列表
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 线上面试评价集合
     */
    public List<TbOnlineInterviewEvaluation> selectTbOnlineInterviewEvaluationList(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation);

    /**
     * 新增线上面试评价
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 结果
     */
    public int insertTbOnlineInterviewEvaluation(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation);

    /**
     * 修改线上面试评价
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 结果
     */
    public int updateTbOnlineInterviewEvaluation(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation);

    /**
     * 批量删除线上面试评价
     * 
     * @param ids 需要删除的线上面试评价主键集合
     * @return 结果
     */
    public int deleteTbOnlineInterviewEvaluationByIds(Long[] ids);

    /**
     * 删除线上面试评价信息
     * 
     * @param id 线上面试评价主键
     * @return 结果
     */
    public int deleteTbOnlineInterviewEvaluationById(Long id);

    /**
     * 根据线上面试ID删除评价
     * 
     * @param onlineInterviewId 线上面试ID
     * @return 结果
     */
    public int deleteTbOnlineInterviewEvaluationByOnlineInterviewId(Long onlineInterviewId);
} 