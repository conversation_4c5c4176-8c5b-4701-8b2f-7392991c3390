package com.ruoyi.talentbase.service;

import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;

import java.util.Collection;
import java.util.List;

public interface ITbOnlineInterviewService {
    TbOnlineInterview selectTbOnlineInterviewById(Long id);
    List<TbOnlineInterview> selectTbOnlineInterviewList(OnlineInterviewQueryDTO queryDTO);
    int insertTbOnlineInterview(TbOnlineInterview interview);
    int updateTbOnlineInterview(TbOnlineInterview interview);
    int deleteTbOnlineInterviewById(Long id);
    int deleteTbOnlineInterviewByIds(Long[] ids);

    /**
     * 根据 inviteUrl 批量查询
     */
    java.util.List<TbOnlineInterview> selectByInviteUrls(Collection<String> urls);

    /**
     * 根据来源类型和来源ID批量过期面试
     */
    int expireOnlineInterviewsBySource(Integer sourceType, Long sourceId);

    int deleteByPInfoId(Long infoId, TalentSourceEnum sourceEnum);
}