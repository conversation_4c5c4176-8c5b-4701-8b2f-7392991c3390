package com.ruoyi.talentbase.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbPersonalFile;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;

/**
 * 个人档案Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbPersonalFileService extends IService<TbPersonalFile>
{
    /**
     * 查询个人档案
     * 
     * @param id 个人档案主键
     * @return 个人档案
     */
    public TbPersonalFile selectTbPersonalFileById(Long id);

    /**
     * 查询个人档案列表
     * 
     * @param tbPersonalFile 个人档案
     * @return 个人档案集合
     */
    public List<TbPersonalFile> selectTbPersonalFileList(TbPersonalFile tbPersonalFile);

    /**
     * 新增个人档案
     * 
     * @param tbPersonalFile 个人档案
     * @return 结果
     */
    public int insertTbPersonalFile(TbPersonalFile tbPersonalFile);

    /**
     * 修改个人档案
     * 
     * @param tbPersonalFile 个人档案
     * @return 结果
     */
    public int updateTbPersonalFile(TbPersonalFile tbPersonalFile);

    /**
     * 批量删除个人档案
     * 
     * @param ids 需要删除的个人档案主键集合
     * @return 结果
     */
    public int deleteTbPersonalFileByIds(Long[] ids);

    /**
     * 删除个人档案信息
     * 
     * @param id 个人档案主键
     * @return 结果
     */
    public int deleteTbPersonalFileById(Long id);

    List<TbPersonalFile> selectTbPersonalFileByIds(Long[] ids);

    /**
     * 批量插入个人文件
     *
     * @param personalFileList 个人文件列表
     * @param personalId 个人ID
     * @param fileSourceEnum 文件来源枚举
     * @return 结果
     */
    int deleteAndInsert(List<TbPersonalFile> personalFileList, Long personalId, Long undergoId,FileSourceEnum fileSourceEnum);
    /**
     * 批量插入个人文件
     *
     * @param personalFileList 个人文件列表
     * @param personalId 个人ID
     * @param fileSourceEnum 文件来源枚举
     * @return 结果
     */
    int deleteAndInsert(List<TbPersonalFile> personalFileList, Long personalId, FileSourceEnum fileSourceEnum);


    int insertBatch(List<TbPersonalFile> fileList, Long personalId,Long undergoId, FileSourceEnum fileSourceEnum);

    int deleteByPersonalId(Long infoId);
}
