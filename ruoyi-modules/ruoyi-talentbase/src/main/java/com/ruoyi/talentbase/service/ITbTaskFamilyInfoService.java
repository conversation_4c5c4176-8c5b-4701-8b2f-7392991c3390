package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.TbTaskFamilyInfo;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface ITbTaskFamilyInfoService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskFamilyInfo selectTbTaskFamilyInfoById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskFamilyInfo> selectTbTaskFamilyInfoList(TbTaskFamilyInfo tbFamilyInfo);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskFamilyInfo(TbTaskFamilyInfo tbFamilyInfo);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskFamilyInfo(TbTaskFamilyInfo tbFamilyInfo);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteTbTaskFamilyInfoByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbTaskFamilyInfoById(Long id);
}
