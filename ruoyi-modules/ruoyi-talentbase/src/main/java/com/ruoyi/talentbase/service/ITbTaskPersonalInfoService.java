package com.ruoyi.talentbase.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.redis.service.IBaseService;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.TbTaskPersonalInfo;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.vo.TbTaskPersonalInfoVo;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface ITbTaskPersonalInfoService extends IService<TbTaskPersonalInfo>
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbTaskPersonalInfo selectTbTaskPersonalInfoById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbTaskPersonalInfo> selectTbTaskPersonalInfoList(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteTbTaskPersonalInfoByIds(Long[] ids);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<TbTaskPersonalInfoVo> selectTbTaskPersonalInfoVoList(TbTaskPersonalInfoParamDto tbPersonalInfo);

    /**
     * 批量更新简历入库状态
     * @param ids
     */
    int batchEditTalentPoolStatus(Long[] ids);

    List<TbTaskPersonalInfo> selectTTPInfoListByStatusOrFlag(TbTaskPersonalInfo personalInfo);

    int editEntryById(Long[] id);

    List<TbTaskPersonalInfo> selectListByIds(Long[] ids);
}
