package com.ruoyi.talentbase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbEducationInfo;
import com.ruoyi.talentbase.domain.TbUserInfo;
import com.ruoyi.talentbase.domain.dto.TbUserInfoDto;
import com.ruoyi.talentbase.domain.vo.TbUserInfoVo;

import java.util.List;

/**
 * 教育信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbUserInfoService extends IService<TbUserInfo> {
    List<TbUserInfoVo> selectTbUserInfoVoList(TbUserInfoDto userInfo);

    TbUserInfo selectTbUserInfoById(Long id);

    int insertTbUserInfo(TbUserInfo userInfo);

    int updateTbUserInfo(TbUserInfo userInfo);

    int deleteTbUserInfoByIds(Long[] ids);

    TbUserInfo selectByBasePersonalId(Long personalId);

    List<TbUserInfo> selectByIds(Long[] ids);

    int updateTbUserLeaveInfo(TbUserInfo userInfo);

    int deleteTbUserInfoByPersonalIds(Long[] ids);

    Integer getUserCount(Long deptId);
}
