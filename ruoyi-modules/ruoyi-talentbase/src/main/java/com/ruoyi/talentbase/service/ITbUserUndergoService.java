package com.ruoyi.talentbase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbUserUndergo;

import java.util.List;

/**
 * 用户档案信息Service接口
 * <AUTHOR>
 */
public interface ITbUserUndergoService extends IService<TbUserUndergo> {
    List<TbUserUndergo> selectTbUserUndergoList(TbUserUndergo tbUserUndergo);

    TbUserUndergo selectTbUserUndergoById(Long id);

    int insertTbUserUndergo(TbUserUndergo tbUserUndergo);

    int updateTbUserUndergo(TbUserUndergo tbUserUndergo);

    int deleteTbUserUndergoByIds(Long[] ids);

    int deleteByPInfoId(Long infoId);
}