package com.ruoyi.talentbase.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbWorkExperience;

/**
 * 工作经历Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbWorkExperienceService extends IService<TbWorkExperience>
{
    /**
     * 查询工作经历
     * 
     * @param id 工作经历主键
     * @return 工作经历
     */
    public TbWorkExperience selectTbWorkExperienceById(Long id);

    /**
     * 查询工作经历列表
     * 
     * @param tbWorkExperience 工作经历
     * @return 工作经历集合
     */
    public List<TbWorkExperience> selectTbWorkExperienceList(TbWorkExperience tbWorkExperience);

    /**
     * 新增工作经历
     * 
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    public int insertTbWorkExperience(TbWorkExperience tbWorkExperience);

    /**
     * 修改工作经历
     * 
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    public int updateTbWorkExperience(TbWorkExperience tbWorkExperience);

    /**
     * 批量删除工作经历
     * 
     * @param ids 需要删除的工作经历主键集合
     * @return 结果
     */
    public int deleteTbWorkExperienceByIds(Long[] ids);

    /**
     * 删除工作经历信息
     * 
     * @param id 工作经历主键
     * @return 结果
     */
    public int deleteTbWorkExperienceById(Long id);

    /**
     * 批量删除和插入
     *
     * @param taskPersonalInfoList 工作经历集合
     * @return 结果
     */
    int deleteAndInsert(List<TbWorkExperience> taskPersonalInfoList,Long personalId);

    // 根据人才ID删除工作经历
    int deleteByPersonalId(Long infoId);
}
