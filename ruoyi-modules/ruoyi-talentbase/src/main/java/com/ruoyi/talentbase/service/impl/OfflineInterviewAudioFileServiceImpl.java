package com.ruoyi.talentbase.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.OfflineInterviewAudioFileMapper;
import com.ruoyi.talentbase.domain.OfflineInterviewAudioFile;
import com.ruoyi.talentbase.service.IOfflineInterviewAudioFileService;

/**
 * 线下面试音频文件Service业务层处理
 */
@Service
public class OfflineInterviewAudioFileServiceImpl implements IOfflineInterviewAudioFileService {
    @Autowired
    private OfflineInterviewAudioFileMapper audioFileMapper;

    /**
     * 查询线下面试音频文件列表
     * 
     * @param audioFile 查询条件
     * @return 音频文件列表
     */
    @Override
    public List<OfflineInterviewAudioFile> selectOfflineInterviewAudioFileList(OfflineInterviewAudioFile audioFile) {
        return audioFileMapper.selectOfflineInterviewAudioFileList(audioFile);
    }

    /**
     * 新增线下面试音频文件
     * 
     * @param audioFile 音频文件信息
     * @return 结果
     */
    @Override
    public int insert(OfflineInterviewAudioFile audioFile) {
        return audioFileMapper.insert(audioFile);
    }

    /**
     * 根据线下面试ID删除音频文件
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    @Override
    public int deleteByOfflineInterviewId(Long offlineInterviewId) {
        return audioFileMapper.deleteByOfflineInterviewId(offlineInterviewId);
    }
} 