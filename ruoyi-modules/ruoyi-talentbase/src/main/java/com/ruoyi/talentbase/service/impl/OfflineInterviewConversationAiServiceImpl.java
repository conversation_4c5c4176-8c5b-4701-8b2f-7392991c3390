package com.ruoyi.talentbase.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.OfflineInterviewConversationAiMapper;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationAi;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationAiService;

/**
 * 线下面试会话AI分析记录Service业务层处理
 */
@Service
public class OfflineInterviewConversationAiServiceImpl implements IOfflineInterviewConversationAiService {
    @Autowired
    private OfflineInterviewConversationAiMapper offlineInterviewConversationAiMapper;

    /**
     * 批量新增线下面试会话AI分析记录
     * 
     * @param aiList 线下面试会话AI分析记录列表
     * @return 结果
     */
    @Override
    public int batchInsert(List<OfflineInterviewConversationAi> aiList) {
        return offlineInterviewConversationAiMapper.batchInsert(aiList);
    }

    /**
     * 根据线下面试ID删除会话AI
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    @Override
    public int deleteByOfflineInterviewId(Long offlineInterviewId) {
        return offlineInterviewConversationAiMapper.deleteByOfflineInterviewId(offlineInterviewId);
    }
} 