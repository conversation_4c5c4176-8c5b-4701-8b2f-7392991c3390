package com.ruoyi.talentbase.service.impl;

import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.OfflineInterviewConversationMapper;
import com.ruoyi.talentbase.domain.OfflineInterviewConversation;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationSetting;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewConversationVO;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationSettingService;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import java.util.Map;

/**
 * 线下面试会话Service业务层处理
 */
@Service
public class OfflineInterviewConversationServiceImpl implements IOfflineInterviewConversationService {
    @Autowired
    private OfflineInterviewConversationMapper conversationMapper;

    @Autowired
    private IOfflineInterviewConversationSettingService conversationSettingService;

    /**
     * 查询线下面试会话VO列表
     * 
     * @param conversation 查询条件
     * @return 线下面试会话VO列表
     */
    @Override
    public List<OfflineInterviewConversationVO> selectOfflineInterviewConversationVOList(OfflineInterviewConversation conversation) {
        List<OfflineInterviewConversation> list = conversationMapper.selectByOfflineInterviewId(conversation.getOfflineInterviewId());
        return convertToVOList(list);
    }

    /**
     * 根据面试ID查询会话列表
     * 
     * @param offlineInterviewId 面试ID
     * @return 会话列表
     */
    @Override
    public List<OfflineInterviewConversation> selectByOfflineInterviewId(Long offlineInterviewId) {
        return conversationMapper.selectByOfflineInterviewId(offlineInterviewId);
    }

    /**
     * 批量新增线下面试会话
     * 
     * @param conversationList 会话列表
     * @return 结果
     */
    @Override
    public int batchInsert(List<OfflineInterviewConversation> conversationList) {
        return conversationMapper.batchInsert(conversationList);
    }

    /**
     * 根据线下面试ID删除会话
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    @Override
    public int deleteByOfflineInterviewId(Long offlineInterviewId) {
        return conversationMapper.deleteByOfflineInterviewId(offlineInterviewId);
    }

    /**
     * 将会话列表转换为VO列表
     * 
     * @param list 会话列表
     * @return VO列表
     */
    private List<OfflineInterviewConversationVO> convertToVOList(List<OfflineInterviewConversation> list) {
        if (list.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取第一个会话的面试ID
        Long offlineInterviewId = list.get(0).getOfflineInterviewId();
        
        // 获取会话配置并创建角色映射
        List<OfflineInterviewConversationSetting> settings = conversationSettingService.selectByOfflineInterviewId(offlineInterviewId);
        Map<Integer, String> roleNameMap = settings.stream()
            .collect(Collectors.toMap(
                OfflineInterviewConversationSetting::getSpeakNum,
                OfflineInterviewConversationSetting::getSpeakRole
            ));

        // 转换会话列表为VO列表
        return list.stream()
            .map(conversation -> {
                OfflineInterviewConversationVO vo = new OfflineInterviewConversationVO();
                vo.setSpeakTxt(conversation.getSpeakTxt());
                vo.setTimestamp(conversation.getTimestamp());
                vo.setRoleName(roleNameMap.getOrDefault(conversation.getSpeakNum(), "未知"));
                return vo;
            })
            .collect(Collectors.toList());
    }
} 