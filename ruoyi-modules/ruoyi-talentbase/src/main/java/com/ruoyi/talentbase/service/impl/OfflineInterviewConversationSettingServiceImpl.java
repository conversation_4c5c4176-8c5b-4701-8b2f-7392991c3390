package com.ruoyi.talentbase.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.OfflineInterviewConversationSettingMapper;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationSetting;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationSettingService;

/**
 * 线下面试会话配置Service业务层处理
 */
@Service
public class OfflineInterviewConversationSettingServiceImpl implements IOfflineInterviewConversationSettingService {
    @Autowired
    private OfflineInterviewConversationSettingMapper offlineInterviewConversationSettingMapper;

    /**
     * 批量新增线下面试会话配置
     * 
     * @param settingList 线下面试会话配置列表
     * @return 结果
     */
    @Override
    public int batchInsert(List<OfflineInterviewConversationSetting> settingList) {
        if (settingList == null || settingList.isEmpty()) {
            return 0;
        }

        // 获取第一个配置的面试ID
        Long offlineInterviewId = settingList.get(0).getOfflineInterviewId();
        
        // 查询已存在的配置
        List<OfflineInterviewConversationSetting> existingSettings = offlineInterviewConversationSettingMapper.selectByOfflineInterviewId(offlineInterviewId);
        
        // 将已存在的配置转换为Map，key为speakNum
        Map<Integer, OfflineInterviewConversationSetting> existingMap = existingSettings.stream()
            .collect(Collectors.toMap(
                OfflineInterviewConversationSetting::getSpeakNum,
                setting -> setting
            ));

        // 分离需要更新和需要插入的配置
        List<OfflineInterviewConversationSetting> toUpdate = settingList.stream()
            .filter(setting -> existingMap.containsKey(setting.getSpeakNum()))
            .collect(Collectors.toList());

        List<OfflineInterviewConversationSetting> toInsert = settingList.stream()
            .filter(setting -> !existingMap.containsKey(setting.getSpeakNum()))
            .collect(Collectors.toList());

        int result = 0;
        
        // 执行更新操作
        if (!toUpdate.isEmpty()) {
            result += offlineInterviewConversationSettingMapper.batchUpdate(toUpdate);
        }
        
        // 执行插入操作
        if (!toInsert.isEmpty()) {
            result += offlineInterviewConversationSettingMapper.batchInsert(toInsert);
        }

        return result;
    }

    /**
     * 批量更新线下面试会话配置
     * 
     * @param settingList 线下面试会话配置列表
     * @return 结果
     */
    @Override
    public int batchUpdate(List<OfflineInterviewConversationSetting> settingList) {
        return offlineInterviewConversationSettingMapper.batchUpdate(settingList);
    }

    /**
     * 根据线下面试ID查询会话配置
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 会话配置列表
     */
    @Override
    public List<OfflineInterviewConversationSetting> selectByOfflineInterviewId(Long offlineInterviewId) {
        return offlineInterviewConversationSettingMapper.selectByOfflineInterviewId(offlineInterviewId);
    }

    /**
     * 根据线下面试ID删除会话配置
     * 
     * @param offlineInterviewId 线下面试ID
     * @return 结果
     */
    @Override
    public int deleteByOfflineInterviewId(Long offlineInterviewId) {
        return offlineInterviewConversationSettingMapper.deleteByOfflineInterviewId(offlineInterviewId);
    }
} 