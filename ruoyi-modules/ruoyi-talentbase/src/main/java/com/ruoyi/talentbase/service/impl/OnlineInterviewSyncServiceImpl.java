package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.TbInterviewJobPaper;
import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewSyncDTO;
import com.ruoyi.talentbase.domain.dto.JobPaperQuestionDTO;
import com.ruoyi.talentbase.domain.dto.InterviewDTO;
import com.ruoyi.talentbase.domain.dto.JobPaperDTO;
import com.ruoyi.talentbase.service.IOnlineInterviewSyncService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperService;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线上面试同步服务实现类
 */
@Service
public class OnlineInterviewSyncServiceImpl implements IOnlineInterviewSyncService {

    @Autowired
    private ITbOnlineInterviewService onlineInterviewService;

    @Autowired
    private ITbInterviewJobPaperService jobPaperService;

    @Autowired
    private ITbInterviewJobPaperQuestionService jobPaperQuestionService;

    @Override
    public OnlineInterviewSyncDTO getSyncData(Long interviewId) {
        // 1. 获取面试记录并转换为DTO
        TbOnlineInterview interview = onlineInterviewService.selectTbOnlineInterviewById(interviewId);
        if (interview == null) {
            return null;
        }
        InterviewDTO interviewDTO = new InterviewDTO();
        BeanUtils.copyProperties(interview, interviewDTO);

        // 2. 获取岗位试卷并转换为DTO
        TbInterviewJobPaper jobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
        if (jobPaper == null) {
            return null;
        }
        JobPaperDTO jobPaperDTO = new JobPaperDTO();
        BeanUtils.copyProperties(jobPaper, jobPaperDTO);

        // 3. 获取岗位试题（不包含答案）
        TbInterviewJobPaperQuestion questionQuery = new TbInterviewJobPaperQuestion();
        questionQuery.setJobPaperId(jobPaper.getId());
        List<TbInterviewJobPaperQuestion> questions = jobPaperQuestionService.selectTbInterviewJobPaperQuestionList(questionQuery);
        
        // 转换为不包含答案的DTO
        List<JobPaperQuestionDTO> questionDTOs = questions.stream().map(question -> {
            JobPaperQuestionDTO dto = new JobPaperQuestionDTO();
            BeanUtils.copyProperties(question, dto);
            return dto;
        }).collect(Collectors.toList());

        // 4. 组装同步数据
        OnlineInterviewSyncDTO syncDTO = new OnlineInterviewSyncDTO();
        syncDTO.setInterview(interviewDTO);
        syncDTO.setJobPaper(jobPaperDTO);
        syncDTO.setQuestions(questionDTOs);

        return syncDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncToExternal(OnlineInterviewSyncDTO syncDTO) {
        try {
            // TODO: 实现同步到外网的具体逻辑
            // 1. 调用外网API进行数据同步
            // 2. 处理同步结果
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 