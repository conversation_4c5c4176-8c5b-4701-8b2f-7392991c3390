package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbAnalyseCertificateInfo;
import com.ruoyi.talentbase.mapper.TbAnalyseCertificateInfoMapper;
import com.ruoyi.talentbase.service.ITbAnalyseCertificateInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TbAnalyseCertificateInfoServiceImpl implements ITbAnalyseCertificateInfoService {

    @Autowired
    private TbAnalyseCertificateInfoMapper mapper;

    @Override
    public TbAnalyseCertificateInfo selectTbAnalyseCertificateInfoById(Long id) {
        return mapper.selectTbAnalyseCertificateInfoById(id);
    }

    @Override
    public List<TbAnalyseCertificateInfo> selectTbAnalyseCertificateInfoList(TbAnalyseCertificateInfo info) {
        return mapper.selectTbAnalyseCertificateInfoList(info);
    }

    @Override
    public int insertTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertTbAnalyseCertificateInfo(info);
    }

    @Override
    public int updateTbAnalyseCertificateInfo(TbAnalyseCertificateInfo info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updateTbAnalyseCertificateInfo(info);
    }

    @Override
    public int deleteTbAnalyseCertificateInfoById(Long id) {
        return mapper.deleteTbAnalyseCertificateInfoById(id);
    }

    @Override
    public int deleteTbAnalyseCertificateInfoByIds(Long[] ids) {
        return mapper.deleteTbAnalyseCertificateInfoByIds(ids);
    }
} 