package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import com.ruoyi.talentbase.mapper.TbAnalyseEducationInfoMapper;
import com.ruoyi.talentbase.service.ITbAnalyseEducationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TbAnalyseEducationInfoServiceImpl implements ITbAnalyseEducationInfoService {

    @Autowired
    private TbAnalyseEducationInfoMapper mapper;

    @Override
    public TbAnalyseEducationInfo selectTbAnalyseEducationInfoById(Long id) {
        return mapper.selectTbAnalyseEducationInfoById(id);
    }

    @Override
    public List<TbAnalyseEducationInfo> selectTbAnalyseEducationInfoList(TbAnalyseEducationInfo info) {
        return mapper.selectTbAnalyseEducationInfoList(info);
    }

    @Override
    public int insertTbAnalyseEducationInfo(TbAnalyseEducationInfo info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertTbAnalyseEducationInfo(info);
    }

    @Override
    public int updateTbAnalyseEducationInfo(TbAnalyseEducationInfo info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updateTbAnalyseEducationInfo(info);
    }

    @Override
    public int deleteTbAnalyseEducationInfoById(Long id) {
        return mapper.deleteTbAnalyseEducationInfoById(id);
    }

    @Override
    public int deleteTbAnalyseEducationInfoByIds(Long[] ids) {
        return mapper.deleteTbAnalyseEducationInfoByIds(ids);
    }
} 