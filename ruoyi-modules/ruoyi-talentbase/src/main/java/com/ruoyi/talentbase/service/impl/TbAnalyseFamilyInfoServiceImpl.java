package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;
import com.ruoyi.talentbase.mapper.TbAnalyseFamilyInfoMapper;
import com.ruoyi.talentbase.service.ITbAnalyseFamilyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TbAnalyseFamilyInfoServiceImpl implements ITbAnalyseFamilyInfoService {

    @Autowired
    private TbAnalyseFamilyInfoMapper mapper;

    @Override
    public TbAnalyseFamilyInfo selectTbAnalyseFamilyInfoById(Long id) {
        return mapper.selectTbAnalyseFamilyInfoById(id);
    }

    @Override
    public List<TbAnalyseFamilyInfo> selectTbAnalyseFamilyInfoList(TbAnalyseFamilyInfo info) {
        return mapper.selectTbAnalyseFamilyInfoList(info);
    }

    @Override
    public int insertTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertTbAnalyseFamilyInfo(info);
    }

    @Override
    public int updateTbAnalyseFamilyInfo(TbAnalyseFamilyInfo info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updateTbAnalyseFamilyInfo(info);
    }

    @Override
    public int deleteTbAnalyseFamilyInfoById(Long id) {
        return mapper.deleteTbAnalyseFamilyInfoById(id);
    }

    @Override
    public int deleteTbAnalyseFamilyInfoByIds(Long[] ids) {
        return mapper.deleteTbAnalyseFamilyInfoByIds(ids);
    }
} 