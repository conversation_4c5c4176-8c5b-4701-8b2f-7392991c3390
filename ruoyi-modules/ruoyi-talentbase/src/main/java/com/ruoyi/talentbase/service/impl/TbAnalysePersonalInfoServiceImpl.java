package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbAnalysePersonalInfo;
import com.ruoyi.talentbase.domain.TbAnalyseEducationInfo;
import com.ruoyi.talentbase.domain.TbAnalyseFamilyInfo;
import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;
import com.ruoyi.talentbase.domain.vo.TbAnalysePersonalGetInfoVO;
import com.ruoyi.talentbase.mapper.TbAnalysePersonalInfoMapper;
import com.ruoyi.talentbase.service.ITbAnalysePersonalInfoService;
import com.ruoyi.talentbase.service.ITbAnalyseEducationInfoService;
import com.ruoyi.talentbase.service.ITbAnalyseFamilyInfoService;
import com.ruoyi.talentbase.service.ITbAnalyseWorkExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TbAnalysePersonalInfoServiceImpl implements ITbAnalysePersonalInfoService {

    @Autowired
    private TbAnalysePersonalInfoMapper mapper;

    @Autowired
    private ITbAnalyseEducationInfoService educationInfoService;
    @Autowired
    private ITbAnalyseFamilyInfoService familyInfoService;
    @Autowired
    private ITbAnalyseWorkExperienceService workExperienceService;

    @Override
    public TbAnalysePersonalInfo selectTbAnalysePersonalInfoById(Long id) {
        return mapper.selectTbAnalysePersonalInfoById(id);
    }

    @Override
    public List<TbAnalysePersonalInfo> selectTbAnalysePersonalInfoList(TbAnalysePersonalInfo info) {
        return mapper.selectTbAnalysePersonalInfoList(info);
    }

    @Override
    public int insertTbAnalysePersonalInfo(TbAnalysePersonalInfo info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertTbAnalysePersonalInfo(info);
    }

    @Override
    public int updateTbAnalysePersonalInfo(TbAnalysePersonalInfo info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updateTbAnalysePersonalInfo(info);
    }

    @Override
    public int deleteTbAnalysePersonalInfoById(Long id) {
        return mapper.deleteTbAnalysePersonalInfoById(id);
    }

    @Override
    public int deleteTbAnalysePersonalInfoByIds(Long[] ids) {
        return mapper.deleteTbAnalysePersonalInfoByIds(ids);
    }

    @Override
    public TbAnalysePersonalGetInfoVO selectTbAnalysePersonalGetInfoVoById(Long id) {
        TbAnalysePersonalInfo info = mapper.selectTbAnalysePersonalInfoById(id);
        if (info == null) {
            return null;
        }
        TbAnalysePersonalGetInfoVO vo = new TbAnalysePersonalGetInfoVO();
        // 基本信息
        vo.setId(info.getId());
        vo.setUserName(info.getUserName());
        vo.setSex(info.getSex());
        vo.setAge(info.getAge());
        vo.setEducation(info.getEducation());
        vo.setYearsOfExperience(info.getYearsOfExperience());
        vo.setPosition(info.getPosition());
        vo.setPhone(info.getPhone());
        vo.setEmail(info.getEmail());
        vo.setSalaryExpectation(info.getSalaryExpectation());
        vo.setTotalScore(info.getTotalScore() == null ? null : java.math.BigDecimal.valueOf(info.getTotalScore()));
        vo.setIntroduction(info.getIntroduction());
        vo.setFiledFlag(info.getFiledFlag());
        vo.setTalentPoolStatus(info.getTalentPoolStatus());
        // 教育信息
        TbAnalyseEducationInfo eduQuery = new TbAnalyseEducationInfo();
        eduQuery.setPersonalId(id);
        vo.setEducationInfoList(educationInfoService.selectTbAnalyseEducationInfoList(eduQuery));
        // 家庭信息
        TbAnalyseFamilyInfo famQuery = new TbAnalyseFamilyInfo();
        famQuery.setPersonalId(id);
        vo.setFamilyInfoList(familyInfoService.selectTbAnalyseFamilyInfoList(famQuery));
        // 工作经历
        TbAnalyseWorkExperience workQuery = new TbAnalyseWorkExperience();
        workQuery.setPersonalId(id);
        vo.setWorkExperienceList(workExperienceService.selectTbAnalyseWorkExperienceList(workQuery));
        return vo;
    }
} 