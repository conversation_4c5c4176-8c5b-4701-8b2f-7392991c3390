package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbAnalyseTask;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.mapper.TbAnalyseTaskMapper;
import com.ruoyi.talentbase.service.ITbAnalyseTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 简历分析任务Service业务层处理
 */
@Service
public class TbAnalyseTaskServiceImpl implements ITbAnalyseTaskService {

    @Autowired
    private TbAnalyseTaskMapper taskMapper;

    @Override
    public TbAnalyseTask selectTbAnalyseTaskById(Long id) {
        return taskMapper.selectTbAnalyseTaskById(id);
    }

    @Override
    public int insertTbAnalyseTask(TbAnalyseTask task) {
        task.setCreateTime(DateUtils.getNowDate());
        return taskMapper.insertTbAnalyseTask(task);
    }

    @Override
    public int updateTbAnalyseTask(TbAnalyseTask task) {
        task.setUpdateTime(DateUtils.getNowDate());
        return taskMapper.updateTbAnalyseTask(task);
    }

    @Override
    public int deleteTbAnalyseTaskByIds(Long[] ids) {
        return taskMapper.deleteTbAnalyseTaskByIds(ids);
    }

    @Override
    public int deleteTbAnalyseTaskById(Long id) {
        return taskMapper.deleteTbAnalyseTaskById(id);
    }

    @Override
    public List<TbAnalyseTask> selectTbAnalyseTaskList(AnalyseTaskQueryDTO query) {
        return taskMapper.selectTbAnalyseTaskListByQuery(query);
    }

    @Override
    public List<ResumeListVO> selectResumeList(ResumeListQueryDTO queryDTO) {
        return taskMapper.selectResumeList(queryDTO);
    }
} 