package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbAnalyseWorkExperience;
import com.ruoyi.talentbase.mapper.TbAnalyseWorkExperienceMapper;
import com.ruoyi.talentbase.service.ITbAnalyseWorkExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工作经历分析Service业务层处理
 */
@Service
public class TbAnalyseWorkExperienceServiceImpl implements ITbAnalyseWorkExperienceService {

    @Autowired
    private TbAnalyseWorkExperienceMapper mapper;

    @Override
    public TbAnalyseWorkExperience selectTbAnalyseWorkExperienceById(Long id) {
        return mapper.selectTbAnalyseWorkExperienceById(id);
    }

    @Override
    public List<TbAnalyseWorkExperience> selectTbAnalyseWorkExperienceList(TbAnalyseWorkExperience workExperience) {
        return mapper.selectTbAnalyseWorkExperienceList(workExperience);
    }

    @Override
    public int insertTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience) {
        workExperience.setCreateTime(DateUtils.getNowDate());
        return mapper.insertTbAnalyseWorkExperience(workExperience);
    }

    @Override
    public int insertBatch(List<TbAnalyseWorkExperience> workExperienceList) {
        if (workExperienceList != null && !workExperienceList.isEmpty()) {
            String username = SecurityUtils.getUsername();
            for (TbAnalyseWorkExperience workExperience : workExperienceList) {
                workExperience.setCreateTime(DateUtils.getNowDate());
                workExperience.setCreateBy(username);
            }
        }
        return mapper.insertBatch(workExperienceList);
    }

    @Override
    public int updateTbAnalyseWorkExperience(TbAnalyseWorkExperience workExperience) {
        workExperience.setUpdateTime(DateUtils.getNowDate());
        workExperience.setUpdateBy(SecurityUtils.getUsername());
        return mapper.updateTbAnalyseWorkExperience(workExperience);
    }

    @Override
    public int deleteTbAnalyseWorkExperienceById(Long id) {
        return mapper.deleteTbAnalyseWorkExperienceById(id);
    }

    @Override
    public int deleteTbAnalyseWorkExperienceByIds(Long[] ids) {
        return mapper.deleteTbAnalyseWorkExperienceByIds(ids);
    }

    @Override
    public int deleteTbAnalyseWorkExperienceByPersonalId(Long personalId) {
        return mapper.deleteTbAnalyseWorkExperienceByPersonalId(personalId);
    }
} 