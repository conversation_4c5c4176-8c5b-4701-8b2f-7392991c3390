package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbContractInfo;
import com.ruoyi.talentbase.mapper.TbContractInfoMapper;
import com.ruoyi.talentbase.service.ITbContractInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@Slf4j
public class TbContractInfoServiceImpl extends ServiceImpl<TbContractInfoMapper, TbContractInfo> implements ITbContractInfoService {

    @Override
    public List<TbContractInfo> selectList(TbContractInfo contractInfo) {
        LambdaQueryWrapper<TbContractInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbContractInfo::getUserId, contractInfo.getUserId());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public TbContractInfo selectById(Long id) {

        return baseMapper.selectById(id);
    }

    @Override
    public int insert(TbContractInfo userInfo) {
        return baseMapper.insert(userInfo);
    }

    @Override
    public int update(TbContractInfo userInfo) {
        return baseMapper.updateById(userInfo);
    }

    @Override
    public int delete(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public Integer selectEndTimeByUserId(Long userId) {
        return baseMapper.selectEndTimeByUserId(userId);
    }

    @Override
    public int deleteByUserInfoId(Long userId) {
        LambdaQueryWrapper<TbContractInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbContractInfo::getUserId, userId);
        return baseMapper.delete(queryWrapper);
    }
}
