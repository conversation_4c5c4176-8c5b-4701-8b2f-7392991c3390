package com.ruoyi.talentbase.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbWorkExperience;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbEducationInfoMapper;
import com.ruoyi.talentbase.domain.TbEducationInfo;
import com.ruoyi.talentbase.service.ITbEducationInfoService;

/**
 * 教育信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class TbEducationInfoServiceImpl implements ITbEducationInfoService {
    @Autowired
    private TbEducationInfoMapper tbEducationInfoMapper;

    /**
     * 查询教育信息
     *
     * @param id 教育信息主键
     * @return 教育信息
     */
    @Override
    public TbEducationInfo selectTbEducationInfoById(Long id) {
        return tbEducationInfoMapper.selectTbEducationInfoById(id);
    }

    /**
     * 查询教育信息列表
     *
     * @param tbEducationInfo 教育信息
     * @return 教育信息
     */
    @Override
    public List<TbEducationInfo> selectTbEducationInfoList(TbEducationInfo tbEducationInfo) {
        return tbEducationInfoMapper.selectTbEducationInfoList(tbEducationInfo);
    }

    /**
     * 新增教育信息
     *
     * @param tbEducationInfo 教育信息
     * @return 结果
     */
    @Override
    public int insertTbEducationInfo(TbEducationInfo tbEducationInfo) {
        tbEducationInfo.setCreateTime(DateUtils.getNowDate());
        return tbEducationInfoMapper.insertTbEducationInfo(tbEducationInfo);
    }

    /**
     * 修改教育信息
     *
     * @param tbEducationInfo 教育信息
     * @return 结果
     */
    @Override
    public int updateTbEducationInfo(TbEducationInfo tbEducationInfo) {
        tbEducationInfo.setUpdateTime(DateUtils.getNowDate());
        return tbEducationInfoMapper.updateTbEducationInfo(tbEducationInfo);
    }

    /**
     * 批量删除教育信息
     *
     * @param ids 需要删除的教育信息主键
     * @return 结果
     */
    @Override
    public int deleteTbEducationInfoByIds(Long[] ids) {
        return tbEducationInfoMapper.deleteTbEducationInfoByIds(ids);
    }

    /**
     * 删除教育信息信息
     *
     * @param id 教育信息主键
     * @return 结果
     */
    @Override
    public int deleteTbEducationInfoById(Long id) {
        return tbEducationInfoMapper.deleteTbEducationInfoById(id);
    }

    @Override
    public int deleteAndInsert(List<TbEducationInfo> educationInfos, Long personalId) {
        try {
            tbEducationInfoMapper.deleteByPersonalId(personalId);
            if (educationInfos != null && educationInfos.size() > 0) {
                for (TbEducationInfo workExperience : educationInfos) {
                    workExperience.setPersonalId(personalId);
                    workExperience.setCreateTime(DateUtils.getNowDate());
                }
                tbEducationInfoMapper.insertBatch(educationInfos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public int deleteByPersonalId(Long infoId) {
        return tbEducationInfoMapper.deleteByPersonalId(infoId);
    }

}
