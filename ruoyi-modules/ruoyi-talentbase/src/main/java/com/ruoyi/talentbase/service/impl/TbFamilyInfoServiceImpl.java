package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbFamilyInfo;
import com.ruoyi.talentbase.mapper.TbFamilyInfoMapper;
import com.ruoyi.talentbase.service.ITbFamilyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 家庭信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class TbFamilyInfoServiceImpl implements ITbFamilyInfoService {
    @Autowired
    private TbFamilyInfoMapper tbFamilyInfoMapper;

    /**
     * 查询家庭信息
     *
     * @param id 家庭信息主键
     * @return 家庭信息
     */
    @Override
    public TbFamilyInfo selectTbFamilyInfoById(Long id) {
        return tbFamilyInfoMapper.selectTbFamilyInfoById(id);
    }

    /**
     * 查询家庭信息列表
     *
     * @param tbFamilyInfo 家庭信息
     * @return 家庭信息
     */
    @Override
    public List<TbFamilyInfo> selectTbFamilyInfoList(TbFamilyInfo tbFamilyInfo) {
        return tbFamilyInfoMapper.selectTbFamilyInfoList(tbFamilyInfo);
    }

    /**
     * 新增家庭信息
     *
     * @param tbFamilyInfo 家庭信息
     * @return 结果
     */
    @Override
    public int insertTbFamilyInfo(TbFamilyInfo tbFamilyInfo) {
        tbFamilyInfo.setCreateTime(DateUtils.getNowDate());
        return tbFamilyInfoMapper.insertTbFamilyInfo(tbFamilyInfo);
    }

    /**
     * 修改家庭信息
     *
     * @param tbFamilyInfo 家庭信息
     * @return 结果
     */
    @Override
    public int updateTbFamilyInfo(TbFamilyInfo tbFamilyInfo) {
        tbFamilyInfo.setUpdateTime(DateUtils.getNowDate());
        return tbFamilyInfoMapper.updateTbFamilyInfo(tbFamilyInfo);
    }

    /**
     * 批量删除家庭信息
     *
     * @param ids 需要删除的家庭信息主键
     * @return 结果
     */
    @Override
    public int deleteTbFamilyInfoByIds(Long[] ids) {
        return tbFamilyInfoMapper.deleteTbFamilyInfoByIds(ids);
    }

    /**
     * 删除家庭信息信息
     *
     * @param id 家庭信息主键
     * @return 结果
     */
    @Override
    public int deleteTbFamilyInfoById(Long id) {
        return tbFamilyInfoMapper.deleteTbFamilyInfoById(id);
    }

    @Override
    public int deleteAndInsert(List<TbFamilyInfo> familyInfoList, Long personalInfoId) {
        try {
            tbFamilyInfoMapper.deleteTbFamilyInfoByPid(personalInfoId);
            if (familyInfoList != null && familyInfoList.size() > 0) {
                familyInfoList.forEach(item -> {
                    item.setPersonalId(personalInfoId);
                    item.setCreateTime(DateUtils.getNowDate());
                });
                tbFamilyInfoMapper.insertBatch(familyInfoList);
            }
        } catch (Exception e) {
            return 0;
        }
        return 1;
    }

    @Override
    public int deleteByPersonalId(Long infoId) {
        return tbFamilyInfoMapper.deleteTbFamilyInfoByPid(infoId);
    }
}
