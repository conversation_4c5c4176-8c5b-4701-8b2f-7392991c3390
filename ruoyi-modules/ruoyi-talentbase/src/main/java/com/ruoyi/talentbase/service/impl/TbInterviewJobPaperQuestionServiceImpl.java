package com.ruoyi.talentbase.service.impl;

import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.mapper.TbInterviewJobPaperQuestionMapper;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class TbInterviewJobPaperQuestionServiceImpl implements ITbInterviewJobPaperQuestionService {
    @Autowired
    private TbInterviewJobPaperQuestionMapper mapper;

    @Override
    public TbInterviewJobPaperQuestion selectTbInterviewJobPaperQuestionById(Long id) {
        return mapper.selectTbInterviewJobPaperQuestionById(id);
    }

    @Override
    public List<TbInterviewJobPaperQuestion> selectTbInterviewJobPaperQuestionList(TbInterviewJobPaperQuestion question) {
        return mapper.selectTbInterviewJobPaperQuestionList(question);
    }

    @Override
    public int insertTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question) {
        return mapper.insertTbInterviewJobPaperQuestion(question);
    }

    @Override
    public int updateTbInterviewJobPaperQuestion(TbInterviewJobPaperQuestion question) {
        return mapper.updateTbInterviewJobPaperQuestion(question);
    }

    @Override
    public int deleteTbInterviewJobPaperQuestionById(Long id) {
        return mapper.deleteTbInterviewJobPaperQuestionById(id);
    }

    @Override
    public int deleteTbInterviewJobPaperQuestionByIds(Long[] ids) {
        return mapper.deleteTbInterviewJobPaperQuestionByIds(ids);
    }

    @Override
    public int deleteTbInterviewJobPaperQuestionByJobPaperId(Long jobPaperId) {
        return mapper.deleteTbInterviewJobPaperQuestionByJobPaperId(jobPaperId);
    }

    @Override
    public List<TbInterviewJobPaperQuestion> selectByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return null;
        }
        return mapper.selectByIds(ids);
    }
} 