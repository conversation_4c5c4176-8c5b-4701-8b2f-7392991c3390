package com.ruoyi.talentbase.service.impl;

import com.ruoyi.talentbase.domain.TbInterviewJobPaper;
import com.ruoyi.talentbase.mapper.TbInterviewJobPaperMapper;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class TbInterviewJobPaperServiceImpl implements ITbInterviewJobPaperService {
    @Autowired
    private TbInterviewJobPaperMapper mapper;

    @Override
    public TbInterviewJobPaper selectTbInterviewJobPaperById(Long id) {
        return mapper.selectTbInterviewJobPaperById(id);
    }

    @Override
    public List<TbInterviewJobPaper> selectTbInterviewJobPaperList(TbInterviewJobPaper paper) {
        return mapper.selectTbInterviewJobPaperList(paper);
    }

    @Override
    public int insertTbInterviewJobPaper(TbInterviewJobPaper paper) {
        return mapper.insertTbInterviewJobPaper(paper);
    }

    @Override
    public int updateTbInterviewJobPaper(TbInterviewJobPaper paper) {
        return mapper.updateTbInterviewJobPaper(paper);
    }

    @Override
    public int deleteTbInterviewJobPaperById(Long id) {
        return mapper.deleteTbInterviewJobPaperById(id);
    }

    @Override
    public int deleteTbInterviewJobPaperByIds(Long[] ids) {
        return mapper.deleteTbInterviewJobPaperByIds(ids);
    }
} 