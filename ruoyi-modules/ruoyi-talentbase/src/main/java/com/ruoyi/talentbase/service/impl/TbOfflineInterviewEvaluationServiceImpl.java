package com.ruoyi.talentbase.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbOfflineInterviewEvaluationMapper;
import com.ruoyi.talentbase.domain.TbOfflineInterviewEvaluation;
import com.ruoyi.talentbase.service.ITbOfflineInterviewEvaluationService;

/**
 * 线下面试评价Service业务层处理
 */
@Service
public class TbOfflineInterviewEvaluationServiceImpl implements ITbOfflineInterviewEvaluationService {
    @Autowired
    private TbOfflineInterviewEvaluationMapper tbOfflineInterviewEvaluationMapper;

    /**
     * 查询线下面试评价
     */
    @Override
    public TbOfflineInterviewEvaluation selectTbOfflineInterviewEvaluationById(Long id) {
        return tbOfflineInterviewEvaluationMapper.selectTbOfflineInterviewEvaluationById(id);
    }

    /**
     * 查询线下面试评价列表
     */
    @Override
    public List<TbOfflineInterviewEvaluation> selectTbOfflineInterviewEvaluationList(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        return tbOfflineInterviewEvaluationMapper.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
    }

    /**
     * 新增线下面试评价
     */
    @Override
    public int insertTbOfflineInterviewEvaluation(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        return tbOfflineInterviewEvaluationMapper.insertTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
    }

    /**
     * 修改线下面试评价
     */
    @Override
    public int updateTbOfflineInterviewEvaluation(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        return tbOfflineInterviewEvaluationMapper.updateTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
    }

    /**
     * 批量删除线下面试评价
     */
    @Override
    public int deleteTbOfflineInterviewEvaluationByIds(Long[] ids) {
        return tbOfflineInterviewEvaluationMapper.deleteTbOfflineInterviewEvaluationByIds(ids);
    }

    /**
     * 删除线下面试评价信息
     */
    @Override
    public int deleteTbOfflineInterviewEvaluationById(Long id) {
        return tbOfflineInterviewEvaluationMapper.deleteTbOfflineInterviewEvaluationById(id);
    }

    /**
     * 根据线下面试ID删除评价
     */
    @Override
    public int deleteTbOfflineInterviewEvaluationByOfflineInterviewId(Long offlineInterviewId) {
        return tbOfflineInterviewEvaluationMapper.deleteTbOfflineInterviewEvaluationByOfflineInterviewId(offlineInterviewId);
    }
} 