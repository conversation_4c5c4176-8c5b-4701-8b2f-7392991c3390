package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbOfflineInterview;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.mapper.TbOfflineInterviewMapper;
import com.ruoyi.talentbase.service.ITbOfflineInterviewService;
import com.ruoyi.talentbase.service.ITbOfflineInterviewEvaluationService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationSettingService;
import com.ruoyi.talentbase.service.IOfflineInterviewAudioFileService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
public class TbOfflineInterviewServiceImpl implements ITbOfflineInterviewService {
    @Autowired
    private TbOfflineInterviewMapper mapper;

    @Autowired
    private ITbOfflineInterviewEvaluationService evaluationService;

    @Autowired
    private IOfflineInterviewConversationService conversationService;

    @Autowired
    private IOfflineInterviewConversationSettingService conversationSettingService;

    @Autowired
    private IOfflineInterviewAudioFileService audioFileService;

    @Autowired
    private IOfflineInterviewConversationAiService conversationAiService;

    @Override
    public TbOfflineInterview selectTbOfflineInterviewById(Long id) {
        return mapper.selectTbOfflineInterviewById(id);
    }

    @Override
    public List<TbOfflineInterview> selectTbOfflineInterviewList(OfflineInterviewQueryDTO queryDTO) {
        return mapper.selectTbOfflineInterviewList(queryDTO);
    }

    @Override
    public int insertTbOfflineInterview(TbOfflineInterview interview) {
        interview.setOperator(SecurityUtils.getNickname());
        interview.setCreateBy(SecurityUtils.getUsername());
        interview.setCreateTime(DateUtils.getNowDate());
        interview.setInterviewStatus(InterviewStatusEnum.NOT_STARTED.getCode());
        interview.setAvatarRedDot(1);
        return mapper.insertTbOfflineInterview(interview);
    }

    @Override
    public int updateTbOfflineInterview(TbOfflineInterview interview) {
        return mapper.updateTbOfflineInterview(interview);
    }

    @Override
    @Transactional
    public int deleteTbOfflineInterviewById(Long id) {
        // Delete related records first
        evaluationService.deleteTbOfflineInterviewEvaluationByOfflineInterviewId(id);
        conversationService.deleteByOfflineInterviewId(id);
        conversationSettingService.deleteByOfflineInterviewId(id);
        audioFileService.deleteByOfflineInterviewId(id);
        conversationAiService.deleteByOfflineInterviewId(id);
        
        // Delete the main record
        return mapper.deleteTbOfflineInterviewById(id);
    }

    @Override
    @Transactional
    public int deleteTbOfflineInterviewByIds(Long[] ids) {
        // Delete related records for each ID
        for (Long id : ids) {
            evaluationService.deleteTbOfflineInterviewEvaluationByOfflineInterviewId(id);
            conversationService.deleteByOfflineInterviewId(id);
            conversationSettingService.deleteByOfflineInterviewId(id);
            audioFileService.deleteByOfflineInterviewId(id);
            conversationAiService.deleteByOfflineInterviewId(id);
        }
        
        // Delete the main records
        return mapper.deleteTbOfflineInterviewByIds(ids);
    }

    @Override
    public int expireOfflineInterviewsBySource(Integer sourceType, Long sourceId) {
        TbOfflineInterview param = new TbOfflineInterview();
        param.setOfflineInterviewSource(sourceType);
        param.setOfflineInterviewSourceId(sourceId);
        param.setInterviewStatus(InterviewStatusEnum.EXPIRED.getCode());
        param.setUpdateBy(SecurityUtils.getUsername());
        param.setUpdateTime(DateUtils.getNowDate());
        return mapper.expireOfflineInterviewsBySource(param);
    }

    @Override
    public int deleteByPInfoId(Long infoId,TalentSourceEnum sourceEnum) {
        return mapper.deleteByPInfoId(infoId, sourceEnum.getCode());
    }
} 