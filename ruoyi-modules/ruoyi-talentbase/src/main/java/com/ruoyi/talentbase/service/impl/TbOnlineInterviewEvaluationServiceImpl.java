package com.ruoyi.talentbase.service.impl;

import com.ruoyi.talentbase.domain.TbOnlineInterviewEvaluation;
import com.ruoyi.talentbase.mapper.TbOnlineInterviewEvaluationMapper;
import com.ruoyi.talentbase.service.ITbOnlineInterviewEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 线上面试评价Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TbOnlineInterviewEvaluationServiceImpl implements ITbOnlineInterviewEvaluationService {
    @Autowired
    private TbOnlineInterviewEvaluationMapper tbOnlineInterviewEvaluationMapper;

    /**
     * 查询线上面试评价
     * 
     * @param id 线上面试评价主键
     * @return 线上面试评价
     */
    @Override
    public TbOnlineInterviewEvaluation selectTbOnlineInterviewEvaluationById(Long id) {
        return tbOnlineInterviewEvaluationMapper.selectTbOnlineInterviewEvaluationById(id);
    }

    /**
     * 查询线上面试评价列表
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 线上面试评价
     */
    @Override
    public List<TbOnlineInterviewEvaluation> selectTbOnlineInterviewEvaluationList(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        return tbOnlineInterviewEvaluationMapper.selectTbOnlineInterviewEvaluationList(tbOnlineInterviewEvaluation);
    }

    /**
     * 新增线上面试评价
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 结果
     */
    @Override
    public int insertTbOnlineInterviewEvaluation(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        return tbOnlineInterviewEvaluationMapper.insertTbOnlineInterviewEvaluation(tbOnlineInterviewEvaluation);
    }

    /**
     * 修改线上面试评价
     * 
     * @param tbOnlineInterviewEvaluation 线上面试评价
     * @return 结果
     */
    @Override
    public int updateTbOnlineInterviewEvaluation(TbOnlineInterviewEvaluation tbOnlineInterviewEvaluation) {
        return tbOnlineInterviewEvaluationMapper.updateTbOnlineInterviewEvaluation(tbOnlineInterviewEvaluation);
    }

    /**
     * 批量删除线上面试评价
     * 
     * @param ids 需要删除的线上面试评价主键
     * @return 结果
     */
    @Override
    public int deleteTbOnlineInterviewEvaluationByIds(Long[] ids) {
        return tbOnlineInterviewEvaluationMapper.deleteTbOnlineInterviewEvaluationByIds(ids);
    }

    /**
     * 删除线上面试评价信息
     * 
     * @param id 线上面试评价主键
     * @return 结果
     */
    @Override
    public int deleteTbOnlineInterviewEvaluationById(Long id) {
        return tbOnlineInterviewEvaluationMapper.deleteTbOnlineInterviewEvaluationById(id);
    }

    /**
     * 根据线上面试ID删除评价
     * 
     * @param onlineInterviewId 线上面试ID
     * @return 结果
     */
    @Override
    public int deleteTbOnlineInterviewEvaluationByOnlineInterviewId(Long onlineInterviewId) {
        return tbOnlineInterviewEvaluationMapper.deleteTbOnlineInterviewEvaluationByOnlineInterviewId(onlineInterviewId);
    }
} 