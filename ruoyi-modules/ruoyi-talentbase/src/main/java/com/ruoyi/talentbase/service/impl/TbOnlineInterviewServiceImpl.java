package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbOnlineInterview;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.mapper.TbOnlineInterviewMapper;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class TbOnlineInterviewServiceImpl implements ITbOnlineInterviewService {
    @Autowired
    private TbOnlineInterviewMapper mapper;

    @Override
    public TbOnlineInterview selectTbOnlineInterviewById(Long id) {
        return mapper.selectTbOnlineInterviewById(id);
    }

    @Override
    public List<TbOnlineInterview> selectTbOnlineInterviewList(OnlineInterviewQueryDTO queryDTO) {
        return mapper.selectTbOnlineInterviewList(queryDTO);
    }

    @Override
    public int insertTbOnlineInterview(TbOnlineInterview interview) {
        return mapper.insertTbOnlineInterview(interview);
    }

    @Override
    public int updateTbOnlineInterview(TbOnlineInterview interview) {
        return mapper.updateTbOnlineInterview(interview);
    }

    @Override
    public int deleteTbOnlineInterviewById(Long id) {
        return mapper.deleteTbOnlineInterviewById(id);
    }

    @Override
    public int deleteTbOnlineInterviewByIds(Long[] ids) {
        return mapper.deleteTbOnlineInterviewByIds(ids);
    }

    @Override
    public java.util.List<TbOnlineInterview> selectByInviteUrls(Collection<String> urls) {
        if (urls == null || urls.isEmpty()) {
            return Collections.emptyList();
        }
        return mapper.selectByInviteUrls(urls);
    }

    @Override
    public int expireOnlineInterviewsBySource(Integer sourceType, Long sourceId) {
        TbOnlineInterview param = new TbOnlineInterview();
        param.setOnlineInterviewSource(sourceType);
        param.setOnlineInterviewSourceId(sourceId);
        param.setInterviewStatus(InterviewStatusEnum.EXPIRED.getCode());
        param.setUpdateBy(SecurityUtils.getUsername());
        param.setUpdateTime(DateUtils.getNowDate());
        return mapper.expireOnlineInterviewsBySource(param);
    }

    // 新增方法，根据 infoId 删除面试记录
    @Override
    public int deleteByPInfoId(Long infoId,TalentSourceEnum sourceEnum) {
        return mapper.deleteByPInfoId(infoId,sourceEnum.getCode());
    }
} 