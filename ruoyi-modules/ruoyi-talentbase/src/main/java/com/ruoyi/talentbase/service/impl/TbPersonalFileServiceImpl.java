package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbPersonalFile;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.mapper.TbPersonalFileMapper;
import com.ruoyi.talentbase.service.ITbPersonalFileService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 个人档案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class TbPersonalFileServiceImpl extends ServiceImpl<TbPersonalFileMapper, TbPersonalFile> implements ITbPersonalFileService {
    /**
     * 查询个人档案
     *
     * @param id 个人档案主键
     * @return 个人档案
     */
    @Override
    public TbPersonalFile selectTbPersonalFileById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询个人档案列表
     *
     * @param tbPersonalFile 个人档案
     * @return 个人档案
     */
    @Override
    public List<TbPersonalFile> selectTbPersonalFileList(TbPersonalFile tbPersonalFile) {
        return baseMapper.selectTbPersonalFileList(tbPersonalFile);
    }

    /**
     * 新增个人档案
     *
     * @param tbPersonalFile 个人档案
     * @return 结果
     */
    @Override
    public int insertTbPersonalFile(TbPersonalFile tbPersonalFile) {
        tbPersonalFile.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(tbPersonalFile);
    }

    /**
     * 修改个人档案
     *
     * @param tbPersonalFile 个人档案
     * @return 结果
     */
    @Override
    public int updateTbPersonalFile(TbPersonalFile tbPersonalFile) {
        tbPersonalFile.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(tbPersonalFile);
    }

    /**
     * 批量删除个人档案
     *
     * @param ids 需要删除的个人档案主键
     * @return 结果
     */
    @Override
    public int deleteTbPersonalFileByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 删除个人档案信息
     *
     * @param id 个人档案主键
     * @return 结果
     */
    @Override
    public int deleteTbPersonalFileById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public List<TbPersonalFile> selectTbPersonalFileByIds(Long[] ids) {
        return baseMapper.selectBatchIds(Arrays.asList(ids));
    }

    /**
     * 批量插入个人文件
     *
     * @param personalFileList 个人文件列表
     * @param personalId       个人ID
     * @param fileSourceEnum   文件来源枚举
     * @return 结果
     */
    @Override
    public int deleteAndInsert(List<TbPersonalFile> personalFileList, Long personalId, FileSourceEnum fileSourceEnum) {
        return deleteAndInsert(personalFileList, personalId, null, fileSourceEnum);
    }


    /**
     * 批量插入个人文件
     *
     * @param personalFileList 个人文件列表
     * @param personalId       个人ID
     * @param fileSourceEnum   文件来源枚举
     * @return 结果
     */
    @Override
    public int deleteAndInsert(List<TbPersonalFile> personalFileList, Long personalId, Long undergoId, FileSourceEnum fileSourceEnum) {
        // 先删除原来的文件
        LambdaQueryWrapper<TbPersonalFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPersonalFile :: getPersonalId, personalId);
        queryWrapper.eq(undergoId != null, TbPersonalFile :: getUndergoId, undergoId);
        queryWrapper.eq(TbPersonalFile :: getFileSource, fileSourceEnum.getCode());
        try {
            baseMapper.delete(queryWrapper);
            if (personalFileList != null && personalFileList.size() > 0) {
                personalFileList.forEach(item -> {
                    item.setPersonalId(personalId);
                    item.setUndergoId(undergoId);
                    item.setFileSource(fileSourceEnum.getCode());
                    item.setUploadTime(DateUtils.getNowDate());
                    item.setCreateTime(DateUtils.getNowDate());
                });
                baseMapper.insertBatch(personalFileList);
            }
        } catch (Exception e) {
            return 0;
        }
        return 1;
    }

    @Override
    public int insertBatch(List<TbPersonalFile> personalFileList, Long personalId, Long undergoId, FileSourceEnum fileSourceEnum) {
        if (personalFileList != null && personalFileList.size() > 0) {
            personalFileList.forEach(item -> {
                item.setPersonalId(personalId);
                item.setUndergoId(undergoId);
                item.setFileSource(fileSourceEnum.getCode());
                item.setUploadTime(DateUtils.getNowDate());
                item.setCreateTime(DateUtils.getNowDate());
            });
            return baseMapper.insertBatch(personalFileList);
        }
        return 0;
    }

    @Override
    public int deleteByPersonalId(Long infoId) {
        // 先删除原来的文件
        LambdaQueryWrapper<TbPersonalFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPersonalFile :: getPersonalId, infoId);
        return baseMapper.delete(queryWrapper);
    }
}
