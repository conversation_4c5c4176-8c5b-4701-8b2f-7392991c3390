package com.ruoyi.talentbase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbTaskEducationInfoMapper;
import com.ruoyi.talentbase.domain.TbTaskEducationInfo;
import com.ruoyi.talentbase.service.ITbTaskEducationInfoService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class TbTaskEducationInfoServiceImpl implements ITbTaskEducationInfoService 
{
    @Autowired
    private TbTaskEducationInfoMapper tbEducationInfoMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TbTaskEducationInfo selectTbTaskEducationInfoById(Long id)
    {
        return tbEducationInfoMapper.selectTbTaskEducationInfoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TbTaskEducationInfo> selectTbTaskEducationInfoList(TbTaskEducationInfo tbEducationInfo)
    {
        return tbEducationInfoMapper.selectTbTaskEducationInfoList(tbEducationInfo);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTbTaskEducationInfo(TbTaskEducationInfo tbEducationInfo)
    {
        tbEducationInfo.setCreateTime(DateUtils.getNowDate());
        return tbEducationInfoMapper.insertTbTaskEducationInfo(tbEducationInfo);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbEducationInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTbTaskEducationInfo(TbTaskEducationInfo tbEducationInfo)
    {
        tbEducationInfo.setUpdateTime(DateUtils.getNowDate());
        return tbEducationInfoMapper.updateTbTaskEducationInfo(tbEducationInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskEducationInfoByIds(Long[] ids)
    {
        return tbEducationInfoMapper.deleteTbTaskEducationInfoByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskEducationInfoById(Long id)
    {
        return tbEducationInfoMapper.deleteTbTaskEducationInfoById(id);
    }
}
