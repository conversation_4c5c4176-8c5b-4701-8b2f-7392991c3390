package com.ruoyi.talentbase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbTaskFamilyInfoMapper;
import com.ruoyi.talentbase.domain.TbTaskFamilyInfo;
import com.ruoyi.talentbase.service.ITbTaskFamilyInfoService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class TbTaskFamilyInfoServiceImpl implements ITbTaskFamilyInfoService 
{
    @Autowired
    private TbTaskFamilyInfoMapper tbFamilyInfoMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TbTaskFamilyInfo selectTbTaskFamilyInfoById(Long id)
    {
        return tbFamilyInfoMapper.selectTbTaskFamilyInfoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TbTaskFamilyInfo> selectTbTaskFamilyInfoList(TbTaskFamilyInfo tbFamilyInfo)
    {
        return tbFamilyInfoMapper.selectTbTaskFamilyInfoList(tbFamilyInfo);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTbTaskFamilyInfo(TbTaskFamilyInfo tbFamilyInfo)
    {
        tbFamilyInfo.setCreateTime(DateUtils.getNowDate());
        return tbFamilyInfoMapper.insertTbTaskFamilyInfo(tbFamilyInfo);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbFamilyInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTbTaskFamilyInfo(TbTaskFamilyInfo tbFamilyInfo)
    {
        tbFamilyInfo.setUpdateTime(DateUtils.getNowDate());
        return tbFamilyInfoMapper.updateTbTaskFamilyInfo(tbFamilyInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskFamilyInfoByIds(Long[] ids)
    {
        return tbFamilyInfoMapper.deleteTbTaskFamilyInfoByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskFamilyInfoById(Long id)
    {
        return tbFamilyInfoMapper.deleteTbTaskFamilyInfoById(id);
    }
}
