package com.ruoyi.talentbase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbTaskPersonalFileMapper;
import com.ruoyi.talentbase.domain.TbTaskPersonalFile;
import com.ruoyi.talentbase.service.ITbTaskPersonalFileService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class TbTaskPersonalFileServiceImpl implements ITbTaskPersonalFileService 
{
    @Autowired
    private TbTaskPersonalFileMapper tbPersonalFileMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TbTaskPersonalFile selectTbTaskPersonalFileById(Long id)
    {
        return tbPersonalFileMapper.selectTbTaskPersonalFileById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TbTaskPersonalFile> selectTbTaskPersonalFileList(TbTaskPersonalFile tbPersonalFile)
    {
        return tbPersonalFileMapper.selectTbTaskPersonalFileList(tbPersonalFile);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTbTaskPersonalFile(TbTaskPersonalFile tbPersonalFile)
    {
        tbPersonalFile.setCreateTime(DateUtils.getNowDate());
        return tbPersonalFileMapper.insertTbTaskPersonalFile(tbPersonalFile);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbPersonalFile 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTbTaskPersonalFile(TbTaskPersonalFile tbPersonalFile)
    {
        tbPersonalFile.setUpdateTime(DateUtils.getNowDate());
        return tbPersonalFileMapper.updateTbTaskPersonalFile(tbPersonalFile);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskPersonalFileByIds(Long[] ids)
    {
        return tbPersonalFileMapper.deleteTbTaskPersonalFileByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskPersonalFileById(Long id)
    {
        return tbPersonalFileMapper.deleteTbTaskPersonalFileById(id);
    }
}
