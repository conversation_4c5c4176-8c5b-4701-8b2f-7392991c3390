package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.talentbase.domain.TbTaskPersonalInfo;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.vo.TbTaskPersonalInfoVo;
import com.ruoyi.talentbase.mapper.TbTaskPersonalInfoMapper;
import com.ruoyi.talentbase.service.ITbTaskPersonalInfoService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class TbTaskPersonalInfoServiceImpl extends ServiceImpl<TbTaskPersonalInfoMapper, TbTaskPersonalInfo> implements ITbTaskPersonalInfoService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TbTaskPersonalInfo selectTbTaskPersonalInfoById(Long id) {
        return baseMapper.selectTbTaskPersonalInfoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TbTaskPersonalInfo> selectTbTaskPersonalInfoList(TbTaskPersonalInfo tbPersonalInfo) {
        LambdaQueryWrapper<TbTaskPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(tbPersonalInfo.getUserName()), TbTaskPersonalInfo :: getUserName, tbPersonalInfo.getUserName());
        queryWrapper.eq(StringUtils.isNotEmpty(tbPersonalInfo.getSex()), TbTaskPersonalInfo :: getSex, tbPersonalInfo.getSex());
        queryWrapper.eq(tbPersonalInfo.getJobId() != null, TbTaskPersonalInfo :: getJobId, tbPersonalInfo.getJobId());
        queryWrapper.eq(tbPersonalInfo.getAge() != null, TbTaskPersonalInfo :: getAge, tbPersonalInfo.getAge());
        queryWrapper.eq(StringUtils.isNotEmpty(tbPersonalInfo.getEthnicity()), TbTaskPersonalInfo :: getEthnicity, tbPersonalInfo.getEthnicity());
        queryWrapper.eq(tbPersonalInfo.getFiledFlag() != null, TbTaskPersonalInfo :: getFiledFlag, tbPersonalInfo.getFiledFlag());
        queryWrapper.eq(tbPersonalInfo.getTalentPoolStatus() != null, TbTaskPersonalInfo :: getTalentPoolStatus, tbPersonalInfo.getTalentPoolStatus());
        queryWrapper.orderByDesc(TbTaskPersonalInfo :: getCreateTime);
        return baseMapper.selectTbTaskPersonalInfoList(tbPersonalInfo);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo) {
        tbPersonalInfo.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(tbPersonalInfo);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param tbPersonalInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTbTaskPersonalInfo(TbTaskPersonalInfo tbPersonalInfo) {
        tbPersonalInfo.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(tbPersonalInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskPersonalInfoByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public List<TbTaskPersonalInfoVo> selectTbTaskPersonalInfoVoList(TbTaskPersonalInfoParamDto tbPersonalInfo) {
        return baseMapper.selectTbTaskPersonalInfoVoList(tbPersonalInfo);
    }

    /**
     * 批量修改人才库状态
     *
     * @param ids
     */
    @Override
    public int batchEditTalentPoolStatus(Long[] ids) {
        LambdaUpdateWrapper<TbTaskPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TbTaskPersonalInfo :: getId, Arrays.asList(ids));
        updateWrapper.set(TbTaskPersonalInfo :: getTalentPoolStatus, 1);
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<TbTaskPersonalInfo> selectTTPInfoListByStatusOrFlag(TbTaskPersonalInfo tbPersonalInfo) {
        LambdaQueryWrapper<TbTaskPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbTaskPersonalInfo::getFiledFlag, tbPersonalInfo.getFiledFlag())
                .or()
                .eq(TbTaskPersonalInfo::getTalentPoolStatus, tbPersonalInfo.getTalentPoolStatus())
                .orderByDesc(TbTaskPersonalInfo::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public int editEntryById(Long[] id) {
        LambdaUpdateWrapper<TbTaskPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TbTaskPersonalInfo :: getId, Arrays.asList(id));
        updateWrapper.set(TbTaskPersonalInfo :: getFiledFlag, 2);
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<TbTaskPersonalInfo> selectListByIds(Long[] ids) {
        return baseMapper.selectBatchIds(Arrays.asList(ids));
    }
}
