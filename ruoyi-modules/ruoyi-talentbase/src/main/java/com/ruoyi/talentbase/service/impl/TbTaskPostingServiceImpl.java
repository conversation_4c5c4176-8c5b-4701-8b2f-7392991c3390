package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbTaskPosting;
import com.ruoyi.talentbase.mapper.TbTaskPostingMapper;
import com.ruoyi.talentbase.service.ITbTaskPostingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查找人才任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class TbTaskPostingServiceImpl implements ITbTaskPostingService {
    @Autowired
    private TbTaskPostingMapper tbJobPostingMapper;

    /**
     * 查询查找人才任务
     *
     * @param id 查找人才任务主键
     * @return 查找人才任务
     */
    @Override
    public TbTaskPosting selectTbTaskPostingById(Long id) {
        return tbJobPostingMapper.selectTbTaskPostingById(id);
    }

    /**
     * 查询查找人才任务列表
     *
     * @param tbJobPosting 查找人才任务
     * @return 查找人才任务
     */
    @Override
    public List<TbTaskPosting> selectTbTaskPostingList(TbTaskPosting tbJobPosting) {
        return tbJobPostingMapper.selectTbTaskPostingList(tbJobPosting);
    }

    /**
     * 新增查找人才任务
     *
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    @Override
    public int insertTbTaskPosting(TbTaskPosting tbJobPosting) {
        tbJobPosting.setCreateTime(DateUtils.getNowDate());
        return tbJobPostingMapper.insertTbTaskPosting(tbJobPosting);
    }

    /**
     * 修改查找人才任务
     *
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    @Override
    public int updateTbTaskPosting(TbTaskPosting tbJobPosting) {
        tbJobPosting.setUpdateTime(DateUtils.getNowDate());
        return tbJobPostingMapper.updateTbTaskPosting(tbJobPosting);
    }

    /**
     * 批量删除查找人才任务
     *
     * @param ids 需要删除的查找人才任务主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskPostingByIds(Long[] ids) {
        return tbJobPostingMapper.deleteTbTaskPostingByIds(ids);
    }

    /**
     * 删除查找人才任务信息
     *
     * @param id 查找人才任务主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskPostingById(Long id) {
        return tbJobPostingMapper.deleteTbTaskPostingById(id);
    }

    /**
     * 查询人才任务数量
     * @param userId
     * @return
     */
    @Override
    public Integer selectTbTaskPostingByUserId(Long userId) {
        return tbJobPostingMapper.selectTbTaskPostingByUserId(userId);
    }

}
