package com.ruoyi.talentbase.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbTaskWorkExperienceMapper;
import com.ruoyi.talentbase.domain.TbTaskWorkExperience;
import com.ruoyi.talentbase.service.ITbTaskWorkExperienceService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class TbTaskWorkExperienceServiceImpl implements ITbTaskWorkExperienceService 
{
    @Autowired
    private TbTaskWorkExperienceMapper tbWorkExperienceMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TbTaskWorkExperience selectTbTaskWorkExperienceById(Long id)
    {
        return tbWorkExperienceMapper.selectTbTaskWorkExperienceById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TbTaskWorkExperience> selectTbTaskWorkExperienceList(TbTaskWorkExperience tbWorkExperience)
    {
        return tbWorkExperienceMapper.selectTbTaskWorkExperienceList(tbWorkExperience);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTbTaskWorkExperience(TbTaskWorkExperience tbWorkExperience)
    {
        tbWorkExperience.setCreateTime(DateUtils.getNowDate());
        return tbWorkExperienceMapper.insertTbTaskWorkExperience(tbWorkExperience);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param tbWorkExperience 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTbTaskWorkExperience(TbTaskWorkExperience tbWorkExperience)
    {
        tbWorkExperience.setUpdateTime(DateUtils.getNowDate());
        return tbWorkExperienceMapper.updateTbTaskWorkExperience(tbWorkExperience);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskWorkExperienceByIds(Long[] ids)
    {
        return tbWorkExperienceMapper.deleteTbTaskWorkExperienceByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTbTaskWorkExperienceById(Long id)
    {
        return tbWorkExperienceMapper.deleteTbTaskWorkExperienceById(id);
    }

    /**
     * 根据个人信息id查询工作经历
     * @param pId
     * @return
     * */
    @Override
    public List<TbTaskWorkExperience> selectTbTaskWorkExperienceByPersonalId(Long pId) {
        return tbWorkExperienceMapper.selectTbTaskWorkExperienceByPersonalId(pId);
    }
}
