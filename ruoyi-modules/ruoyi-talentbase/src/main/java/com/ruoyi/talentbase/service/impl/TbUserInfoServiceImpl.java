package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.mapper.SysAdminUserMapper;
import com.ruoyi.talentbase.domain.TbUserInfo;
import com.ruoyi.talentbase.domain.TbWorkExperience;
import com.ruoyi.talentbase.domain.dto.TbUserInfoDto;
import com.ruoyi.talentbase.domain.vo.TbUserInfoVo;
import com.ruoyi.talentbase.mapper.TbUserInfoMapper;
import com.ruoyi.talentbase.mapper.TbWorkExperienceMapper;
import com.ruoyi.talentbase.service.ITbUserInfoService;
import com.ruoyi.talentbase.service.ITbWorkExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 工作经历Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class TbUserInfoServiceImpl extends ServiceImpl<TbUserInfoMapper, TbUserInfo> implements ITbUserInfoService {
    @Override
    public List<TbUserInfoVo> selectTbUserInfoVoList(TbUserInfoDto userInfo) {
        return baseMapper.selectTbUserInfoVoList(userInfo);
    }

    @Override
    public TbUserInfo selectTbUserInfoById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int insertTbUserInfo(TbUserInfo userInfo) {
        return baseMapper.insert(userInfo);
    }

    @Override
    public int updateTbUserInfo(TbUserInfo userInfo) {
        return baseMapper.updateById(userInfo);
    }

    @Override
    public int deleteTbUserInfoByIds(Long[] ids) {
        LambdaUpdateWrapper<TbUserInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TbUserInfo :: getId, ids);
        return baseMapper.delete(updateWrapper);
    }

    @Override
    public TbUserInfo selectByBasePersonalId(Long personalId) {
        LambdaQueryWrapper<TbUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbUserInfo :: getBasePersonalId, personalId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbUserInfo> selectByIds(Long[] ids) {

        return baseMapper.selectBatchIds(Arrays.asList(ids));
    }

    @Override
    public int updateTbUserLeaveInfo(TbUserInfo userInfo) {
        LambdaUpdateWrapper<TbUserInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbUserInfo :: getBasePersonalId, userInfo.getBasePersonalId());
        updateWrapper.set(TbUserInfo :: getDeptId, userInfo.getDeptId());
        updateWrapper.set(TbUserInfo :: getDepts, userInfo.getDepts());
        updateWrapper.set(TbUserInfo :: getPositionLevel, userInfo.getPositionLevel());
        updateWrapper.set(TbUserInfo :: getPosition, userInfo.getPosition());
        updateWrapper.set(userInfo.getLeaveTime() != null, TbUserInfo :: getLeaveTime, userInfo.getLeaveTime());
        updateWrapper.set(userInfo.getLeaveReason() != null, TbUserInfo :: getLeaveReason, userInfo.getLeaveReason());
        updateWrapper.set(userInfo.getFormalLeaveTime() != null, TbUserInfo :: getFormalLeaveTime, userInfo.getFormalLeaveTime());
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteTbUserInfoByPersonalIds(Long[] ids) {
        LambdaQueryWrapper<TbUserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbUserInfo :: getBasePersonalId, ids);
        return baseMapper.delete(wrapper);
    }

    @Override
    public Integer getUserCount(Long deptId) {
        LambdaQueryWrapper<TbUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbUserInfo :: getDeptId, deptId);
        Long count = baseMapper.selectCount(queryWrapper);
        return count.intValue();
    }
}
