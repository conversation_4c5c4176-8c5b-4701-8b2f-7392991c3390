package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.talentbase.domain.TbUserUndergo;
import com.ruoyi.talentbase.mapper.TbUserUndergoMapper;
import com.ruoyi.talentbase.service.ITbUserUndergoService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 用户档案信息Service业务层处理
 * <AUTHOR>
 */
@Service
public class TbUserUndergoServiceImpl extends ServiceImpl<TbUserUndergoMapper, TbUserUndergo> implements ITbUserUndergoService {

    @Override
    public List<TbUserUndergo> selectTbUserUndergoList(TbUserUndergo tbUserUndergo) {
        LambdaQueryWrapper<TbUserUndergo> queryWrapper = new LambdaQueryWrapper<>(tbUserUndergo);
        queryWrapper.orderByDesc(TbUserUndergo::getOperateTime).orderByDesc(TbUserUndergo::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public TbUserUndergo selectTbUserUndergoById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int insertTbUserUndergo(TbUserUndergo tbUserUndergo) {
        return baseMapper.insert(tbUserUndergo);
    }

    @Override
    public int updateTbUserUndergo(TbUserUndergo tbUserUndergo) {
        return baseMapper.updateById(tbUserUndergo);
    }

    @Override
    public int deleteTbUserUndergoByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public int deleteByPInfoId(Long infoId) {
        LambdaQueryWrapper<TbUserUndergo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbUserUndergo::getPersonalId, infoId);
        return baseMapper.delete(queryWrapper);
    }
}