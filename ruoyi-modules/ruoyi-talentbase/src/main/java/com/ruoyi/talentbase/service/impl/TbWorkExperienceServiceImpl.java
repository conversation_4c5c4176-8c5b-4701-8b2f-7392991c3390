package com.ruoyi.talentbase.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.talentbase.mapper.TbWorkExperienceMapper;
import com.ruoyi.talentbase.domain.TbWorkExperience;
import com.ruoyi.talentbase.service.ITbWorkExperienceService;

/**
 * 工作经历Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class TbWorkExperienceServiceImpl extends ServiceImpl<TbWorkExperienceMapper, TbWorkExperience> implements ITbWorkExperienceService {
    @Autowired
    private TbWorkExperienceMapper tbWorkExperienceMapper;

    /**
     * 查询工作经历
     *
     * @param id 工作经历主键
     * @return 工作经历
     */
    @Override
    public TbWorkExperience selectTbWorkExperienceById(Long id) {
        return tbWorkExperienceMapper.selectTbWorkExperienceById(id);
    }

    /**
     * 查询工作经历列表
     *
     * @param tbWorkExperience 工作经历
     * @return 工作经历
     */
    @Override
    public List<TbWorkExperience> selectTbWorkExperienceList(TbWorkExperience tbWorkExperience) {
        return tbWorkExperienceMapper.selectTbWorkExperienceList(tbWorkExperience);
    }

    /**
     * 新增工作经历
     *
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    @Override
    public int insertTbWorkExperience(TbWorkExperience tbWorkExperience) {
        tbWorkExperience.setCreateTime(DateUtils.getNowDate());
        return tbWorkExperienceMapper.insertTbWorkExperience(tbWorkExperience);
    }

    /**
     * 修改工作经历
     *
     * @param tbWorkExperience 工作经历
     * @return 结果
     */
    @Override
    public int updateTbWorkExperience(TbWorkExperience tbWorkExperience) {
        tbWorkExperience.setUpdateTime(DateUtils.getNowDate());
        return tbWorkExperienceMapper.updateTbWorkExperience(tbWorkExperience);
    }

    /**
     * 批量删除工作经历
     *
     * @param ids 需要删除的工作经历主键
     * @return 结果
     */
    @Override
    public int deleteTbWorkExperienceByIds(Long[] ids) {
        return tbWorkExperienceMapper.deleteTbWorkExperienceByIds(ids);
    }

    /**
     * 删除工作经历信息
     *
     * @param id 工作经历主键
     * @return 结果
     */
    @Override
    public int deleteTbWorkExperienceById(Long id) {
        return tbWorkExperienceMapper.deleteTbWorkExperienceById(id);
    }

    @Override
    public int deleteAndInsert(List<TbWorkExperience> taskPersonalInfoList, Long personalId) {
        LambdaQueryWrapper<TbWorkExperience> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbWorkExperience :: getPersonalId, personalId);
        try {
            baseMapper.delete(queryWrapper);
            if (taskPersonalInfoList != null && !taskPersonalInfoList.isEmpty()) {
                taskPersonalInfoList.forEach(item -> {
                    item.setPersonalId(personalId);
                    item.setCreateTime(DateUtils.getNowDate());
                });
                baseMapper.insertBatch(taskPersonalInfoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public int deleteByPersonalId(Long infoId) {

        LambdaQueryWrapper<TbWorkExperience> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbWorkExperience :: getPersonalId, infoId);
        try {
            return baseMapper.delete(queryWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
