<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.OfflineInterviewAudioFileMapper">
    
    <resultMap type="OfflineInterviewAudioFile" id="OfflineInterviewAudioFileResult">
        <result property="id" column="id"/>
        <result property="offlineInterviewId" column="offline_interview_id"/>
        <result property="audioFile" column="audio_file"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectOfflineInterviewAudioFileVo">
        select id, offline_interview_id, audio_file,
        create_by, create_time, update_by, update_time, remark
        from tb_offline_interview_audio_file
    </sql>

    <select id="selectOfflineInterviewAudioFileList" parameterType="OfflineInterviewAudioFile" resultMap="OfflineInterviewAudioFileResult">
        <include refid="selectOfflineInterviewAudioFileVo"/>
        <where>
            <if test="offlineInterviewId != null">and offline_interview_id = #{offlineInterviewId}</if>
        </where>
    </select>
    
    <insert id="insert" parameterType="OfflineInterviewAudioFile" useGeneratedKeys="true" keyProperty="id">
        insert into tb_offline_interview_audio_file (
            offline_interview_id, audio_file,
            create_by, create_time, update_by, update_time, remark
        ) values (
            #{offlineInterviewId}, #{audioFile},
            #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
        )
    </insert>

    <delete id="deleteByOfflineInterviewId" parameterType="Long">
        delete from tb_offline_interview_audio_file where offline_interview_id = #{offlineInterviewId}
    </delete>

</mapper> 