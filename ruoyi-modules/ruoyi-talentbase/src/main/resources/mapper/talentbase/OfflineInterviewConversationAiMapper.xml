<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.OfflineInterviewConversationAiMapper">
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into tb_offline_interview_conversation_ai (
            offline_interview_id, timestamp, ai_txt,
            create_by, create_time, update_by, update_time, remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.offlineInterviewId}, #{item.timestamp}, #{item.aiTxt},
            #{item.createBy}, sysdate(), #{item.updateBy}, sysdate(), #{item.remark}
            )
        </foreach>
    </insert>

    <delete id="deleteByOfflineInterviewId" parameterType="Long">
        delete from tb_offline_interview_conversation_ai
        where offline_interview_id = #{offlineInterviewId}
    </delete>

</mapper> 