<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.OfflineInterviewConversationMapper">
    
    <resultMap type="OfflineInterviewConversation" id="OfflineInterviewConversationResult">
        <result property="id" column="id"/>
        <result property="offlineInterviewId" column="offline_interview_id"/>
        <result property="speakNum" column="speak_num"/>
        <result property="speakTxt" column="speak_txt"/>
        <result property="speakTime" column="speak_time"/>
        <result property="timestamp" column="timestamp"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectOfflineInterviewConversationVo">
        select id, offline_interview_id, speak_num, speak_txt, speak_time, timestamp,
        create_by, create_time, update_by, update_time, remark
        from tb_offline_interview_conversation
    </sql>

    <select id="selectByOfflineInterviewId" parameterType="Long" resultMap="OfflineInterviewConversationResult">
        <include refid="selectOfflineInterviewConversationVo"/>
        where offline_interview_id = #{offlineInterviewId}
        order by timestamp asc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tb_offline_interview_conversation (
            offline_interview_id, speak_num, speak_txt, speak_time, timestamp,
            create_by, create_time, update_by, update_time, remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.offlineInterviewId}, #{item.speakNum}, #{item.speakTxt}, #{item.speakTime}, #{item.timestamp},
            #{item.createBy}, sysdate(), #{item.updateBy}, sysdate(), #{item.remark}
            )
        </foreach>
    </insert>

    <delete id="deleteByOfflineInterviewId" parameterType="Long">
        delete from tb_offline_interview_conversation where offline_interview_id = #{offlineInterviewId}
    </delete>

</mapper> 