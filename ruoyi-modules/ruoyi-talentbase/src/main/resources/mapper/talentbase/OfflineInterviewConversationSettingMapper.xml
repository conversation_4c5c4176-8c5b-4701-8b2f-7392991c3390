<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.OfflineInterviewConversationSettingMapper">
    
    <resultMap type="OfflineInterviewConversationSetting" id="OfflineInterviewConversationSettingResult">
        <result property="id" column="id"/>
        <result property="offlineInterviewId" column="offline_interview_id"/>
        <result property="speakNum" column="speak_num"/>
        <result property="speakRole" column="speak_role"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectOfflineInterviewConversationSettingVo">
        select id, offline_interview_id, speak_num, speak_role,
        create_by, create_time, update_by, update_time, remark
        from tb_offline_interview_conversation_setting
    </sql>

    <select id="selectByOfflineInterviewId" parameterType="Long" resultMap="OfflineInterviewConversationSettingResult">
        <include refid="selectOfflineInterviewConversationSettingVo"/>
        where offline_interview_id = #{offlineInterviewId}
        order by speak_num asc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tb_offline_interview_conversation_setting (
            offline_interview_id, speak_num, speak_role,
            create_by, create_time, update_by, update_time, remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.offlineInterviewId}, #{item.speakNum}, #{item.speakRole},
            #{item.createBy}, sysdate(), #{item.updateBy}, sysdate(), #{item.remark}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_offline_interview_conversation_setting
            <set>
                <if test="item.speakRole != null">speak_role = #{item.speakRole},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                update_time = sysdate(),
                <if test="item.remark != null">remark = #{item.remark},</if>
            </set>
            where offline_interview_id = #{item.offlineInterviewId} and speak_num = #{item.speakNum}
        </foreach>
    </update>

    <delete id="deleteByOfflineInterviewId" parameterType="Long">
        delete from tb_offline_interview_conversation_setting where offline_interview_id = #{offlineInterviewId}
    </delete>

</mapper> 