<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalyseCertificateInfoMapper">
    <resultMap id="TbAnalyseCertificateInfoResult" type="TbAnalyseCertificateInfo">
        <result column="id" property="id"/>
        <result column="personal_id" property="personalId"/>
        <result column="certificate_name" property="certificateName"/>
        <result column="file_url" property="fileUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, personal_id, certificate_name, file_url, create_by, create_time, update_by, update_time, remark
    </sql>

    <select id="selectTbAnalyseCertificateInfoList" parameterType="TbAnalyseCertificateInfo" resultMap="TbAnalyseCertificateInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_certificate_info
        <where>
            <if test="personalId!=null">AND personal_id = #{personalId}</if>
            <if test="certificateName!=null and certificateName!=''">AND certificate_name LIKE concat('%',#{certificateName},'%')</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectTbAnalyseCertificateInfoById" parameterType="Long" resultMap="TbAnalyseCertificateInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_certificate_info WHERE id = #{id}
    </select>

    <insert id="insertTbAnalyseCertificateInfo" parameterType="TbAnalyseCertificateInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_analyse_certificate_info (personal_id, certificate_name, file_url, create_by, create_time)
        VALUES (#{personalId}, #{certificateName}, #{fileUrl}, #{createBy}, #{createTime})
    </insert>

    <update id="updateTbAnalyseCertificateInfo" parameterType="TbAnalyseCertificateInfo">
        UPDATE tb_analyse_certificate_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateName!=null">certificate_name = #{certificateName},</if>
            <if test="fileUrl!=null">file_url = #{fileUrl},</if>
            <if test="updateBy!=null">update_by = #{updateBy},</if>
            <if test="updateTime!=null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbAnalyseCertificateInfoById" parameterType="Long">
        DELETE FROM tb_analyse_certificate_info WHERE id = #{id}
    </delete>

    <delete id="deleteTbAnalyseCertificateInfoByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_certificate_info WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
</mapper> 