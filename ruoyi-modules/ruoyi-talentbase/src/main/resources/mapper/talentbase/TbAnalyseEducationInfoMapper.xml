<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalyseEducationInfoMapper">
    <resultMap id="TbAnalyseEducationInfoResult" type="TbAnalyseEducationInfo">
        <result column="id" property="id"/>
        <result column="personal_id" property="personalId"/>
        <result column="education_level" property="educationLevel"/>
        <result column="graduation_school" property="graduationSchool"/>
        <result column="Introduction" property="introduction"/>
        <result column="major" property="major"/>
        <result column="skills" property="skills"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, personal_id, education_level, graduation_school, Introduction, major, skills, start_time, end_time, create_by, create_time, update_by, update_time, remark
    </sql>

    <select id="selectTbAnalyseEducationInfoList" parameterType="TbAnalyseEducationInfo" resultMap="TbAnalyseEducationInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_education_info
        <where>
            <if test="personalId!=null">AND personal_id = #{personalId}</if>
            <if test="educationLevel!=null and educationLevel!=''">AND education_level = #{educationLevel}</if>
        </where>
        ORDER BY start_time DESC
    </select>

    <select id="selectTbAnalyseEducationInfoById" parameterType="Long" resultMap="TbAnalyseEducationInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_education_info WHERE id = #{id}
    </select>

    <insert id="insertTbAnalyseEducationInfo" parameterType="TbAnalyseEducationInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_analyse_education_info (personal_id, education_level, graduation_school, Introduction, major, skills, start_time, end_time, create_by, create_time)
        VALUES (#{personalId}, #{educationLevel}, #{graduationSchool}, #{introduction}, #{major}, #{skills}, #{startTime}, #{endTime}, #{createBy}, #{createTime})
    </insert>

    <update id="updateTbAnalyseEducationInfo" parameterType="TbAnalyseEducationInfo">
        UPDATE tb_analyse_education_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="educationLevel!=null">education_level = #{educationLevel},</if>
            <if test="graduationSchool!=null">graduation_school = #{graduationSchool},</if>
            <if test="introduction!=null">Introduction = #{introduction},</if>
            <if test="major!=null">major = #{major},</if>
            <if test="skills!=null">skills = #{skills},</if>
            <if test="startTime!=null">start_time = #{startTime},</if>
            <if test="endTime!=null">end_time = #{endTime},</if>
            <if test="updateBy!=null">update_by = #{updateBy},</if>
            <if test="updateTime!=null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbAnalyseEducationInfoById" parameterType="Long">
        DELETE FROM tb_analyse_education_info WHERE id = #{id}
    </delete>

    <delete id="deleteTbAnalyseEducationInfoByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_education_info WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
</mapper> 