<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalyseFamilyInfoMapper">
    <resultMap id="TbAnalyseFamilyInfoResult" type="TbAnalyseFamilyInfo">
        <result column="id" property="id"/>
        <result column="personal_id" property="personalId"/>
        <result column="member_name" property="memberName"/>
        <result column="relationship" property="relationship"/>
        <result column="workplace" property="workplace"/>
        <result column="position" property="position"/>
        <result column="member_phone" property="memberPhone"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, personal_id, member_name, relationship, workplace, position, member_phone, create_by, create_time, update_by, update_time, remark
    </sql>

    <select id="selectTbAnalyseFamilyInfoList" parameterType="TbAnalyseFamilyInfo" resultMap="TbAnalyseFamilyInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_family_info
        <where>
            <if test="personalId!=null">AND personal_id = #{personalId}</if>
            <if test="memberName!=null and memberName!=''">AND member_name LIKE concat('%',#{memberName},'%')</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectTbAnalyseFamilyInfoById" parameterType="Long" resultMap="TbAnalyseFamilyInfoResult">
        SELECT <include refid="Base_Column_List"/> FROM tb_analyse_family_info WHERE id = #{id}
    </select>

    <insert id="insertTbAnalyseFamilyInfo" parameterType="TbAnalyseFamilyInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_analyse_family_info (personal_id, member_name, relationship, workplace, position, member_phone, create_by, create_time)
        VALUES (#{personalId}, #{memberName}, #{relationship}, #{workplace}, #{position}, #{memberPhone}, #{createBy}, #{createTime})
    </insert>

    <update id="updateTbAnalyseFamilyInfo" parameterType="TbAnalyseFamilyInfo">
        UPDATE tb_analyse_family_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberName!=null">member_name = #{memberName},</if>
            <if test="relationship!=null">relationship = #{relationship},</if>
            <if test="workplace!=null">workplace = #{workplace},</if>
            <if test="position!=null">position = #{position},</if>
            <if test="memberPhone!=null">member_phone = #{memberPhone},</if>
            <if test="updateBy!=null">update_by = #{updateBy},</if>
            <if test="updateTime!=null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbAnalyseFamilyInfoById" parameterType="Long">
        DELETE FROM tb_analyse_family_info WHERE id = #{id}
    </delete>

    <delete id="deleteTbAnalyseFamilyInfoByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_family_info WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
</mapper> 