<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbEducationInfoMapper">
    
    <resultMap type="TbEducationInfo" id="TbEducationInfoResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="educationLevel"    column="education_level"    />
        <result property="graduationSchool"    column="graduation_school"    />
        <result property="introduction"    column="Introduction"    />
        <result property="major"    column="major"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbEducationInfoVo">
        select id, personal_id, education_level, graduation_school, Introduction, major, start_time, end_time, create_by, create_time, update_by, update_time from tb_education_info
    </sql>

    <select id="selectTbEducationInfoList" parameterType="TbEducationInfo" resultMap="TbEducationInfoResult">
        <include refid="selectTbEducationInfoVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="educationLevel != null  and educationLevel != ''"> and education_level = #{educationLevel}</if>
            <if test="graduationSchool != null  and graduationSchool != ''"> and graduation_school = #{graduationSchool}</if>
            <if test="introduction != null  and introduction != ''"> and Introduction = #{introduction}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectTbEducationInfoById" parameterType="Long" resultMap="TbEducationInfoResult">
        <include refid="selectTbEducationInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbEducationInfo" parameterType="TbEducationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_education_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personalId != null">personal_id,</if>
            <if test="educationLevel != null">education_level,</if>
            <if test="graduationSchool != null">graduation_school,</if>
            <if test="introduction != null">Introduction,</if>
            <if test="major != null">major,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personalId != null">#{personalId},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
            <if test="graduationSchool != null">#{graduationSchool},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="major != null">#{major},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into tb_education_info (personal_id, education_level, graduation_school, Introduction, major, start_time,
        end_time, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.personalId}, #{item.educationLevel}, #{item.graduationSchool}, #{item.introduction}, #{item.major},
            #{item.startTime}, #{item.endTime}, #{item.createBy}, #{item.createTime}, #{item.updateBy},
            #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateTbEducationInfo" parameterType="TbEducationInfo">
        update tb_education_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
            <if test="graduationSchool != null">graduation_school = #{graduationSchool},</if>
            <if test="introduction != null">Introduction = #{introduction},</if>
            <if test="major != null">major = #{major},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbEducationInfoById" parameterType="Long">
        delete from tb_education_info where id = #{id}
    </delete>

    <delete id="deleteTbEducationInfoByIds" parameterType="String">
        delete from tb_education_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByPersonalId">
        delete from tb_education_info where personal_id = #{personalId}
    </delete>
</mapper>