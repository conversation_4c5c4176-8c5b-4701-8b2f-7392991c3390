<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbInterviewJobPaperMapper">
    <resultMap id="TbInterviewJobPaperResultMap" type="com.ruoyi.talentbase.domain.TbInterviewJobPaper">
        <id property="id" column="id"/>
        <result property="jobName" column="job_name"/>
        <result property="validDays" column="valid_days"/>
        <result property="openingRemark" column="opening_remark"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectTbInterviewJobPaperById" resultMap="TbInterviewJobPaperResultMap">
        SELECT * FROM tb_interview_job_paper WHERE id = #{id}
    </select>

    <select id="selectTbInterviewJobPaperList" resultMap="TbInterviewJobPaperResultMap">
        SELECT * FROM tb_interview_job_paper
        <where>
            <if test="jobName != null and jobName != ''">AND job_name LIKE CONCAT('%', #{jobName}, '%')</if>
            <if test="validDays != null">AND valid_days = #{validDays}</if>
        </where>
    </select>

    <insert id="insertTbInterviewJobPaper" parameterType="com.ruoyi.talentbase.domain.TbInterviewJobPaper" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_interview_job_paper
        (job_name, valid_days, opening_remark, remark, create_by, create_time, update_by, update_time)
        VALUES
        (#{jobName}, #{validDays}, #{openingRemark}, #{remark}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <update id="updateTbInterviewJobPaper" parameterType="com.ruoyi.talentbase.domain.TbInterviewJobPaper">
        UPDATE tb_interview_job_paper
        <set>
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="validDays != null">valid_days = #{validDays},</if>
            <if test="openingRemark != null">opening_remark = #{openingRemark},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbInterviewJobPaperById" parameterType="Long">
        DELETE FROM tb_interview_job_paper WHERE id = #{id}
    </delete>

    <delete id="deleteTbInterviewJobPaperByIds" parameterType="Long">
        DELETE FROM tb_interview_job_paper WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 