<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbOfflineInterviewEvaluationMapper">
    
    <resultMap type="TbOfflineInterviewEvaluation" id="TbOfflineInterviewEvaluationResult">
        <result property="id"    column="id"    />
        <result property="offlineInterviewId"    column="offline_interview_id"    />
        <result property="evaluationContent"    column="evaluation_content"    />
        <result property="evaluationType"    column="evaluation_type"    />
        <result property="score"    column="score"    />
        <result property="interviewerName"    column="interviewer_name"    />
        <result property="interviewerUserName"    column="interviewer_user_name"    />
        <result property="interviewerUserId"    column="interviewer_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTbOfflineInterviewEvaluationVo">
        select id, offline_interview_id, evaluation_content, evaluation_type, score, interviewer_name, interviewer_user_name, interviewer_user_id, create_by, create_time, update_by, update_time, remark from tb_offline_interview_evaluation
    </sql>

    <select id="selectTbOfflineInterviewEvaluationList" parameterType="TbOfflineInterviewEvaluation" resultMap="TbOfflineInterviewEvaluationResult">
        <include refid="selectTbOfflineInterviewEvaluationVo"/>
        <where>  
            <if test="offlineInterviewId != null "> and offline_interview_id = #{offlineInterviewId}</if>
            <if test="evaluationContent != null  and evaluationContent != ''"> and evaluation_content = #{evaluationContent}</if>
            <if test="evaluationType != null  and evaluationType != ''"> and evaluation_type = #{evaluationType}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="interviewerName != null  and interviewerName != ''"> and interviewer_name like concat('%', #{interviewerName}, '%')</if>
            <if test="interviewerUserName != null  and interviewerUserName != ''"> and interviewer_user_name like concat('%', #{interviewerUserName}, '%')</if>
            <if test="interviewerUserId != null "> and interviewer_user_id = #{interviewerUserId}</if>
        </where>
        order by evaluation_type asc, create_time asc
    </select>
    
    <select id="selectTbOfflineInterviewEvaluationById" parameterType="Long" resultMap="TbOfflineInterviewEvaluationResult">
        <include refid="selectTbOfflineInterviewEvaluationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbOfflineInterviewEvaluation" parameterType="TbOfflineInterviewEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into tb_offline_interview_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="offlineInterviewId != null">offline_interview_id,</if>
            <if test="evaluationContent != null">evaluation_content,</if>
            <if test="evaluationType != null">evaluation_type,</if>
            <if test="score != null">score,</if>
            <if test="interviewerName != null">interviewer_name,</if>
            <if test="interviewerUserName != null">interviewer_user_name,</if>
            <if test="interviewerUserId != null">interviewer_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="offlineInterviewId != null">#{offlineInterviewId},</if>
            <if test="evaluationContent != null">#{evaluationContent},</if>
            <if test="evaluationType != null">#{evaluationType},</if>
            <if test="score != null">#{score},</if>
            <if test="interviewerName != null">#{interviewerName},</if>
            <if test="interviewerUserName != null">#{interviewerUserName},</if>
            <if test="interviewerUserId != null">#{interviewerUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTbOfflineInterviewEvaluation" parameterType="TbOfflineInterviewEvaluation">
        update tb_offline_interview_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="offlineInterviewId != null">offline_interview_id = #{offlineInterviewId},</if>
            <if test="evaluationContent != null">evaluation_content = #{evaluationContent},</if>
            <if test="evaluationType != null">evaluation_type = #{evaluationType},</if>
            <if test="score != null">score = #{score},</if>
            <if test="interviewerName != null">interviewer_name = #{interviewerName},</if>
            <if test="interviewerUserName != null">interviewer_user_name = #{interviewerUserName},</if>
            <if test="interviewerUserId != null">interviewer_user_id = #{interviewerUserId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbOfflineInterviewEvaluationById" parameterType="Long">
        delete from tb_offline_interview_evaluation where id = #{id}
    </delete>

    <delete id="deleteTbOfflineInterviewEvaluationByIds" parameterType="String">
        delete from tb_offline_interview_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbOfflineInterviewEvaluationByOfflineInterviewId" parameterType="Long">
        delete from tb_offline_interview_evaluation where offline_interview_id = #{offlineInterviewId}
    </delete>
</mapper> 