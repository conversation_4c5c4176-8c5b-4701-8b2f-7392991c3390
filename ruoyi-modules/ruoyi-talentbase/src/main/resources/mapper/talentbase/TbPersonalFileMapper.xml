<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbPersonalFileMapper">
    
    <resultMap type="TbPersonalFile" id="TbPersonalFileResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="undergoId"    column="undergo_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbPersonalFileVo">
        select id,
               personal_id,
               undergo_id,
               file_name,
               file_size,
               file_url,
               upload_time,
               del_flag,
               file_type,
               file_source,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from tb_personal_file
    </sql>
    <insert id="insertBatch">
        insert into tb_personal_file (personal_id,undergo_id, file_name, file_size, file_url, upload_time, del_flag, file_type,
        file_source, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.personalId},#{item.undergoId},
             #{item.fileName}, #{item.fileSize}, #{item.fileUrl}, #{item.uploadTime},
            #{item.delFlag}, #{item.fileType}, #{item.fileSource}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <select id="selectTbPersonalFileList" parameterType="TbPersonalFile" resultMap="TbPersonalFileResult">
        <include refid="selectTbPersonalFileVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="undergoId!= null "> and undergo_id = #{undergoId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileSource != null "> and file_source = #{fileSource}</if>
            <if test="startTime != null"> and upload_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and upload_time &lt;= #{endTime}</if>
        </where>
        order by upload_time desc
    </select>
    
    <select id="selectTbPersonalFileById" parameterType="Long" resultMap="TbPersonalFileResult">
        <include refid="selectTbPersonalFileVo"/>
        where id = #{id}
    </select>
</mapper>