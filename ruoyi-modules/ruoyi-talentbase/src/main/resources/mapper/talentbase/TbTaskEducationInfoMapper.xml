<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbTaskEducationInfoMapper">
    
    <resultMap type="TbTaskEducationInfo" id="TbTaskEducationInfoResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="educationLevel"    column="education_level"    />
        <result property="graduationSchool"    column="graduation_school"    />
        <result property="introduction"    column="introduction"    />
        <result property="major"    column="major"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbTaskEducationInfoVo">
        select id,
               personal_id,
               education_level,
               graduation_school,
               introduction,
               major,
               start_time,
               end_time,
               create_by,
               create_time,
               update_by,
               update_time
        from tb_task_education_info
    </sql>

    <select id="selectTbTaskEducationInfoList" parameterType="TbTaskEducationInfo" resultMap="TbTaskEducationInfoResult">
        <include refid="selectTbTaskEducationInfoVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="educationLevel != null  and educationLevel != ''"> and education_level = #{educationLevel}</if>
            <if test="graduationSchool != null  and graduationSchool != ''"> and graduation_school = #{graduationSchool}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectTbTaskEducationInfoById" parameterType="Long" resultMap="TbTaskEducationInfoResult">
        <include refid="selectTbTaskEducationInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbTaskEducationInfo" parameterType="TbTaskEducationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_task_education_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personalId != null">personal_id,</if>
            <if test="educationLevel != null">education_level,</if>
            <if test="graduationSchool != null">graduation_school,</if>
            <if test="introduction != null">introduction,</if>
            <if test="major != null">major,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personalId != null">#{personalId},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
            <if test="graduationSchool != null">#{graduationSchool},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="major != null">#{major},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTbTaskEducationInfo" parameterType="TbTaskEducationInfo">
        update tb_task_education_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
            <if test="graduationSchool != null">graduation_school = #{graduationSchool},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="major != null">major = #{major},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbTaskEducationInfoById" parameterType="Long">
        delete from tb_task_education_info where id = #{id}
    </delete>

    <delete id="deleteTbTaskEducationInfoByIds" parameterType="String">
        delete from tb_task_education_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>