<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbTaskFamilyInfoMapper">
    
    <resultMap type="TbTaskFamilyInfo" id="TbTaskFamilyInfoResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="relationship"    column="relationship"    />
        <result property="workplace"    column="workplace"    />
        <result property="position"    column="position"    />
        <result property="memberPhone"    column="member_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbTaskFamilyInfoVo">
        select id, personal_id, member_name, relationship, workplace, position, member_phone, create_by, create_time, update_by, update_time from tb_task_family_info
    </sql>

    <select id="selectTbTaskFamilyInfoList" parameterType="TbTaskFamilyInfo" resultMap="TbTaskFamilyInfoResult">
        <include refid="selectTbTaskFamilyInfoVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="memberName != null  and memberName != ''"> and member_name like concat('%', #{memberName}, '%')</if>
            <if test="relationship != null  and relationship != ''"> and relationship = #{relationship}</if>
            <if test="workplace != null  and workplace != ''"> and workplace = #{workplace}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="memberPhone != null  and memberPhone != ''"> and member_phone = #{memberPhone}</if>
        </where>
    </select>
    
    <select id="selectTbTaskFamilyInfoById" parameterType="Long" resultMap="TbTaskFamilyInfoResult">
        <include refid="selectTbTaskFamilyInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbTaskFamilyInfo" parameterType="TbTaskFamilyInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_task_family_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personalId != null">personal_id,</if>
            <if test="memberName != null">member_name,</if>
            <if test="relationship != null">relationship,</if>
            <if test="workplace != null">workplace,</if>
            <if test="position != null">position,</if>
            <if test="memberPhone != null">member_phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personalId != null">#{personalId},</if>
            <if test="memberName != null">#{memberName},</if>
            <if test="relationship != null">#{relationship},</if>
            <if test="workplace != null">#{workplace},</if>
            <if test="position != null">#{position},</if>
            <if test="memberPhone != null">#{memberPhone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTbTaskFamilyInfo" parameterType="TbTaskFamilyInfo">
        update tb_task_family_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="memberName != null">member_name = #{memberName},</if>
            <if test="relationship != null">relationship = #{relationship},</if>
            <if test="workplace != null">workplace = #{workplace},</if>
            <if test="position != null">position = #{position},</if>
            <if test="memberPhone != null">member_phone = #{memberPhone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbTaskFamilyInfoById" parameterType="Long">
        delete from tb_task_family_info where id = #{id}
    </delete>

    <delete id="deleteTbTaskFamilyInfoByIds" parameterType="String">
        delete from tb_task_family_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>