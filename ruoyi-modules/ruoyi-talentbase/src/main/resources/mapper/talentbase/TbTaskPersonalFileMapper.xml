<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbTaskPersonalFileMapper">
    
    <resultMap type="TbTaskPersonalFile" id="TbTaskPersonalFileResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbTaskPersonalFileVo">
        select id,
               personal_id,
               file_name,
               file_size,
               file_url,
               upload_time,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from tb_task_personal_file
    </sql>

    <select id="selectTbTaskPersonalFileList" parameterType="TbTaskPersonalFile" resultMap="TbTaskPersonalFileResult">
        <include refid="selectTbTaskPersonalFileVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
        </where>
    </select>
    
    <select id="selectTbTaskPersonalFileById" parameterType="Long" resultMap="TbTaskPersonalFileResult">
        <include refid="selectTbTaskPersonalFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbTaskPersonalFile" parameterType="TbTaskPersonalFile" useGeneratedKeys="true" keyProperty="id">
        insert into tb_task_personal_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personalId != null">personal_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personalId != null">#{personalId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTbTaskPersonalFile" parameterType="TbTaskPersonalFile">
        update tb_task_personal_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbTaskPersonalFileById" parameterType="Long">
        delete from tb_task_personal_file where id = #{id}
    </delete>

    <delete id="deleteTbTaskPersonalFileByIds" parameterType="String">
        delete from tb_task_personal_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>