<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbTaskPersonalInfoMapper">

    <resultMap type="TbTaskPersonalInfo" id="TbTaskPersonalInfoResult">
        <result property="id" column="id"/>
        <result property="jobId" column="job_id"/>
        <result property="userName" column="user_name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="idCard" column="id_card"/>
        <result property="ethnicity" column="ethnicity"/>
        <result property="marriageStatus" column="marriage_status"/>
        <result property="yearsOfExperience" column="years_of_experience"/>
        <result property="education" column="education"/>
        <result property="schoolName" column="school_name"/>
        <result property="major" column="major"/>
        <result property="position" column="position"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="skills" column="skills"/>
        <result property="filedFlag" column="filed_flag"/>
        <result property="certificate" column="certificate"/>
        <result property="currentAddress" column="current_address"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="introduction" column="introduction"/>
        <result property="foreignProficiency" column="foreign_proficiency"/>
        <result property="professionalLevel" column="professional_level"/>
        <result property="jobIntent" column="job_intent"/>
        <result property="salaryExpectation" column="salary_expectation"/>
        <result property="recruitmentChannel" column="recruitment_channel"/>
        <result property="talentPoolStatus" column="talent_pool_status"/>
        <result property="minimumEducationScore" column="minimum_education_score"/>
        <result property="workExperienceScore" column="work_experience_score"/>
        <result property="jobHoppingRateScore" column="job_hopping_rate_score"/>
        <result property="salaryRangeScore" column="salary_range_score"/>
        <result property="totalScore" column="total_score"/>
        <result property="workStatus" column="work_status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTbTaskPersonalInfoVo">
        select id,
        job_id,
        user_name,
        sex,
        age,
        id_card,
        ethnicity,
        marriage_status,
        years_of_experience,
        education,
        school_name,
        major,
        position,
        phone,
        email,
        avatar,
        skills,
        certificate,
        current_address,
        political_status,
        filed_flag,
        introduction,
        foreign_proficiency,
        professional_level,
        job_intent,
        salary_expectation,
        recruitment_channel,
        talent_pool_status,
        minimum_education_score,
        work_experience_score,
        job_hopping_rate_score,
        salary_range_score,
        total_score,
        work_status,
        error_msg,
        create_by,
        create_time,
        update_by,
        update_time,
        remark
        from tb_task_personal_info
    </sql>

    <select id="selectTbTaskPersonalInfoList" parameterType="TbTaskPersonalInfo" resultMap="TbTaskPersonalInfoResult">
        <include refid="selectTbTaskPersonalInfoVo"/>
        <where>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="jobId != null">and job_id = #{jobId}</if>
            <if test="age != null">and age = #{age}</if>
            <if test="ethnicity != null  and ethnicity != ''">and ethnicity = #{ethnicity}</if>
            <if test="talentPoolStatus != null">and talent_pool_status = #{talentPoolStatus}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTbTaskPersonalInfoById" parameterType="Long" resultMap="TbTaskPersonalInfoResult">
        <include refid="selectTbTaskPersonalInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectTbTaskPersonalInfoVoList" resultType="com.ruoyi.talentbase.domain.vo.TbTaskPersonalInfoVo">
        select id, user_name, sex, age, years_of_experience, education, total_score, work_status,
        talent_pool_status,create_time, update_time, salary_expectation,error_msg,filed_flag,recruitment_channel,
        job_intent
        from tb_task_personal_info
        <where>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null and sex != ''">and sex = #{sex}</if>
            <if test="jobId != null">and job_id = #{jobId}</if>
            <if test="ageLowerBound != null">and age &gt;= #{ageLowerBound}</if>
            <if test="ageUpperBound != null">and age &lt;= #{ageUpperBound}</if>
            <if test="education != null">and education = #{education}</if>
            <if test="filedFlag!= null">and filed_flag = #{filedFlag}</if>
            <if test="talentPoolStatus != null  and talentPoolStatus != ''">and talent_pool_status =
                #{talentPoolStatus}
            </if>
        </where>
        <if test="orderField != null">
            <choose>
                <when test="orderField == 0">
                    order by total_score
                </when>
                <when test="orderField == 1">
                    order by years_of_experience
                </when>
                <when test="orderField == 2">
                    order by age
                </when>
            </choose>
            <choose>
                <when test="sort == 0">
                    asc
                </when>
                <otherwise>
                    desc
                </otherwise>
            </choose>
        </if>
    </select>

    <insert id="insertTbTaskPersonalInfo" parameterType="TbTaskPersonalInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_task_personal_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">job_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="idCard != null">id_card,</if>
            <if test="ethnicity != null">ethnicity,</if>
            <if test="marriageStatus != null">marriage_status,</if>
            <if test="yearsOfExperience != null">years_of_experience,</if>
            <if test="education != null">education,</if>
            <if test="schoolName != null">school_name,</if>
            <if test="major != null">major,</if>
            <if test="position!= null">position,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="skills != null">skills,</if>
            <if test="certificate != null">certificate,</if>
            <if test="currentAddress != null">current_address,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="introduction != null">introduction,</if>
            <if test="foreignProficiency != null">foreign_proficiency,</if>
            <if test="professionalLevel != null">professional_level,</if>
            <if test="jobIntent != null">job_intent,</if>
            <if test="salaryExpectation != null">salary_expectation,</if>
            <if test="recruitmentChannel != null">recruitment_channel,</if>
            <if test="talentPoolStatus != null">talent_pool_status,</if>
            <if test="minimumEducationScore != null">minimum_education_score,</if>
            <if test="workExperienceScore != null">work_experience_score,</if>
            <if test="jobHoppingRateScore != null">job_hopping_rate_score,</if>
            <if test="salaryRangeScore != null">salary_range_score,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="workStatus != null">work_status,</if>
            <if test="errorMsg!= null">error_msg,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">#{jobId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="ethnicity != null">#{ethnicity},</if>
            <if test="marriageStatus != null">#{marriageStatus},</if>
            <if test="yearsOfExperience != null">#{yearsOfExperience},</if>
            <if test="education != null">#{education},</if>
            <if test="schoolName != null">#{schoolName},</if>
            <if test="major != null">#{major},</if>
            <if test="position != null">#{position},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="skills != null">#{skills},</if>
            <if test="certificate != null">#{certificate},</if>
            <if test="currentAddress != null">#{currentAddress},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="foreignProficiency != null">#{foreignProficiency},</if>
            <if test="professionalLevel != null">#{professionalLevel},</if>
            <if test="jobIntent != null">#{jobIntent},</if>
            <if test="salaryExpectation != null">#{salaryExpectation},</if>
            <if test="recruitmentChannel != null">#{recruitmentChannel},</if>
            <if test="talentPoolStatus != null">#{talentPoolStatus},</if>
            <if test="minimumEducationScore != null">#{minimumEducationScore},</if>
            <if test="workExperienceScore != null">#{workExperienceScore},</if>
            <if test="jobHoppingRateScore != null">#{jobHoppingRateScore},</if>
            <if test="salaryRangeScore != null">#{salaryRangeScore},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="workStatus != null">#{workStatus},</if>
            <if test="errorMsg!= null">#{errorMsg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTbTaskPersonalInfo" parameterType="TbTaskPersonalInfo">
        update tb_task_personal_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="ethnicity != null">ethnicity = #{ethnicity},</if>
            <if test="marriageStatus != null">marriage_status = #{marriageStatus},</if>
            <if test="yearsOfExperience != null">years_of_experience = #{yearsOfExperience},</if>
            <if test="education != null">education = #{education},</if>
            <if test="schoolName != null">school_name = #{schoolName},</if>
            <if test="major != null">major = #{major},</if>
            <if test="position != null">position = #{position},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="skills != null">skills = #{skills},</if>
            <if test="certificate != null">certificate = #{certificate},</if>
            <if test="currentAddress != null">current_address = #{currentAddress},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="foreignProficiency != null">foreign_proficiency = #{foreignProficiency},</if>
            <if test="professionalLevel != null">professional_level = #{professionalLevel},</if>
            <if test="jobIntent != null">job_intent = #{jobIntent},</if>
            <if test="salaryExpectation != null">salary_expectation = #{salaryExpectation},</if>
            <if test="recruitmentChannel != null">recruitment_channel = #{recruitmentChannel},</if>
            <if test="talentPoolStatus != null">talent_pool_status = #{talentPoolStatus},</if>
            <if test="minimumEducationScore != null">minimum_education_score = #{minimumEducationScore},</if>
            <if test="workExperienceScore != null">work_experience_score = #{workExperienceScore},</if>
            <if test="jobHoppingRateScore != null">job_hopping_rate_score = #{jobHoppingRateScore},</if>
            <if test="salaryRangeScore != null">salary_range_score = #{salaryRangeScore},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="workStatus != null">work_status = #{workStatus},</if>
            <if test="errorMsg!= null">error_msg = #{errorMsg},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchEditTalentPoolStatus">
        update tb_task_personal_info set talent_pool_status = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteTbTaskPersonalInfoById" parameterType="Long">
        delete from tb_task_personal_info where id = #{id}
    </delete>

    <delete id="deleteTbTaskPersonalInfoByIds" parameterType="String">
        delete from tb_task_personal_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>