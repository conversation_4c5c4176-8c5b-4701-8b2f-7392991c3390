<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbUserInfoMapper">

    <resultMap id="TbUserInfoVoResultMap" type="com.ruoyi.talentbase.domain.vo.TbUserInfoVo">
        <id column="user_id" property="userId"/>
        <result column="base_personal_id" property="basePersonalId"/>
        <result column="dept_id" property="deptId"/>
        <result column="company_id" property="companyId"/>
        <result column="depts" property="depts"/>
        <result column="position" property="position"/>
        <result column="position_level" property="positionLevel"/>
        <result column="insurance_base" property="insuranceBase"/>
        <result column="insurance_ratio" property="insuranceRatio"/>
        <result column="salary" property="salary"/>
        <result column="entry_time" property="entryTime"/>
        <result column="turnover_time" property="turnoverTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- selectTbUserInfoVoList 因为信息的不确定性，采用聚合查询和子查询处理,后续如果数据量上去可以采用移除聚合查询加字段和加索引 -->
    <select id="selectTbUserInfoVoList" resultType="com.ruoyi.talentbase.domain.vo.TbUserInfoVo">
        SELECT
        tu.id,
        tu.dept_id,
        tu.position,
        tu.position_level,
        tpi.user_name,
        tpi.avatar,
        tpi.employment_status,
        IF
        ( tpi.update_time IS NULL, tpi.create_time, tpi.update_time ) AS updateTime,
        tpi.create_archives_time,
        tpi.id AS basePersonalId,
        tci.countdown
        FROM
        tb_personal_info tpi
        LEFT JOIN tb_user_info tu ON tpi.id = tu.base_personal_id
        LEFT JOIN ( SELECT user_id, DATEDIFF( MAX( end_time ), NOW()) AS countdown FROM tb_contract_info WHERE end_time
        IS NOT NULL GROUP BY user_id ) AS tci ON tci.user_id = tu.id
        <where>
            <if test="delFlag!= null">
                and tpi.del_flag = #{delFlag}
            </if>
            <if test="filedFlag!= null and filedFlag!= ''">
                and tpi.filed_flag = #{filedFlag}
            </if>
            <if test="userName != null and userName != ''">
                and tpi.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="deptId!= null and deptId!= ''">
                and (find_in_set(#{deptId}, tu.depts) or tu.dept_id = #{deptId})
            </if>
            <if test="employmentStatus!= null">
                and tpi.employment_status = #{employmentStatus}
            </if>
            <if test="positionLevel!= null and positionLevel!= ''">
                and tu.position_level like concat('%', #{positionLevel}, '%')
            </if>
            <if test="position!= null and position!= ''">
                and tu.position like concat('%', #{position}, '%')
            </if>
        </where>
        ORDER BY
        CASE
        WHEN tpi.employment_status IN ( 0, 5 ) THEN
        1 ELSE 0
        END,
        <if test="sort != null">
            <choose>
                <when test="sort == 0">
                    countdown asc,
                </when>
                <otherwise>
                    countdown desc,
                </otherwise>
            </choose>
        </if>
        tpi.create_archives_time desc
    </select>

</mapper>