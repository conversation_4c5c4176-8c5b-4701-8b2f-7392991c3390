<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbWorkExperienceMapper">
    
    <resultMap type="TbWorkExperience" id="TbWorkExperienceResult">
        <result property="id"    column="id"    />
        <result property="personalId"    column="personal_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="position"    column="position"    />
        <result property="resignationReason"    column="resignation_reason"    />
        <result property="workIntroduction"    column="work_Introduction"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTbWorkExperienceVo">
        select id, personal_id, company_name, position, resignation_reason, work_Introduction, start_time, end_time, create_by, create_time, update_by, update_time from tb_work_experience
    </sql>

    <select id="selectTbWorkExperienceList" parameterType="TbWorkExperience" resultMap="TbWorkExperienceResult">
        <include refid="selectTbWorkExperienceVo"/>
        <where>  
            <if test="personalId != null "> and personal_id = #{personalId}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="resignationReason != null  and resignationReason != ''"> and resignation_reason = #{resignationReason}</if>
            <if test="workIntroduction != null  and workIntroduction != ''"> and work_Introduction = #{workIntroduction}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectTbWorkExperienceById" parameterType="Long" resultMap="TbWorkExperienceResult">
        <include refid="selectTbWorkExperienceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTbWorkExperience" parameterType="TbWorkExperience" useGeneratedKeys="true" keyProperty="id">
        insert into tb_work_experience
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personalId != null">personal_id,</if>
            <if test="companyName != null">company_name,</if>
            <if test="position != null">position,</if>
            <if test="resignationReason != null">resignation_reason,</if>
            <if test="workIntroduction != null">work_Introduction,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personalId != null">#{personalId},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="position != null">#{position},</if>
            <if test="resignationReason != null">#{resignationReason},</if>
            <if test="workIntroduction != null">#{workIntroduction},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into tb_work_experience (personal_id, company_name, position, resignation_reason, work_Introduction,
        start_time, end_time, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.personalId}, #{item.companyName}, #{item.position}, #{item.resignationReason},
            #{item.workIntroduction}, #{item.startTime}, #{item.endTime}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateTbWorkExperience" parameterType="TbWorkExperience">
        update tb_work_experience
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="resignationReason != null">resignation_reason = #{resignationReason},</if>
            <if test="workIntroduction != null">work_Introduction = #{workIntroduction},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWorkExperienceById" parameterType="Long">
        delete from tb_work_experience where id = #{id}
    </delete>

    <delete id="deleteTbWorkExperienceByIds" parameterType="String">
        delete from tb_work_experience where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>