@echo off
echo.
echo [信息] 启动若依单体应用
echo.

cd ruoyi-admin

echo [信息] 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java环境未配置，请先安装Java 8+
    pause
    exit /b 1
)

echo.
echo [信息] 启动应用...
echo [提示] 如果启动失败，请检查数据库和Redis连接配置
echo.

java -Dspring.profiles.active=dev ^
     -Dserver.port=8080 ^
     -Xms512m -Xmx1024m ^
     -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m ^
     -Dfile.encoding=UTF-8 ^
     -cp "target/classes;../ruoyi-common/ruoyi-common-core/target/classes;../ruoyi-common/ruoyi-common-security/target/classes;../ruoyi-common/ruoyi-common-redis/target/classes;../ruoyi-common/ruoyi-common-entity/target/classes;../ruoyi-common/ruoyi-common-swagger/target/classes;../ruoyi-common/ruoyi-common-datascope/target/classes;../ruoyi-common/ruoyi-common-datasource/target/classes;../ruoyi-common/ruoyi-common-log/target/classes;../ruoyi-auth/target/classes" ^
     com.ruoyi.RuoYiApplication

echo.
echo [信息] 应用已停止
pause
