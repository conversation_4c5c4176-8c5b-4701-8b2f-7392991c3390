# Tomcat
server:
  port: 9218

# Spring
spring:
  main:
    # 允许class之间循环调用
    allow-circular-references: true
  application:
    # 应用名称
    name: ruoyi-knowledgebase
  cloud:
    nacos:
      discovery:
#        ip: *************
        # 服务注册地址
        namespace: 10f65034-51d6-4eb8-8bb8-b057cf130584
        server-addr: localhost:8848
      config:
        # 配置中心地址
        namespace: 10f65034-51d6-4eb8-8bb8-b057cf130584
        server-addr: localhost:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
