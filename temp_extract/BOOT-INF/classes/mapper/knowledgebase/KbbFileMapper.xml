<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbFileMapper">
    
    <resultMap type="KbbFile" id="KbbFileResult">
        <result property="id"    column="id"    />
        <result property="kbId"    column="kb_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="documentId"    column="document_id"    />
        <result property="segmentCount"    column="segment_count"    />
        <result property="status"    column="status"    />
        <result property="isAvailable"    column="is_available"    />
        <result property="isDownload"    column="is_download"    />
        <result property="operator"    column="operator"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKbbFileVo">
        select id, kb_id, file_name, file_url, file_type, file_size, document_id, segment_count, status, is_available, is_download, operator, upload_time, create_by, create_time, update_by, update_time, remark from kbb_file
    </sql>

    <select id="selectKbbFileList" parameterType="KbbFile" resultMap="KbbFileResult">
        <include refid="selectKbbFileVo"/>
        <where>  
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="documentId != null  and documentId != ''"> and document_id = #{documentId}</if>
            <if test="segmentCount != null "> and segment_count = #{segmentCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isAvailable != null  and isAvailable != ''"> and is_available = #{isAvailable}</if>
            <if test="isDownload != null  and isDownload != ''"> and is_download = #{isDownload}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
        </where>
    </select>
    
    <select id="selectKbbFileById" parameterType="Long" resultMap="KbbFileResult">
        <include refid="selectKbbFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKbbFile" parameterType="KbbFile" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kbId != null">kb_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="documentId != null">document_id,</if>
            <if test="segmentCount != null">segment_count,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="isDownload != null">is_download,</if>
            <if test="operator != null">operator,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kbId != null">#{kbId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="segmentCount != null">#{segmentCount},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="isDownload != null">#{isDownload},</if>
            <if test="operator != null">#{operator},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKbbFile" parameterType="KbbFile">
        update kbb_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="segmentCount != null">segment_count = #{segmentCount},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="isDownload != null">is_download = #{isDownload},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbFileById" parameterType="Long">
        delete from kbb_file where id = #{id}
    </delete>

    <delete id="deleteKbbFileByIds" parameterType="String">
        delete from kbb_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询知识库文件总数 -->
    <select id="selectKbbFileCount" parameterType="KbbFile" resultType="int">
        select count(1) from kbb_file
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="documentId != null  and documentId != ''"> and document_id = #{documentId}</if>
            <if test="segmentCount != null "> and segment_count = #{segmentCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isAvailable != null  and isAvailable != ''"> and is_available = #{isAvailable}</if>
            <if test="isDownload != null  and isDownload != ''"> and is_download = #{isDownload}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
        </where>
    </select>

    <!-- 根据ID列表查询文件列表 -->
    <select id="selectKbbFileListByIds" parameterType="Long[]" resultMap="KbbFileResult">
        <include refid="selectKbbFileVo"/>
        where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>