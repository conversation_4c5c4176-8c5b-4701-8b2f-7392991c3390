<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbKnowledgeTagMapper">
    
    <resultMap type="KbbKnowledgeTag" id="KbbKnowledgeTagResult">
        <result property="id"    column="id"    />
        <result property="kbId"    column="kb_id"    />
        <result property="tagId"    column="tag_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectKbbKnowledgeTagVo">
        select id, kb_id, tag_id, create_time from kbb_knowledge_tag
    </sql>

    <select id="selectKbbKnowledgeTagList" parameterType="KbbKnowledgeTag" resultMap="KbbKnowledgeTagResult">
        <include refid="selectKbbKnowledgeTagVo"/>
        <where>  
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
        </where>
    </select>
    
    <select id="selectKbbKnowledgeTagById" parameterType="Long" resultMap="KbbKnowledgeTagResult">
        <include refid="selectKbbKnowledgeTagVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKbbKnowledgeTag" parameterType="KbbKnowledgeTag" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_knowledge_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kbId != null">kb_id,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kbId != null">#{kbId},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateKbbKnowledgeTag" parameterType="KbbKnowledgeTag">
        update kbb_knowledge_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbKnowledgeTagById" parameterType="Long">
        delete from kbb_knowledge_tag where id = #{id}
    </delete>

    <delete id="deleteKbbKnowledgeTagByIds" parameterType="String">
        delete from kbb_knowledge_tag where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteKbbKnowledgeTagByKbId" parameterType="Long">
        delete from kbb_knowledge_tag where kb_id = #{kbId}
    </delete>

    <delete id="deleteKbbKnowledgeTagByTagId" parameterType="Long">
        delete from kbb_knowledge_tag where tag_id = #{tagId}
    </delete>
</mapper>