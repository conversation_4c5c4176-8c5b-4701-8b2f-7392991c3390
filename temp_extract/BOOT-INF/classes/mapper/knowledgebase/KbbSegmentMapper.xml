<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledgebase.mapper.KbbSegmentMapper">
    
    <resultMap type="KbbSegment" id="KbbSegmentResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="segmentIndex"    column="segment_index"    />
        <result property="segmentContent"    column="segment_content"    />
        <result property="wordCount"    column="word_count"    />
        <result property="keywords"    column="keywords"    />
        <result property="isAvailable"    column="is_available"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKbbSegmentVo">
        select id, file_id, segment_index, segment_content, word_count, keywords, is_available, create_by, create_time, update_by, update_time from kbb_segment
    </sql>

    <select id="selectKbbSegmentList" parameterType="KbbSegment" resultMap="KbbSegmentResult">
        <include refid="selectKbbSegmentVo"/>
        <where>  
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="segmentIndex != null "> and segment_index = #{segmentIndex}</if>
            <if test="segmentContent != null  and segmentContent != ''"> and segment_content = #{segmentContent}</if>
            <if test="wordCount != null "> and word_count = #{wordCount}</if>
            <if test="keywords != null  and keywords != ''"> and keywords = #{keywords}</if>
            <if test="isAvailable != null  and isAvailable != ''"> and is_available = #{isAvailable}</if>
        </where>
    </select>
    
    <select id="selectKbbSegmentById" parameterType="Long" resultMap="KbbSegmentResult">
        <include refid="selectKbbSegmentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKbbSegment" parameterType="KbbSegment" useGeneratedKeys="true" keyProperty="id">
        insert into kbb_segment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="segmentIndex != null">segment_index,</if>
            <if test="segmentContent != null and segmentContent != ''">segment_content,</if>
            <if test="wordCount != null">word_count,</if>
            <if test="keywords != null">keywords,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="segmentIndex != null">#{segmentIndex},</if>
            <if test="segmentContent != null and segmentContent != ''">#{segmentContent},</if>
            <if test="wordCount != null">#{wordCount},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKbbSegment" parameterType="KbbSegment">
        update kbb_segment
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="segmentIndex != null">segment_index = #{segmentIndex},</if>
            <if test="segmentContent != null and segmentContent != ''">segment_content = #{segmentContent},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKbbSegmentById" parameterType="Long">
        delete from kbb_segment where id = #{id}
    </delete>

    <delete id="deleteKbbSegmentByIds" parameterType="String">
        delete from kbb_segment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>